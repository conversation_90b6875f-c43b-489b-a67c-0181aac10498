# =====================================
# US1 Dev Instance Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  alias_parent_domain_to_self = false

  admin_api_config = {
    secret_name = "projects/************/secrets/ict-dev-auth0-management-client-id/versions/latest"
  }

  api_security_roles = []

  # note: this path is relative to the project and resources directories, not this file
  config_api_config_file_path = "${get_repo_root()}/services/api/apps/api/ict-config-api/src/data/app-config-dev.json"

  data_explorer_config = {
    secret_name = "projects/************/secrets/ict-dev-aiml-invocation-url/versions/latest"
    url         = "https://opendataqnadev-v1-ai.ops.dematic.dev"
  }

  enforce_unique_naming  = false
  instance_name          = "us1"
  instance_friendly_name = "US1"

  simulation_api_key_secret_name = (
    "projects/************/secrets/ict-dev-simulation-api-key/versions/latest"
  )

  subnet_cidr_range = "**********/28"

  # note: this path is relative to the project and resources directories, not this file
  tenant_config_file = "${get_repo_root()}/config/tenants/dev.tenants.json"

  ui_app_artifact_folder = "${get_repo_root()}/services/ui/dist"

  use_environment_name_for_instance_name = true
  use_environment_name_in_project_name   = true

  iam_bindings = {
    "roles/resourcemanager.projectIamAdmin" = [
      local.cicd_service_account_principal
    ]
  }
}
