locals {
  project_name = (var.use_environment_name_in_project_name
    ? "${var.global_naming_root}-${var.lz_abbreviation}-${var.instance_name}-${var.environment_name}"
    : "${var.global_naming_root}-${var.lz_abbreviation}-${var.instance_name}"
  )

  common_resource_labels = merge(
    var.global_resource_labels,
    var.lz_resource_labels,
    {
      environment = var.environment_name
      instance    = "${var.instance_name}-${var.environment_name}"
    }
  )

  project_apis = concat(
    var.global_project_apis,
    ["prod.n4gcp.neo4j.io"]
  )

  project_labels = merge(
    var.global_project_labels,
    var.lz_project_labels,
    local.common_resource_labels
  )
}
