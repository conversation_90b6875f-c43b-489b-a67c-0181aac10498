module "bigquery-scheduled-queries" {
  source = "../../../../../infrastructure/modules/instance/bigquery-scheduled-queries"

  project_id = local.project_id
  region     = var.global_default_region

  common_resource_labels = local.common_resource_labels

  enforce_unique_naming = var.enforce_unique_naming

  environment = var.environment_name

  required_roles = var.bigquery_scheduler_service_account_roles

  scheduled_queries = {
    superior_workstation_daily_performance = {
      display_name           = "Workstation Daily Performance"
      schedule               = "0 * * * *" # Every hour
      destination_dataset_id = "${local.tenant_dataset}"
      destination_table_name = "superior_workstation_daily_performance"
      write_disposition      = "WRITE_TRUNCATE"
      query                  = file("../../../../../services/api/sql/scheduled_queries/workstations/superior_workstation_daily_performance.sql")
    }
  }
}
