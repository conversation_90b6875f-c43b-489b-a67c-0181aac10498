locals {
  scheduled_queries_base_path = "../../../../../services/api/sql/scheduled_queries"
}

module "bigquery-scheduled-queries" {
  source = "../../../../../infrastructure/modules/custom/superior_uniform/bigquery-scheduled-queries"

  project_id = local.project_id
  region     = var.global_default_region

  common_resource_labels = local.common_resource_labels

  enforce_unique_naming = var.enforce_unique_naming

  environment = var.environment_name

  scheduled_queries = {
    platinum_workstation_daily_performance = {
      display_name           = "Platinum Workstation Daily Performance"
      schedule               = "0 * * * *" # Every hour
      destination_dataset_id = "superior_uniform"
      destination_table_name = "platinum_workstation_daily_performance"
      write_disposition      = "WRITE_TRUNCATE"
      query                  = file("${local.scheduled_queries_base_path}/workstations/workstation_daily_performance.sql")
    }
  }
}
