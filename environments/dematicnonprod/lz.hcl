# =====================================
# DematicNonProd Landing Zone Variables
# =====================================

locals {
  cicd_service_account           = "<EMAIL>"
  cicd_service_account_principal = "serviceAccount:${local.cicd_service_account}"
}

inputs = {
  lz_cicd_project_id = "ict-nonprod-cicd"

  lz_folder_id    = "************" # DematicNonProd/ControlTower
  lz_folder_name  = "DematicNonProd"
  lz_short_name   = "nonprod"
  lz_abbreviation = "np"

  lz_agent_ai_api_url            = "https://weather-time-agent-************.us-central1.run.app"
  lz_agent_search_url            = "https://dematic-chat.ops.dematic.dev"
  lz_agent_search_audience_url   = "https://dematic-chat-************.us-central1.run.app"
  lz_gold_questions_url          = "https://ragengine.ops.dematic.dev"
  lz_gold_questions_audience_url = "https://ragengine-************.us-central1.run.app"

  lz_api_security_roles = []

  lz_cloud_run_environment_variables = {
    PARENT_FOLDER = "DematicNonProd"
  }

  lz_cors_origin = "*.dematic.dev"

  lz_default_cloud_run_config = {
    enable_binary_auth = false

    image_tag    = "latest"
    cpu_limit    = 1
    memory_limit = "1024Mi"

    cpu_idle          = true
    startup_cpu_boost = false

    min_instances = 0
    max_instances = 12
  }

  lz_network_flow_log_config = {
    aggregation_interval = "INTERVAL_10_MIN"
    flow_sampling        = 0.7
    metadata             = "INCLUDE_ALL_METADATA"
  }

  lz_project_apis = []

  lz_project_api_identities = []
  lz_project_labels         = {}
  lz_resource_annotations   = {}

  lz_resource_labels = {
    landing_zone = "nonprod"
  }

  lz_simulation_api_url = "https://canvas-gateway-6uhnuu6m.uc.gateway.dev"

  iam_bindings = {
    "roles/bigquery.resourceViewer" = [
      "group:<EMAIL>"
    ]
    "roles/bigquery.admin" = [
      local.cicd_service_account_principal
    ]
    "roles/iam.serviceAccountUser" = [
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.cicd_service_account_principal
    ]
    "roles/secretmanager.viewer" = [
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.cicd_service_account_principal
    ]
  }
}
