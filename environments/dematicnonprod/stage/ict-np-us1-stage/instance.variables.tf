# =====================================
# US1 Stage Instance Variables
# =====================================

variable "admin_api_config" {
  description = <<EOT
    The configuration for the Admin API Cloud Run service. This is used to set environment 
    variables in the Cloud Run container.
  EOT

  type = object({
    secret_name = string
  })
}

variable "alias_parent_domain_to_self" {
  description = <<EOT
    Set to true to alias the parent domain to the instance's domain. For example, this would make 
    `us1.ict.dematic.cloud` resolve to the same IP address as `ict.dematic.cloud`.
  EOT

  type    = bool
  default = false
}

variable "api_security_roles" {
  description = <<EOT
    The security roles to grant to all API Cloud Run Service Accounts in this instance.
  EOT

  type = list(string)
}

variable "config_api_config_file_path" {
  description = <<EOT
    The path to the API configuration file to use for the environment. This is used to set the 
    configuration for the API Cloud Run service.

    NOTE: This path is relative to the project and resources directories, not the location of 
    this file. It will need to navigate one additional directory up to reach the `config/` 
    directory where the API configuration files are stored.
  EOT

  type = string
}

variable "data_explorer_config" {
  description = <<EOT
    The Data Explorer configuration for this instance of Control Tower. These are applied as 
    environment variables in the Cloud Run container.

    Note: the tables are currently defined in the global configuration.
  EOT

  type = object({
    secret_name = string
    url         = string
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_friendly_name" {
  description = <<EOT
    A friendly name for the instance, used for display purposes with the environment_name. 
    E.g., `US1`.
  EOT

  type = string
}

variable "instance_name" {
  description = <<EOT
    The name of the instance being that the resources are being deployed to. This value will be 
    combined with the `environment_name` to create the unique identier for this instance. E.g., 
    `us1`.
  EOT

  type = string
}

variable "simulation_api_key_secret_name" {
  description = "The name of the secret containing the API key for the simulation service."
  type        = string
}

variable "subnet_cidr_range" {
  description = "The IP CIDR range to assign to the instance's subnet in the shared VPC."
  type        = string
}

variable "tenant_config_file" {
  description = <<EOT
    The path to the tenant configuration file to use for the environment.

    NOTE: This path is relative to the project and resources directories, not the location of 
    this file. It will need to navigate one additional directory up to reach the `config/` 
    directory where the tenant configuration files are stored.
  EOT

  type = string
}

variable "ui_app_artifact_folder" {
  description = <<EOT
    The path to the UI application build artifact folder (dist) to use for the environment.
  EOT

  type = string
}
variable "use_environment_name_for_instance_name" {
  description = <<EOT
    Set this to true to use the `environment_name` instead of the `instance_name` for the 
    instance's name. For example, in the US1 Dev instance, this would result in the DNS name 
    `dev.ict.dematic.dev`, and the resource naming prefix would be `dev`.

    Note: This will not apply to the project name, because the project name has the option of 
    including both values.
  EOT

  type = bool
}

variable "use_environment_name_in_project_name" {
  description = <<EOT
    Set this to true to use the `environment_name` with the `instance_name` in the project name.
    For example, in the US1 Dev instance, this would result in the project name `ict-np-us1-dev`. 
    In prod, we'd set this to false, so the project name would be `ict-np-us1`.
  EOT

  type = bool
}
