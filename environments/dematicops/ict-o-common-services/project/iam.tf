module "project_permissions" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "8.0.0"

  projects = [module.project.project_id]
  mode     = "additive"
  bindings = var.iam_bindings
}

# Bucket folder-scoped permissions for ict-o-tfstate bucket
# Gives devops full access to dematicnonprod/ folder and subfolders only
# Grant full object access to dematicnonprod/ folder in ict-o-tfstate bucket
module "project_permissions_dematicnonprod_gcs_folder_access" {
  source   = "terraform-google-modules/iam/google//modules/projects_iam"
  version  = "~> 8.0"

  projects = [module.project.project_id]
  mode     = "additive"

  bindings = {}

  conditional_bindings = [
    {
      role        = "roles/storage.admin"
      members     = ["group:<EMAIL>"]
      title       = "DematicNonProd GCS folder access only"
      description = "Allow full access only to dematicnonprod/ folder and all subfolders"
      expression = "resource.name.startsWith('projects/_/buckets/ict-o-tfstate/objects/dematicnonprod/')"
    }
  ]
}