module "scaffolding" {
  source = "../../../../../infrastructure/modules/instance/scaffolding"

  project_id     = local.project_id
  project_number = local.project_number


  artifact_registry_project_id = data.terraform_remote_state.common.outputs.project_id
  common_resource_labels       = local.common_resource_labels
  enforce_unique_naming        = var.enforce_unique_naming
  instance_display_name        = local.instance_display_name
  instance_prefix              = local.instance_prefix
  naming_root                  = var.global_naming_root
  region                       = var.global_default_region
  vpc_access_connector_config  = var.global_default_vpc_access_connector_config

  access_logs_bucket_config = {
    is_retention_policy_locked = false
    location                   = "US"
    retention_period_seconds   = 30 * 24 * 60 * 60 # 30 days in seconds
    storage_class              = "STANDARD"
  }

  network_config = {
    enable_flow_logs = true
    flow_log_config  = var.lz_network_flow_log_config

    ip_addresses = {
      google_health_check_source_ip_ranges = var.google_health_check_source_ip_ranges
      google_iap_source_ip_ranges          = var.google_iap_source_ip_ranges
      private_service_connect_ip_address   = var.global_default_ip_ranges.private_service_connect
    }
  }

  shared_vpc_config = {
    host_network_name = var.lz_shared_vpc_network.network_name
    host_project_id   = var.lz_shared_vpc_network.project_id
    subnet_cidr_range = var.subnet_cidr_range
  }
}

module "postgresql" {
  source = "../../../../../infrastructure/modules/instance/postgresql"

  project_id = local.project_id
  region     = var.global_default_region
  zone       = var.global_default_zone

  access_logs_bucket          = module.scaffolding.access_logs_bucket_name
  bucket_location             = "us"
  common_resource_labels      = local.common_resource_labels
  database_username           = var.default_postgresql_username
  enforce_unique_naming       = var.enforce_unique_naming
  environment                 = var.environment_name
  environment_variables       = local.cloud_run_environment_variables
  instance_prefix             = local.instance_prefix
  naming_root                 = var.global_naming_root
  vpc_access_connector_config = local.default_vpc_access_connector_config

  artifact_registry_repository = data.terraform_remote_state.common.outputs.docker_repository

  # databases = var.postgres_databases
  databases = concat([
    for tenant in local.tenant_config : tenant["metadata"]["dataset"]
    ],
    ["dematic_software"]
  )


  migration_container_image = {
    image_name = "ict-typeorm-migrations-cr"
    image_tag  = "latest"
  }

  migration_cloud_run_config = var.lz_default_cloud_run_config

  network_config = {
    enable_flow_logs  = true
    network_self_link = module.scaffolding.network_self_link
    subnet_ip_range   = var.global_default_ip_ranges.typeorm_migrations

    flow_log_config = var.lz_network_flow_log_config
  }

  depends_on = [module.scaffolding]
}

module "redis-cache" {
  source = "../../../../../infrastructure/modules/instance/redis"

  project_id     = local.project_id
  region         = var.global_default_region
  zone           = var.global_default_zone
  alternate_zone = var.global_default_alternate_zone

  common_resource_labels = local.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = local.instance_prefix
  network_self_link      = module.scaffolding.network_self_link

  depends_on = [module.scaffolding]
}
