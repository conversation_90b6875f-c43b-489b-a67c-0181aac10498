module "neo4j" {
  source = "../../../../../infrastructure/modules/instance/neo4j"

  common_resource_labels = local.common_resource_labels
  project_id             = local.project_id

  neo4j_aura_credentials = {
    client_id     = var.neo4j_aura_credentials.client_id
    client_secret = var.neo4j_aura_credentials.client_secret
    tenant_id     = var.neo4j_aura_credentials.tenant_id
  }

  neo4j_database_names = [
    "superior_uniform_eudoraar",
    "acehardware_wilmertx"
  ]
}
