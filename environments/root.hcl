# =====================================
# Root terragrunt configuration
# =====================================
locals {
  path_relative_to_include = path_relative_to_include()
  state_file_bucket        = "ict-o-tfstate"

  common_services_state_prefix = "dematicops/ict-o-common-services/resources"

  is_common_services = (
    endswith(local.path_relative_to_include, "common-services/project") ||
    endswith(local.path_relative_to_include, "common-services/resources")
  )
  is_tableau = (
    endswith(local.path_relative_to_include, "tableau")
  )

  # Determine service account based on landing zone
  # NOTE: Not yet impemented because we haven't setup service account impersonation yet.
  service_account = (
    startswith(local.path_relative_to_include, "dematicops") ? "<EMAIL>" :
    startswith(local.path_relative_to_include, "dematicnonprod") ? "<EMAIL>" :
    startswith(local.path_relative_to_include, "dematicprod") ? "<EMAIL>" :
    "<EMAIL>" # default fallback
  )
}

terraform {
  # before_hook "terragrunt_clean_cache" {
  #   commands = ["init"]
  #   execute  = ["${get_repo_root()}/scripts/clean-terragrunt-terraform-cache.sh"]
  # }

  before_hook "terragrunt_hcl_format" {
    commands = ["init", "plan"]
    execute  = ["${get_repo_root()}/scripts/format-terragrunt-hcl.sh"]
  }

  extra_arguments "disable_lock_for_plan" {
    commands  = ["plan"]
    arguments = ["-lock=false"]
  }

  extra_arguments "compact_warnings" {
    commands  = ["plan", "apply"]
    arguments = ["-compact-warnings"]
  }

  # Increase parallelism for faster execution
  extra_arguments "performance" {
    commands  = ["plan", "apply", "destroy"]
    arguments = ["-parallelism=20"]
  }
}

# The remote_state block can create a storage bucket, while the generate "backend" block cannot.
remote_state {
  backend = "gcs"
  config = {
    project  = "ict-o-common-services-kfqd"
    bucket   = local.state_file_bucket
    location = "US"
    prefix   = "${local.path_relative_to_include}"
  }
}

generate "backend" {
  path      = "backend.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  backend "gcs" {
    bucket = "${local.state_file_bucket}"
    prefix = "${local.path_relative_to_include}"
  }
}
EOF
}

generate "dependencies" {
  path      = "dependencies.tf"
  if_exists = "overwrite"
  contents  = <<EOF
%{if endswith(local.path_relative_to_include, "/project") == false && local.is_tableau == false~}
data "terraform_remote_state" "project" {
  backend = "gcs"

  config = {
    bucket = "${local.state_file_bucket}"
    prefix = "${trimsuffix(local.path_relative_to_include, "/resources")}/project"
  }
}
%{endif~}
%{if local.is_common_services == false~}
data "terraform_remote_state" "common" {
  backend = "gcs"

  config = {
    bucket = "${local.state_file_bucket}"
    prefix = "${local.common_services_state_prefix}"
  }
}
%{endif~}
EOF
}

# terraform {
#   extra_arguments "env_file" {
#     commands = [ ...  ]
#     required_var_files = ["${get_parent_terragrunt_dir()}/env/${get_env("ENV")}/env.tfvars"]
#   }
# }
