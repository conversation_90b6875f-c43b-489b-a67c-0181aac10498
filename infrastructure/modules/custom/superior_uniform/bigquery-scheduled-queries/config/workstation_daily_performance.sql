-- Superior Uniform Workstation Daily Performance Query
-- This query aggregates workstation performance metrics for the superior_uniform tenant
-- Runs hourly to keep performance data fresh for dashboards

SELECT 
  workstation_id,
  DATE(performance_timestamp) as performance_date,
  AVG(throughput_rate) as avg_throughput_rate,
  MAX(throughput_rate) as max_throughput_rate,
  MIN(throughput_rate) as min_throughput_rate,
  COUNT(*) as total_records,
  SUM(items_processed) as total_items_processed,
  AVG(efficiency_percentage) as avg_efficiency,
  CURRENT_TIMESTAMP() as last_updated
FROM `ict-p-tableau.superior_uniform_tableau_views.eudora_ar_report_workstation_daily_performance_view`
WHERE DATE(performance_timestamp) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAYS)
GROUP BY 
  workstation_id,
  DATE(performance_timestamp)
ORDER BY 
  performance_date DESC,
  workstation_id ASC
