locals {
  resource_namespace = "bigquery-scheduler"
  unique_root_name   = "${var.environment}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # Required IAM roles for BigQuery scheduled queries
  required_roles = [
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
    "roles/bigquery.dataTransfer.editor"
  ]
}
