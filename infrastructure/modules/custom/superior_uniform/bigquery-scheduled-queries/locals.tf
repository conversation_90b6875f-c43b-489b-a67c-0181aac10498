locals {
  resource_namespace = "bigquery-scheduler"
  unique_root_name   = "${var.environment}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # Required IAM roles for BigQuery scheduled queries
  required_roles = [
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
    "roles/bigquery.dataTransfer.editor"
  ]

  # Scheduled queries configuration for superior_uniform tenant
  scheduled_queries = {
    platinum_workstation_daily_performance = {
      display_name           = "Platinum Workstation Daily Performance"
      schedule               = "0 * * * *" # Every hour
      destination_dataset_id = "superior_uniform"
      destination_table_name = "platinum_workstation_daily_performance"
      write_disposition      = "WRITE_TRUNCATE"
      query                  = file("${path.module}/config/workstation_daily_performance.sql")
    }
  }
}
