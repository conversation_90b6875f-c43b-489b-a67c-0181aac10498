# Service account and IAM roles
resource "google_service_account" "bigquery_scheduler" {
  project      = var.project_id
  account_id   = local.root_name
  display_name = "Service account for BigQuery scheduled queries"
}

resource "google_project_iam_member" "bigquery_scheduler" {
  project  = var.project_id
  for_each = toset(local.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.bigquery_scheduler.email}"
}

# BigQuery scheduled queries
resource "google_bigquery_data_transfer_config" "scheduled_queries" {
  for_each = var.scheduled_queries

  project                = var.project_id
  display_name           = each.value.display_name
  location               = var.region
  data_source_id         = "scheduled_query"
  schedule               = each.value.schedule
  destination_dataset_id = each.value.destination_dataset_id

  params = {
    destination_table_name_template = each.value.destination_table_name
    write_disposition               = each.value.write_disposition
    query                           = each.value.query
  }
}
