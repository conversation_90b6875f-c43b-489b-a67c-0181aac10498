# Service account and IAM roles
resource "google_service_account" "bigquery_scheduler" {
  project      = var.project_id
  account_id   = local.root_name
  display_name = "Service account for BigQuery scheduled queries"
}

resource "google_project_iam_member" "bigquery_scheduler" {
  project  = var.project_id
  for_each = toset(local.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.bigquery_scheduler.email}"
}

# BigQuery scheduled query for platinum workstation daily performance
resource "google_bigquery_data_transfer_config" "platinum_workstation_daily_performance" {
  project                = var.project_id
  display_name           = "Platinum Workstation Daily Performance"
  location               = var.region
  data_source_id         = "scheduled_query"
  schedule               = "0 * * * *" # Every hour
  destination_dataset_id = "superior_uniform"

  params = {
    destination_table_name_template = "platinum_workstation_daily_performance"
    write_disposition               = "WRITE_TRUNCATE"
    query                           = file("${path.module}/config/workstation_daily_performance.sql")
  }
}

resource "google_project_service" "bigquery" {
  project = var.project_id
  service = "bigquery.googleapis.com"

  disable_on_destroy = false
}
