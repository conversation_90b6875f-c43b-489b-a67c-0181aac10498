# Service account and IAM roles
resource "google_service_account" "bigquery_scheduler" {
  project      = var.project_id
  account_id   = local.root_name
  display_name = "Service account for BigQuery scheduled queries"
}

resource "google_project_iam_member" "bigquery_scheduler" {
  project  = var.project_id
  for_each = toset(local.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.bigquery_scheduler.email}"
}

# BigQuery scheduled query for platinum workstation daily performance
resource "google_bigquery_data_transfer_config" "platinum_workstation_daily_performance" {
  project                = var.project_id
  display_name           = "Platinum Workstation Daily Performance"
  location               = "US" # Multi-region to access datasets in different locations
  data_source_id         = "scheduled_query"
  schedule               = "every 1 hours" # Every hour
  destination_dataset_id = "superior_uniform"
  service_account_name   = google_service_account.bigquery_scheduler.email

  params = {
    destination_table_name_template = "platinum_workstation_daily_performance"
    write_disposition               = "WRITE_TRUNCATE"
    query                           = file("${path.module}/config/workstation_daily_performance.sql")
  }

  depends_on = [
    google_project_service.bigquery_data_transfer,
    google_bigquery_dataset.superior_uniform
  ]
}

# Enable required APIs
resource "google_project_service" "bigquery" {
  project = var.project_id
  service = "bigquery.googleapis.com"

  disable_on_destroy = false
}

resource "google_project_service" "bigquery_data_transfer" {
  project = var.project_id
  service = "bigquerydatatransfer.googleapis.com"

  disable_on_destroy = false
}

# Create the BigQuery dataset for superior_uniform data
resource "google_bigquery_dataset" "superior_uniform" {
  project    = var.project_id
  dataset_id = "superior_uniform"
  location   = var.region

  friendly_name = "Superior Uniform Data"
  description   = "Dataset containing processed data for Superior Uniform tenant"

  labels = var.common_resource_labels

  # Allow scheduled query service account to write to this dataset
  access {
    role          = "WRITER"
    user_by_email = google_service_account.bigquery_scheduler.email
  }

  # Allow project viewers to read the dataset
  access {
    role          = "READER"
    special_group = "projectReaders"
  }

  # Allow project owners full access
  access {
    role          = "OWNER"
    special_group = "projectOwners"
  }

  depends_on = [google_project_service.bigquery]
}

