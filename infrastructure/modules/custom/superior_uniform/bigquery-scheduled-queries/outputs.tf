output "scheduled_query_config" {
  description = "BigQuery Data Transfer configuration for platinum workstation daily performance."
  value = {
    id           = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.id
    name         = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.name
    display_name = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.display_name
    location     = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.location
    schedule     = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.schedule
  }
}

output "scheduled_query_id" {
  description = "BigQuery Data Transfer configuration ID for platinum workstation daily performance."
  value       = google_bigquery_data_transfer_config.platinum_workstation_daily_performance.id
}

output "service_account_email" {
  description = "Email of the BigQuery scheduler service account."
  value       = google_service_account.bigquery_scheduler.email
}
