variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy BigQuery scheduled queries in."
  type        = string
}

variable "enforce_unique_naming" {
  description = <<EOF
    Set this to true to use the provided 'environment' value to create a unique prefix for all
    resources in the module. This is required to allow multiple instances of the module to be
    deployed in the same GCP project.
  EOF
  type        = bool
}

variable "environment" {
  description = <<EOF
    The name of the environment that the resources are being deployed to. This value will be 
    combined with the resource namespace to create a unique prefix for any resources requiring a 
    globally-unique identifier.
  EOF
  type        = string
}

variable "scheduled_queries" {
  description = "Map of scheduled queries to create"
  type = map(object({
    display_name           = string
    schedule               = string
    destination_dataset_id = string
    destination_table_name = string
    write_disposition      = string
    query                  = string
  }))
  default = {}
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
  default     = {}
}
