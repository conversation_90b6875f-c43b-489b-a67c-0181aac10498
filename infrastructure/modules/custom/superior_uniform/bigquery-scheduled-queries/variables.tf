variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
  default     = {}
}

variable "enforce_unique_naming" {
  description = <<EOF
    Set this to true to use the provided 'environment' value to create a unique prefix for all
    resources in the module. This is required to allow multiple instances of the module to be
    deployed in the same GCP project.
  EOF
  type        = bool
}

variable "environment" {
  description = <<EOF
    The name of the environment that the resources are being deployed to. This value will be
    combined with the resource namespace to create a unique prefix for any resources requiring a
    globally-unique identifier.
  EOF
  type        = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy BigQuery scheduled queries in."
  type        = string
}
