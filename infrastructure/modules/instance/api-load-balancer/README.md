<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# API Load Balancer Terraform Module

WIP

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the the API load balancer. More documentation to come, but it is likely that this module will be combined with the UI load balancer to create a single load balancer per environment.

## Usage

Basic usage of this module is as follows:

```hcl
module "api-load-balancer" {
  source  = "gitlab.com/dematic/control-tower-api-load-balancer/google"
  version = "~> 0.1.0"

  # Required variables
  backend_monitoring_config  = var.backend_monitoring_config
  backend_services           = var.backend_services
  common_resource_labels     = var.common_resource_labels
  default_backend_service_id = var.default_backend_service_id
  dns_config                 = var.dns_config
  enforce_unique_naming      = var.enforce_unique_naming
  instance_display_name      = var.instance_display_name
  instance_prefix            = var.instance_prefix
  load_balancing_scheme      = var.load_balancing_scheme
  minimum_tls_version        = var.minimum_tls_version
  notification_email         = var.notification_email
  project_id                 = var.project_id
  region                     = var.region
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| backend-service-monitors | ./backend-monitor | n/a |
| ssl\_certificate | ./ssl_certificate | n/a |

## Resources

| Name | Type |
|------|------|
| [google_compute_global_address.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_address) | resource |
| [google_compute_global_forwarding_rule.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_forwarding_rule) | resource |
| [google_compute_ssl_policy.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_ssl_policy) | resource |
| [google_compute_target_https_proxy.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_target_https_proxy) | resource |
| [google_compute_url_map.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map) | resource |
| [google_dns_record_set.a_record](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_dns_record_set.cname_record](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_monitoring_notification_channel.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_notification_channel) | resource |
| [google_dns_managed_zone.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| backend\_monitoring\_config | The configuration for the monitors that will be created for each backend service that is      registered with the load balancer's URL map, used with the backend-monitor sub-module to create      uptime checks and optional alerts. For details about the parameters, see the backend-monitor      sub-module. | ```object({ monitoring = object({ http_check_config = object({ port = number use_ssl = bool validate_ssl = bool }) basic_uptime_check_config = object({ period = string timeout = string }) full_uptime_check_config = object({ period = string timeout = string }) # if not specified, the uptime check will only run from the primary region regions = optional(list(string)) }) alerting = object({ enabled = bool condition_threshold = optional(object({ comparison = string duration = string trigger_count = number aggregation_alignment_period = string })) }) })``` | n/a | yes |
| backend\_services | The backend services that should be registered with the load balancer's URL map. The key is the      service name, and the value is a map that contains the properties needed to integrate each      service with the load balancer. For details about the parameters, see the `backend-service` and      `storage-enabled-backend-service` sub-modules. | ```map(object({ backend_service_friendly_name = string backend_service_id = string backend_service_name = string basic_health_check_path = string full_health_check_path = string url_map_path = string }))``` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| default\_backend\_service\_id | The ID of the backend service to register as the default route on the load balancer. This is      used when no other backend service matches the request. | `string` | n/a | yes |
| dns\_config | The DNS configuration for the load balancer. | ```object({ a_record_ttl = number cname_record_ttl = number project_id = string zone_name = string })``` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| instance\_display\_name | The human-readable name of the instance that the resources are being deployed to. | `string` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| load\_balancing\_scheme | The load balancing scheme to use for the load balancer. For an external load balancer, this can      be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either      `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for     production use, as it provides better performance and reliability. | `string` | n/a | yes |
| minimum\_tls\_version | The minimum TLS version to use for the load balancer. This value is used to create the      SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported. | `string` | n/a | yes |
| notification\_email | The email address to send notifications to. This is used for alerting when the monitored      resource is down. | `string` | n/a | yes |
| project\_id | The ID of the GCP project to deploy the load balancer in. | `string` | n/a | yes |
| region | The region to deploy regional resources in. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| backend\_ip\_address | The IP address for the API load balancer. |
| dns\_names | The DNS name(s) registered for the API load balancer. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  #     The configuration for the monitors that will be created for each backend service that is
  #     registered with the load balancer's URL map, used with the backend-monitor sub-module to create
  #     uptime checks and optional alerts. For details about the parameters, see the backend-monitor
  #     sub-module.
  #
  backend_monitoring_config = ""
  
  #     The backend services that should be registered with the load balancer's URL map. The key is the
  #     service name, and the value is a map that contains the properties needed to integrate each
  #     service with the load balancer. For details about the parameters, see the `backend-service` and
  #     `storage-enabled-backend-service` sub-modules.
  #
  backend_services = ""
  
  #     The common labels that should be applied to all resources that can be labeled.
  #
  common_resource_labels = ""
  
  #     The ID of the backend service to register as the default route on the load balancer. This is
  #     used when no other backend service matches the request.
  #
  default_backend_service_id = ""
  
  # The DNS configuration for the load balancer.
  dns_config = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The human-readable name of the instance that the resources are being deployed to.
  #
  instance_display_name = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The load balancing scheme to use for the load balancer. For an external load balancer, this can
  #     be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either
  #     `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
  #     production use, as it provides better performance and reliability.
  #
  load_balancing_scheme = ""
  
  #     The minimum TLS version to use for the load balancer. This value is used to create the
  #     SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported.
  #
  minimum_tls_version = ""
  
  #     The email address to send notifications to. This is used for alerting when the monitored
  #     resource is down.
  #
  notification_email = ""
  
  # The ID of the GCP project to deploy the load balancer in.
  project_id = ""
  
  # The region to deploy regional resources in.
  region = ""
}
```

<!-- END_TFVARS_DOCS-->
