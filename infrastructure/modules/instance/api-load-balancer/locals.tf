locals {
  resource_namespace = "api"
  unique_root_name   = "${var.instance_prefix}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # DNS
  cname_record_dns_name = "${local.resource_namespace}.${data.google_dns_managed_zone.main.dns_name}"
  a_record_dns_name     = "run-${local.cname_record_dns_name}"

  # monitoring
  notification_channel_name = "API Monitoring Notification Channel - ${var.instance_display_name}"
}
