variable "backend_monitoring_config" {
  description = <<EOT
    The configuration for the monitors that will be created for each backend service that is 
    registered with the load balancer's URL map, used with the backend-monitor sub-module to create 
    uptime checks and optional alerts. For details about the parameters, see the backend-monitor 
    sub-module.
  EOT

  type = object({
    monitoring = object({
      http_check_config = object({
        port         = number
        use_ssl      = bool
        validate_ssl = bool
      })

      basic_uptime_check_config = object({
        period  = string
        timeout = string
      })

      full_uptime_check_config = object({
        period  = string
        timeout = string
      })

      # if not specified, the uptime check will only run from the primary region
      regions = optional(list(string))
    })

    alerting = object({
      enabled = bool

      condition_threshold = optional(object({
        comparison    = string
        duration      = string
        trigger_count = number

        aggregation_alignment_period = string
      }))
    })
  })
}

variable "backend_services" {
  description = <<EOT
    The backend services that should be registered with the load balancer's URL map. The key is the 
    service name, and the value is a map that contains the properties needed to integrate each 
    service with the load balancer. For details about the parameters, see the `backend-service` and 
    `storage-enabled-backend-service` sub-modules.
  EOT

  type = map(object({
    backend_service_friendly_name = string
    backend_service_id            = string
    backend_service_name          = string
    basic_health_check_path       = string
    full_health_check_path        = string
    url_map_path                  = string
  }))
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "default_backend_service_id" {
  description = <<EOT
    The ID of the backend service to register as the default route on the load balancer. This is 
    used when no other backend service matches the request.
  EOT

  type = string
}

variable "dns_config" {
  description = "The DNS configuration for the load balancer."
  type = object({
    a_record_ttl     = number
    cname_record_ttl = number
    project_id       = string
    zone_name        = string
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "load_balancing_scheme" {
  description = <<EOT
    The load balancing scheme to use for the load balancer. For an external load balancer, this can 
    be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either 
    `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
    production use, as it provides better performance and reliability.
  EOT

  type = string

  validation {
    condition = contains(
      ["EXTERNAL", "EXTERNAL_MANAGED", "INTERNAL", "INTERNAL_MANAGED"],
      var.load_balancing_scheme
    )

    error_message = <<EOT
      The load balancing scheme must be one of: EXTERNAL, EXTERNAL_MANAGED, INTERNAL, 
      INTERNAL_MANAGED."
    EOT
  }
}

variable "minimum_tls_version" {
  description = <<EOT
    The minimum TLS version to use for the load balancer. This value is used to create the 
    SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported.
  EOT

  type = string

  validation {
    condition     = contains(["TLS_1_2", "TLS_1_3"], var.minimum_tls_version)
    error_message = "The minimum TLS version must be one of: TLS_1_2, TLS_1_3."
  }
}

variable "notification_email" {
  description = <<EOT
    The email address to send notifications to. This is used for alerting when the monitored 
    resource is down.
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy the load balancer in."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}
