resource "random_string" "random" {
  length  = 4
  special = false
  upper   = false

  keepers = {
    image_family  = var.host_config.image_family
    image_project = var.host_config.image_project
  }
}

resource "google_compute_region_instance_template" "main" {
  project = var.project_id
  name    = "${local.root_name}-template-${random_string.random.result}"
  region  = var.region

  tags   = var.host_tags
  labels = merge(var.common_resource_labels, var.host_labels)

  instance_description = <<EOF
    Instance template for the Ingition proxy backend for the front-end load balancer.
  EOF

  can_ip_forward = false
  machine_type   = var.host_config.machine_type

  metadata = {
    block-project-ssh-keys = true
    startup-script         = <<EOF
    #! /bin/bash
    sudo apt-get update
    sudo apt-get upgrade -y
    sudo apt-get install -y nginx
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'
    sudo ufw --force enable 
    sudo ufw status numbered
    sudo systemctl start nginx
    sudo systemctl enable nginx

    gcloud storage cp ${local.nginx_config_bucket_object} /etc/nginx/sites-available/default
    gcloud storage cp ${local.ignition_upstreams_bucket_object} /etc/nginx/conf.d/

    sudo /etc/init.d/nginx restart
    EOF
  }

  disk {
    source_image = data.google_compute_image.main.self_link
    auto_delete  = true
    boot         = true
  }

  network_interface {
    subnetwork = google_compute_subnetwork.main.self_link
  }

  service_account {
    email  = google_service_account.main.email
    scopes = local.host_service_account_scopes
  }

  scheduling {
    automatic_restart   = true
    on_host_maintenance = "MIGRATE"
    preemptible         = false
    provisioning_model  = "STANDARD"
  }

  shielded_instance_config {
    enable_secure_boot          = true
    enable_vtpm                 = true
    enable_integrity_monitoring = true
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_region_instance_group_manager" "main" {
  project = var.project_id
  name    = "${local.root_name}-hosts"
  region  = var.region

  base_instance_name        = "${local.root_name}-host"
  distribution_policy_zones = data.google_compute_zones.available_zones.names

  target_size = var.host_config.count

  named_port {
    name = "proxy"
    port = "80"
  }

  update_policy {
    minimal_action               = "REPLACE"
    type                         = "OPPORTUNISTIC"
    instance_redistribution_type = "PROACTIVE"
    replacement_method           = "SUBSTITUTE"
    max_unavailable_fixed        = local.zone_count
    max_surge_fixed              = local.zone_count
  }

  version {
    instance_template = google_compute_region_instance_template.main.self_link
  }
}

resource "google_compute_health_check" "main" {
  project = var.project_id
  name    = "${local.root_name}-basic"

  check_interval_sec = 15
  timeout_sec        = 10

  tcp_health_check {
    port = 80
  }
}

module "security_policy" {
  source  = "GoogleCloudPlatform/cloud-armor/google"
  version = "5.0.0"

  project_id = var.project_id
  name       = local.root_name

  default_rule_action = "allow"
  type                = "CLOUD_ARMOR"
  json_parsing        = "STANDARD"
  log_level           = "NORMAL"

  layer_7_ddos_defense_enable          = true
  layer_7_ddos_defense_rule_visibility = "STANDARD"

  user_ip_request_headers = []
}

resource "google_compute_backend_service" "main" {
  project = var.project_id
  name    = local.root_name

  protocol        = "HTTP"
  port_name       = "proxy"
  security_policy = module.security_policy.policy.id
  timeout_sec     = 10
  enable_cdn      = false

  load_balancing_scheme = "EXTERNAL_MANAGED"

  health_checks = [
    google_compute_health_check.main.id
  ]

  backend {
    group           = google_compute_region_instance_group_manager.main.instance_group
    balancing_mode  = "UTILIZATION"
    capacity_scaler = 1.0
  }

  log_config {
    enable      = true
    sample_rate = 1
  }
}
