<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Neo4j AuraDB Terraform Module

This Terraform module creates Neo4j AuraDB instances for multiple tenants using the Neo4j Aura API and securely stores their credentials in Google Cloud Secret Manager.

This module performs the following:

- First, retrieves the GCP secret "auradb\_api\_client\_credentials" which should contain the neo4j API client credentials and tenant ID
- Those values are then used to authenticate with the neo4j API (https://api.neo4j.io/oauth/token) via the http tf module
- Then, that token is used to POST to the neo4j `/instances` endpoint to create new database instances
- The output from the POST call contains the database instance secret. For each new instance that's created, that password is used to create a secret in GCP Secret Manager containing neo4j connection details
  - The POST call should **not** be made for existing database instances, as it does not simply update the instance; it will create a new one. TF state should handle this

## Features

- Creates Neo4j AuraDB instances for each database name in the provided list using the Aura API
- Configures instances with specified memory (1GB), storage (2GB), and 1 CPU
- Stores connection credentials in Google Cloud Secret Manager in a standardized JSON format

## Documentation Links

This module uses the official Neo4j Aura API to create and manage instances. For more information:

- **Neo4j Aura API Documentation**: https://neo4j.com/docs/aura/api/overview/
- **Neo4j Aura API Specification**: https://neo4j.com/docs/aura/platform/api/specification/

## Prerequisites

1. **Neo4j Aura API Credentials**:

   - Create API credentials in the Neo4j Aura Console
   - Navigate to Account Settings > API Keys in the Aura Console
   - Create a new API key and note the Client ID and Client Secret

2. **Google Cloud Project**: A GCP project with Secret Manager API enabled

## Usage

```hcl
module "neo4j" {
  source = "../../../../../infrastructure/modules/instance/neo4j"

  # Standard Control Tower variables
  project_id             = local.project_id
  common_resource_labels = local.common_resource_labels

  neo4j_database_names = ["customer_name"]

  # Optional: Neo4j specific configuration
  aura_region      = "gcp-us-central1"
  instance_memory  = "1GB"
  instance_storage = "2GB"
  aura_type        = "professional-db"
}
```

## Secret Format

Each database will have its own GCP secret containing neo4j connection details in the following JSON structure:

```json
{
  "NEO4J_URI": "neo4j+s://{instanceId}.databases.neo4j.io",
  "NEO4J_USERNAME": "neo4j",
  "NEO4J_PASSWORD": "generated-secure-password",
  "AURA_INSTANCEID": "aura-instance-id",
  "AURA_INSTANCENAME": "database_instance_name"
}
```

## Authentication Setup

1. **Get Neo4j Aura API Credentials**:

   - Navigate to the Neo4j Aura service in GCP in the project you want to enable it
   - Click Enable
   - Click Manage on Provider to go to the Neo4j console
   - Log in, then take note of the tenant ID that is in the URL of the console. You will also need this for step 2
     (e.g. `https://console-preview.neo4j.io/projects/{your_tenant_id}/instances`)
   - then navigate to **Account Settings** → **API Keys**
   - Click **Generate API Key**
   - Name it something meaningful, e.g. `CT CI TF Key`
   - Click create and copy the **Client ID** and **Client Secret**

2. **Create a secret in GCP containing the neo4j API key credentials**
   - Navigate to Secret Manager in GCP
   - Create a new secret named `auradb_api_client_credentials`
   - Paste the contents of your client credentials from step #1 into the value. It must be in the following format:
   ```json
   {
     "CLIENT_SECRET": "your_api_key_client_secret",
     "CLIENT_ID": "your_api_key_client_id",
     "TENANT_ID": "your_tenant_id"
   }
   ```
   Once the above steps are completed, this module can be executed.

## Usage

Basic usage of this module is as follows:

```hcl
module "neo4j" {
  source  = "gitlab.com/dematic/control-tower-neo4j/google"
  version = "~> 0.1.0"

  # Required variables
  common_resource_labels = var.common_resource_labels
  project_id             = var.project_id

  # Optional variables
  aura_region          = "us-east1"
  aura_type            = "professional-db"
  instance_memory      = "1GB"
  instance_storage     = "2GB"
  neo4j_database_names = []
  neo4j_version        = "5"
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |
| http | ~> 3.0 |
| local | ~> 2.0 |
| null | ~> 3.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |
| http | ~> 3.0 |
| local | ~> 2.0 |
| null | ~> 3.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_secret_manager_secret.neo4j_instances](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret) | resource |
| [google_secret_manager_secret_version.neo4j_instances](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_version) | resource |
| [null_resource.neo4j_instances](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [google_secret_manager_secret_version.neo4j_client_credentials](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/secret_manager_secret_version) | data source |
| [http_http.aura_token](https://registry.terraform.io/providers/hashicorp/http/latest/docs/data-sources/http) | data source |
| [local_file.neo4j_responses](https://registry.terraform.io/providers/hashicorp/local/latest/docs/data-sources/file) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| aura\_region | Neo4j Aura region for database instances | `string` | `"us-east1"` | no |
| aura\_type | Neo4j Aura type | `string` | `"professional-db"` | no |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| instance\_memory | Memory allocation for each Neo4j instance | `string` | `"1GB"` | no |
| instance\_storage | Storage allocation for each Neo4j instance | `string` | `"2GB"` | no |
| neo4j\_database\_names | List of database names to create Neo4j instances for | `list(string)` | `[]` | no |
| neo4j\_version | Neo4j version for the database instances | `string` | `"5"` | no |
| project\_id | The ID of the shared services GCP project to deploy to. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| auth\_status | Status of the Neo4j Aura API authentication |
| created\_secrets | List of GCP Secret Manager secret IDs created for Neo4j instances |
| neo4j\_instances | Details of created Neo4j AuraDB instances |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # Neo4j Aura region for database instances
  aura_region = "us-east1"
  
  # Neo4j Aura type
  aura_type = "professional-db"
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  # Memory allocation for each Neo4j instance
  instance_memory = "1GB"
  
  # Storage allocation for each Neo4j instance
  instance_storage = "2GB"
  
  # List of database names to create Neo4j instances for
  neo4j_database_names = []
  
  # Neo4j version for the database instances
  neo4j_version = "5"
  
  # The ID of the shared services GCP project to deploy to.
  project_id = ""
}
```

<!-- END_TFVARS_DOCS-->
