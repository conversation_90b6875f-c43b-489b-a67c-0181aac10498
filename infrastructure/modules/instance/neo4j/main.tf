###############################################
# Python-managed Aura instance orchestration   #
###############################################

# Plan step: compute which databases must be created (no side effects)
data "external" "neo4j_plan" {
  program = [
    "python3",
    "${path.module}/scripts/neo4j_manage.py",
  ]

  query = {
    action          = "plan"
    api_base_url    = local.aura_api_base_url
    access_token    = local.access_token
    tenant_id       = local.neo4j_credentials.TENANT_ID
    neo4j_customers = jsonencode(var.neo4j_customers)
  }
}

locals {
  neo4j_to_create_list = try(jsondecode(data.external.neo4j_plan.result.to_create_json), [])
  neo4j_plan_hash      = try(data.external.neo4j_plan.result.to_create_hash, "")
  neo4j_results_path   = "${path.module}/.neo4j_created.json"
  db_to_secret_id      = { for c in var.neo4j_customers : c.database_name => "${c.tenant_name}_${c.facility_name}_AuraDB" }
}

# Apply step: actually create missing instances. This runs only during apply.
resource "null_resource" "neo4j_apply_create" {
  # Changing the plan hash triggers re-run on apply
  triggers = {
    plan_hash = local.neo4j_plan_hash
  }


  provisioner "local-exec" {
    when    = create
    command = <<EOT
      set -euo pipefail
      jq -n \
        --arg action "apply" \
        --arg api_base_url "${local.aura_api_base_url}" \
        --arg access_token "${local.access_token}" \
        --arg tenant_id "${local.neo4j_credentials.TENANT_ID}" \
        --arg neo4j_version "${var.neo4j_version}" \
        --arg aura_region "${var.aura_region}" \
        --arg instance_memory "${var.instance_memory}" \
        --arg instance_storage "${var.instance_storage}" \
        --arg aura_type "${var.aura_type}" \
        --arg project_id "${var.project_id}" \
        --arg neo4j_customers '${jsonencode(var.neo4j_customers)}' \
        --arg results_path "${local.neo4j_results_path}" \
        --argjson to_create ${data.external.neo4j_plan.result.to_create_json} \
        '{
          action: $action,
          api_base_url: $api_base_url,
          access_token: $access_token,
          tenant_id: $tenant_id,
          neo4j_version: $neo4j_version,
          aura_region: $aura_region,
          instance_memory: $instance_memory,
          instance_storage: $instance_storage,
          aura_type: $aura_type,
          project_id: $project_id,
          neo4j_customers: $neo4j_customers,
          to_create: $to_create,
          results_path: $results_path
        }' | \
      python3 "${path.module}/scripts/neo4j_manage.py" | jq -e '.' > /dev/null
    EOT
  }
}

# Read created results (if any) for secret creation after apply
data "external" "neo4j_created" {
  program = [
    "python3",
    "${path.module}/scripts/neo4j_manage.py",
  ]

  query = {
    action       = "read_created"
    results_path = local.neo4j_results_path
  }

  depends_on = [null_resource.neo4j_apply_create]
}

locals {
  neo4j_created_map = try(jsondecode(data.external.neo4j_created.result.created_map_json), {})
}

# Create GCP Secret Manager secrets for all requested databases (known at plan time)
resource "google_secret_manager_secret" "neo4j_instances_created" {
  for_each = { for customer in var.neo4j_customers : customer.database_name => customer }

  project   = var.project_id
  secret_id = "${each.value.tenant_name}_${each.value.facility_name}_AuraDB"

  labels = var.common_resource_labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "neo4j_instances_created" {
  for_each = { for customer in var.neo4j_customers : customer.database_name => customer }

  secret = google_secret_manager_secret.neo4j_instances_created[each.key].id

  secret_data = jsonencode(
    lookup(local.neo4j_created_map, each.key, null) != null ? {
      NEO4J_URI         = "neo4j+s://${local.neo4j_created_map[each.key].instance_id}.databases.neo4j.io"
      NEO4J_USERNAME    = local.neo4j_created_map[each.key].username
      NEO4J_PASSWORD    = local.neo4j_created_map[each.key].password
      AURA_INSTANCEID   = local.neo4j_created_map[each.key].instance_id
      AURA_INSTANCENAME = local.neo4j_created_map[each.key].database_name
      } : {
      NEO4J_URI         = ""
      NEO4J_USERNAME    = ""
      NEO4J_PASSWORD    = ""
      AURA_INSTANCEID   = ""
      AURA_INSTANCENAME = each.key
      STATUS            = "NOT_CREATED"
    }
  )
}
