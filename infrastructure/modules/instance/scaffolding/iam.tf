locals {
  serverless_agent_suffix = "serverless-robot-prod.iam.gserviceaccount.com"
}

# Ensure the Cloud Run service agent can access images in the core services Artifact Registry
resource "google_project_iam_member" "cloud_run_agent" {
  project = var.core_services_project_id
  role    = "roles/artifactregistry.reader"
  member = (
    "serviceAccount:service-${var.project_number}@${local.serverless_agent_suffix}"
  )

  lifecycle {
    create_before_destroy = true
  }
}

# Configure IAP for the Cloud Runs
module "cloud_run_iap" {
  source = "./iap"
  count  = var.iap_config.enable ? 1 : 0

  project_id = var.project_id

  enforce_unique_naming   = var.enforce_unique_naming
  instance_display_name   = var.instance_display_name
  instance_prefix         = var.instance_prefix
  iap_brand_configuration = var.iap_config.brand_configuration
  members                 = var.iap_config.members
}
