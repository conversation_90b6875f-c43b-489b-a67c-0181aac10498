locals {
  resource_namespace = "iap"
  unique_root_name   = "${var.instance_prefix}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  iap_application_title = (
    "${var.iap_brand_configuration.application_title} ${var.instance_display_name}"
  )

  iap_member_roles = [
    "roles/iam.serviceAccountTokenCreator",
    "roles/iam.serviceAccountUser",
  ]

  iap_sa_roles = [
    "roles/iam.serviceAccountTokenCreator",
    "roles/iap.httpsResourceAccessor",
  ]

  iap_members_flat = flatten([
    for email in var.members : [
      for role in local.iap_member_roles : {
        member = email
        role   = role
      }
    ]
  ])
}
