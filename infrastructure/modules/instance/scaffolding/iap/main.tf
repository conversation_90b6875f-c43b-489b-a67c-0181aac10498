resource "google_service_account" "main" {
  project     = var.project_id
  account_id  = "${local.root_name}-user-sa"
  description = "Service account for enabling IAP access to the API backend."
}

resource "google_project_iam_member" "main" {
  project  = var.project_id
  for_each = toset(local.iap_sa_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_service_account_iam_member" "main" {
  service_account_id = google_service_account.main.name

  for_each = {
    for binding in local.iap_members_flat :
    "${binding.member}-${binding.role}" => binding
  }

  member = each.value.member
  role   = each.value.role
}

resource "google_iap_brand" "main" {
  project = var.project_id

  support_email     = var.iap_brand_configuration.support_email
  application_title = local.iap_application_title
}

resource "google_iap_client" "main" {
  display_name = "IAP Client for ${var.instance_display_name}"
  brand        = google_iap_brand.main.name
}

resource "google_secret_manager_secret" "main" {
  project = var.project_id

  secret_id = "${local.root_name}-client-secret"

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "main" {
  secret      = google_secret_manager_secret.main.id
  secret_data = google_iap_client.main.secret
}
