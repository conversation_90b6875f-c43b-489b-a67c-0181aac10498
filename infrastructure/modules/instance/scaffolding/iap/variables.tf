variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "iap_brand_configuration" {
  description = <<EOT
    The configuration for the Identity-Aware Proxy (IAP) brand. This includes the support email 
    and application title for the IAP brand. This is used to create an OAuth brand for IAP.
  EOT

  type = object({
    support_email     = string
    application_title = string
  })
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "members" {
  description = <<EOT
    A list of IAM members to grant access to the IAP service account. This is used to allow 
    developers to access the API backend through IAP.

    Each member should be in the format `<type>:<email>`, where `<type>` is the type of the
    IAM member (e.g., "user", "serviceAccount", "group") and `<email>` is the email address of
    the IAM member.
  EOT

  type = list(string)

  validation {
    condition     = length(var.members) > 0
    error_message = "At least one member must be provided if IAP is being enabled."
  }

  validation {
    error_message = <<EOT
      error_message = "Each member must be in the format '<type>:<email>', where <type> is 
      'user', 'serviceAccount', or 'group', and <email> is a valid email address."
    EOT

    condition = alltrue(
      [
        for member in var.members :
        can(regex(
          "^(user|serviceAccount|group):[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
          member)
        )
      ]
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}
