module "network" {
  source = "./network"

  project_id = var.project_id
  region     = var.region

  common_resource_labels = var.common_resource_labels
  enable_flow_logs       = var.network_config.enable_flow_logs
  enforce_unique_naming  = var.enforce_unique_naming
  flow_log_configuration = var.network_config.flow_log_config
  instance_display_name  = var.instance_display_name
  instance_prefix        = var.instance_prefix

  google_health_check_source_ip_ranges = (
    var.network_config.ip_addresses.google_health_check_source_ip_ranges
  )

  google_iap_source_ip_ranges = var.network_config.ip_addresses.google_iap_source_ip_ranges

  private_service_connect_ip_address = (
    var.network_config.ip_addresses.private_service_connect_ip_address
  )
}

resource "google_storage_bucket" "access_logs" {
  project  = var.project_id
  name     = "${local.globally_unique_root_name}-access-logs"
  location = var.access_logs_bucket_config.location

  uniform_bucket_level_access = true
  storage_class               = var.access_logs_bucket_config.storage_class
  force_destroy               = true
  public_access_prevention    = "enforced"
  labels                      = var.common_resource_labels

  retention_policy {
    is_locked        = var.access_logs_bucket_config.is_retention_policy_locked
    retention_period = var.access_logs_bucket_config.retention_period_seconds
  }
}

resource "google_storage_bucket" "snapshots" {
  project  = var.project_id
  name     = "${local.globally_unique_root_name}-snapshots"
  location = var.snapshots_bucket_config.location

  uniform_bucket_level_access = true
  storage_class               = var.snapshots_bucket_config.storage_class
  force_destroy               = true
  public_access_prevention    = "enforced"
  labels                      = var.common_resource_labels

  retention_policy {
    is_locked        = var.snapshots_bucket_config.is_retention_policy_locked
    retention_period = var.snapshots_bucket_config.retention_period_seconds
  }
}

resource "google_compute_shared_vpc_service_project" "main" {
  count = var.shared_vpc_host_project_id != null ? 1 : 0

  service_project = var.project_id
  host_project    = var.shared_vpc_host_project_id
}
