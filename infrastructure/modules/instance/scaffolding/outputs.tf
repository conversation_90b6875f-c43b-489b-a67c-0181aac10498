output "access_logs_bucket_name" {
  value       = google_storage_bucket.access_logs.name
  description = "The name of the access logs bucket."
}

output "network_flow_log_configuration" {
  description = <<EOF
    The default flow log configuration that subnets in the VPC network should use unless 
    requirements dictate a different configuration.
    
  EOF

  value = module.network.flow_log_configuration
}

output "network_self_link" {
  description = "The self-link of the VPC network that was created."
  value       = module.network.network_self_link
}

output "service_networking_connection_ip_range" {
  description = <<EOF
    The IP range that was reserved for the private service networking connection. This is used to 
    connect GCP services like Memorystore and Cloud SQL with our VPC.
  EOF

  value = module.network.service_networking_connection_ip_range
}

output "snapshots_bucket_name" {
  value       = google_storage_bucket.snapshots.name
  description = "The name of the snapshots bucket."
}
