output "access_logs_bucket_name" {
  value       = google_storage_bucket.access_logs.name
  description = "The name of the access logs bucket."
}

output "network_flow_log_configuration" {
  description = <<EOF
    The default flow log configuration that subnets in the VPC network should use unless 
    requirements dictate a different configuration.
    
  EOF

  value = module.network.flow_log_configuration
}

output "network_self_link" {
  description = "The self-link of the VPC network that was created."
  value       = module.network.network_self_link
}

output "service_networking_connection_ip_range" {
  description = <<EOF
    The IP range that was reserved for the private service networking connection. This is used to 
    connect GCP services like Memorystore and Cloud SQL with our VPC.
  EOF

  value = module.network.service_networking_connection_ip_range
}

output "shared_vpc_connection" {
  description = <<EOT
    The connection information for the shared VPC, including the project ID, network self-link, and 
    the self-link of the subnet that was created for this instance in the shared VPC.
  EOT

  value = {
    network_id              = data.google_compute_network.shared_vpc.id
    network_self_link       = data.google_compute_network.shared_vpc.self_link
    project_id              = data.google_compute_network.shared_vpc.project
    subnet_id               = google_compute_subnetwork.shared_vpc_subnet.id
    subnet_self_link        = google_compute_subnetwork.shared_vpc_subnet.self_link
    vpc_access_connector_id = google_vpc_access_connector.shared_vpc.id
  }
}
