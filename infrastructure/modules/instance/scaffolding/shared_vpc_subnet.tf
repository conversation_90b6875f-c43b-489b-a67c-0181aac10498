# data source validates the existence of the shared VPC
data "google_compute_network" "shared_vpc" {
  project = var.shared_vpc_config.host_project_id
  name    = var.shared_vpc_config.host_network_name
}

# create a subnet in the shared VPC
resource "google_compute_subnetwork" "shared_vpc_subnet" {
  project = data.google_compute_network.shared_vpc.project
  region  = var.region

  ip_cidr_range = var.shared_vpc_config.subnet_cidr_range
  name          = "${var.instance_prefix}-subnet"
  network       = data.google_compute_network.shared_vpc.self_link
}

resource "google_vpc_access_connector" "shared_vpc" {
  project = var.project_id
  region  = var.region

  machine_type  = var.vpc_access_connector_config.machine_type
  min_instances = var.vpc_access_connector_config.min_instances
  max_instances = var.vpc_access_connector_config.max_instances
  name          = "${var.instance_prefix}-vpc-connector"

  subnet {
    name       = google_compute_subnetwork.shared_vpc_subnet.name
    project_id = data.google_compute_network.shared_vpc.project
  }
}
