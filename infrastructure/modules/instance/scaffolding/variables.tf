variable "access_logs_bucket_config" {
  description = "The configuration for the access logs storage bucket."
  type = object({
    is_retention_policy_locked = bool
    location                   = string
    retention_period_seconds   = number
    storage_class              = string
  })

  validation {
    condition = (
      contains(["STANDARD", "NEARLINE", "COLD<PERSON>IN<PERSON>", "ARCHIVE"],
      var.access_logs_bucket_config.storage_class)
    )
    error_message = "The storage class must be one of STANDARD, NEARLINE, COLDLINE, or ARCHIVE."
  }
}

variable "additional_apis" {
  description = <<EOT
    The list of additional APIs that should be enabled on the project beyond the default set.
    This is a list of API names, such as "compute.googleapis.com", "storage.googleapis.com", etc.
  EOT

  type = list(string)
}

variable "additional_api_identities" {
  description = <<EOT
    The list of additional APIs that use service agents that should be enabled on the project, 
    beyond the core set already defined in the module. This will also force the initialization of 
    the service agent so that they can be immediately used by other Terraform resources.
  EOT

  type = list(object({
    api   = string
    roles = list(string)
  }))
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "core_services_project_id" {
  description = "The ID of the core services project to use for the project."
  type        = string
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "iap_config" {
  description = <<EOT
    The configuration for the Identity-Aware Proxy (IAP) service. This includes whether IAP is 
    enabled; if enabled, two additional properties are required: the OAuth brand configuration, and 
    a list of members who should be granted permissions to impersonate the IAP service account. The 
    members should be in the format `<type>:<email>`, where `<type>` is the type of the IAM member 
    (e.g., "user", "serviceAccount", "group") and `<email>` is a valid email address.
  EOT

  type = object({
    enable = bool

    members = optional(list(string), [])
    brand_configuration = optional(object({
      application_title = string
      support_email     = string
    }))
  })

  validation {
    error_message = "If IAP is enabled, at least one IAP member must be provided."
    condition = (
      var.iap_config.enable == false ||
      length(var.iap_config.members) > 0
    )
  }

  validation {
    error_message = "If IAP is enabled, the brand configuration must be provided."
    condition = (
      var.iap_config.enable == false ||
      var.iap_config.brand_configuration != null
    )
  }
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "network_config" {
  description = <<EOT
    The configuration for the VPC network. See the README and variables.tf of the network submodule 
    for more details on the configuration options.
  EOT

  type = object({
    enable_flow_logs = bool

    ip_addresses = object({
      google_health_check_source_ip_ranges = list(string)
      google_iap_source_ip_ranges          = list(string)
      private_service_connect_ip_address   = string
    })

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    condition = (
      var.network_config.enable_flow_logs == false ||
      var.network_config.flow_log_config != null
    )

    error_message = "The flow log configuration must be provided if flow logs are enabled."
  }

  validation {
    error_message = <<EOT
      The value for `private_service_connect_ip_address` must be a valid IP or CIDR range.
    EOT

    condition = (
      can(regex(
        "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
        var.network_config.ip_addresses.private_service_connect_ip_address
      )) ||
      can(regex(
        "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
        var.network_config.ip_addresses.private_service_connect_ip_address
      ))
    )
  }

  validation {
    error_message = <<EOT
      The value for `google_health_check_source_ip_ranges` must be a list of valid IP or CIDR 
      ranges.
    EOT

    condition = (
      alltrue(
        [for ip in var.network_config.ip_addresses.google_health_check_source_ip_ranges :
          can(regex(
            "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
            ip
          )) ||
          can(regex(
            "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
            ip
          ))
        ]
      )
    )
  }

  validation {
    error_message = <<EOT
      The value for `google_iap_source_ip_ranges` must be a list of valid IP or CIDR ranges.
    EOT

    condition = (
      alltrue(
        [for ip in var.network_config.ip_addresses.google_iap_source_ip_ranges :
          can(regex(
            "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
            ip
          )) ||
          can(regex(
            "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
            ip
          ))
        ]
      )
    )
  }
}

variable "project_config" {
  description = <<EOT
    The parameters needed to create the new project for the Control Tower instance. This includes
    the billing account, folder ID, organization ID, a folder short name value to use for 
    project naming, any project-specific labels that should be added to common_labels and applied 
    to the project.
  EOT

  type = object({
    billing_account   = string
    folder_id         = string
    folder_short_name = string
    organization_id   = string
    project_labels    = map(string)
  })
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "project_number" {
  description = "The project number of the GCP project to deploy to."
  type        = string
}

variable "project_role_assignments" {
  description = <<EOT
    The IAM role assignments to apply to the project. This is a map of role names to a list of 
    member identifiers. The member identifiers can be any valid GCP IAM member identifier, 
    including service accounts, groups, and users. Non-authoratative (i.e., won't override 
    assignments defined by other modules).
  EOT

  type    = map(list(string))
  default = {}
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "shared_vpc_host_project_id" {
  description = <<EOT
    The ID of the shared VPC host project to use for the Control Tower instance. This is required 
    if the Control Tower instance is to be deployed in a shared VPC network.
  EOT

  type    = string
  default = null
}

variable "snapshots_bucket_config" {
  description = "The configuration for the snapshot storage bucket."
  type = object({
    is_retention_policy_locked = bool
    location                   = string
    retention_period_seconds   = number
    storage_class              = string
  })

  validation {
    condition = (
      contains(["STANDARD", "NEARLINE", "COLDLINE", "ARCHIVE"],
      var.snapshots_bucket_config.storage_class)
    )
    error_message = "The storage class must be one of STANDARD, NEARLINE, COLDLINE, or ARCHIVE."
  }
}
