variable "access_logs_bucket_config" {
  description = "The configuration for the access logs storage bucket."
  type = object({
    is_retention_policy_locked = bool
    location                   = string
    retention_period_seconds   = number
    storage_class              = string
  })

  validation {
    condition = (
      contains(["STANDARD", "NEARLINE", "CO<PERSON><PERSON><PERSON><PERSON>", "ARCHIVE"],
      var.access_logs_bucket_config.storage_class)
    )
    error_message = "The storage class must be one of STANDARD, NEARLINE, COLDLINE, or ARCHIVE."
  }
}

variable "artifact_registry_project_id" {
  description = <<EOT
    The project ID for the Artifact Registry Docker repository that hosts the API images. This is 
    used to grant read access to the Cloud Run service agent so that it can pull the images for 
    Cloud Run deployments.
  EOT
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "network_config" {
  description = <<EOT
    The configuration for the VPC network. See the README and variables.tf of the network submodule 
    for more details on the configuration options.
  EOT

  type = object({
    enable_flow_logs = bool

    ip_addresses = object({
      google_health_check_source_ip_ranges = list(string)
      google_iap_source_ip_ranges          = list(string)
      private_service_connect_ip_address   = string
    })

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    condition = (
      var.network_config.enable_flow_logs == false ||
      var.network_config.flow_log_config != null
    )

    error_message = "The flow log configuration must be provided if flow logs are enabled."
  }

  validation {
    error_message = <<EOT
      The value for `private_service_connect_ip_address` must be a valid IP or CIDR range.
    EOT

    condition = (
      can(regex(
        "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
        var.network_config.ip_addresses.private_service_connect_ip_address
      )) ||
      can(regex(
        "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
        var.network_config.ip_addresses.private_service_connect_ip_address
      ))
    )
  }

  validation {
    error_message = <<EOT
      The value for `google_health_check_source_ip_ranges` must be a list of valid IP or CIDR 
      ranges.
    EOT

    condition = (
      alltrue(
        [for ip in var.network_config.ip_addresses.google_health_check_source_ip_ranges :
          can(regex(
            "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
            ip
          )) ||
          can(regex(
            "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
            ip
          ))
        ]
      )
    )
  }

  validation {
    error_message = <<EOT
      The value for `google_iap_source_ip_ranges` must be a list of valid IP or CIDR ranges.
    EOT

    condition = (
      alltrue(
        [for ip in var.network_config.ip_addresses.google_iap_source_ip_ranges :
          can(regex(
            "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(/(?:[0-9]|[1-2][0-9]|3[0-2]))?$",
            ip
          )) ||
          can(regex(
            "^([0-9]{1,3}\\.){3}[0-9]{1,3}$",
            ip
          ))
        ]
      )
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "project_number" {
  description = "The project number of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "shared_vpc_config" {
  description = <<EOT
    The configuration for the shared VPC connection, including:
    - host_network_name: The name of the host network.
    - host_project_id: The project ID of the host project.
    - subnet_ip_range: The IP CIDR range for the subnet.
  EOT

  type = object({
    host_network_name = string
    host_project_id   = string
    subnet_cidr_range = string
  })
}

variable "vpc_access_connector_config" {
  description = "The configuration for the VPC access connector."
  type = object({
    machine_type  = string
    min_instances = number
    max_instances = number
  })
}
