<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# UI Load Balancer Terraform Module

## Table of Contents

- [Description](#description)
- [Resource Lifecycle Management](#resource-lifecycle-management)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the resources for the UI load balancer

## Resource Lifecycle Management

The following resource configurations are required to allow successful deployment and ongoing management through `tofu apply` and `tofu destroy` commands:

### SSL Certificates

An SSL certificate cannot be destroyed if it's in use, which means that operational motions that require replacement will fail. There are two steps required to get around this:

#### 1. Add the `create_before_destroy` lifecycle rule

``` yaml
lifecycle {
    create_before_destroy = true
  }
```

This causes Terraform to create the replacement before destroying the old one. However, this will fail if the resource has a static name, because the name will already be in use. This leads to the second step.

#### 2. Add a generated suffix to the SSL certificate resource name

By generating a 4-character, random string and adding it as a suffix to the resource name (in keeping with standard GCP conventions for resources managed by automation), we can ensure that the replacement cert will always have a unique name. This allows resources dependent on the old cert to switch to the new one, which in turn enables destruction of the old cert.

To ensure that we're only, and always, regenerating the random suffix if the cert must be replaced, we set `keepers` on the `random_string` for anything that causes cert regeneration. This avoids unnecessary churn, while still ensuring we can change anything and everything about the cert if needed.

``` yaml
resource "random_string" "main" {
  length  = 4
  special = false
  upper   = false

  keepers = {
    managed_domain = google_dns_record_set.main.name
  }
}
```

## Usage

Basic usage of this module is as follows:

```hcl
module "ui-load-balancer" {
  source  = "gitlab.com/dematic/control-tower-ui-load-balancer/google"
  version = "~> 0.1.0"

  # Required variables
  alert_email_address    = var.alert_email_address
  common_resource_labels = var.common_resource_labels
  dns_config             = var.dns_config
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  load_balancing_scheme  = var.load_balancing_scheme
  minimum_tls_version    = var.minimum_tls_version
  project_id             = var.project_id
  region                 = var.region
  site_asset_backend_id  = var.site_asset_backend_id

  # Optional variables
  proxy_service_backend_id = null
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| ssl\_certificate | ./ssl_certificate | n/a |

## Resources

| Name | Type |
|------|------|
| [google_compute_global_address.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_address) | resource |
| [google_compute_global_forwarding_rule.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_forwarding_rule) | resource |
| [google_compute_ssl_policy.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_ssl_policy) | resource |
| [google_compute_target_https_proxy.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_target_https_proxy) | resource |
| [google_compute_url_map.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map) | resource |
| [google_dns_record_set.a_record](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_monitoring_uptime_check_config.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_uptime_check_config) | resource |
| [google_dns_managed_zone.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| alert\_email\_address | The email address to send alerts to. | `string` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| dns\_config | The DNS configuration for the load balancer. | ```object({ a_record_ttl = number project_id = string zone_name = string })``` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| load\_balancing\_scheme | The load balancing scheme to use for the load balancer. For an external load balancer, this can      be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either      `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for     production use, as it provides better performance and reliability. | `string` | n/a | yes |
| minimum\_tls\_version | The minimum TLS version to use for the load balancer. This value is used to create the      SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported. | `string` | n/a | yes |
| project\_id | The ID of the GCP project to deploy the load balancer in. | `string` | n/a | yes |
| proxy\_service\_backend\_id | The ID of the backend service that exposes the regional proxy host(s) to the front-end load      balancer. | `string` | `null` | no |
| region | The region to deploy regional resources in. | `string` | n/a | yes |
| site\_asset\_backend\_id | The ID of the backend service that serves the static site assets to the front-end load balancer. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| dns\_names | The DNS name(s) registered for the UI load balancer. |
| frontend\_ip\_address | The public IP address for this Control Tower instance. |
| instance\_base\_url | The base URL for the instance. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The email address to send alerts to.
  alert_email_address = ""
  
  #     The common labels that should be applied to all resources that can be labeled.
  #
  common_resource_labels = ""
  
  # The DNS configuration for the load balancer.
  dns_config = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The load balancing scheme to use for the load balancer. For an external load balancer, this can
  #     be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either
  #     `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
  #     production use, as it provides better performance and reliability.
  #
  load_balancing_scheme = ""
  
  #     The minimum TLS version to use for the load balancer. This value is used to create the
  #     SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported.
  #
  minimum_tls_version = ""
  
  # The ID of the GCP project to deploy the load balancer in.
  project_id = ""
  
  #     The ID of the backend service that exposes the regional proxy host(s) to the front-end load
  #     balancer.
  #
  proxy_service_backend_id = ""
  
  # The region to deploy regional resources in.
  region = ""
  
  #     The ID of the backend service that serves the static site assets to the front-end load balancer.
  #
  site_asset_backend_id = ""
}
```

<!-- END_TFVARS_DOCS-->
