/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/ai/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAiBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/ai/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAiFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/ai/enterprise-search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAiEnterpriseSearch"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/user-writable": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** @description Updates a user writable setting. This endpoint is specifically for user-level settings.
         *     Any authenticated user can update their own settings, and configurators can update any user's settings. */
        put: operations["PutUserWritable"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/process-flow/metric-configs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Get metric configurations based on filter criteria.
         *     Without any query params, returns all metric configs that are not soft_deleted. */
        get: operations["GetMetricConfigs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/process-flow/metric-config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** @description Updates or creates a metric configuration.
         *     If a configuration with the same metric name and facility already exists, it will be updated.
         *     Otherwise, a new configuration will be created.
         *     The facility ID is determined from the ict-facility-id header. */
        put: operations["PutConfigProcessFlowMetricConfig"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetConfigBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetConfigFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/settings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Retrieves all settings and their values or a single setting based on id and/or name */
        get: operations["GetConfigSettings"];
        /** @description Updates the specified setting with the properties in settingInfo.  Creates a new setting if
         *     the setting isn't found, and the required information is provided. */
        put: operations["PutConfigSettings"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/insert-default-config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Inserts the default settings to seed a new tenant's database */
        post: operations["PostConfigDefaultConfig"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/settings/{settingId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["DeleteConfigSetting"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/settings/schema": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["getSettingSchema"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/setting-logs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Retrieves logs for a setting */
        get: operations["GetConfigSettingLogs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/facility-config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetConfigFacilityConfig"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/curated-data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * @deprecated
         * @description Handle a request for the top 100 rows of a table.
         */
        get: operations["GetConfigCuratedData"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/v2/curated-data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Handle a request for the top 100 rows of a table. */
        get: operations["GetConfigCuratedDataV2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/curated-tables/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Endpoint for getting a list of tables available in the curated dataset. */
        get: operations["GetConfigCuratedTablesList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/config/app-config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetConfigAppConfig"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerSearch"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/results": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerResults"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/results/{resultId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerResult"];
        put: operations["PutDataExplorerResult"];
        post?: never;
        delete: operations["DeleteDataExplorerResult"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/recommendations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerRecommendations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/gold-questions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDematicChatGoldQuestions"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataExplorerFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/data-explorer/agentsearch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Retrieves an example data explorer result. */
        get: operations["GetDataExplorerAgentSearch"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/diagnostics/infrastructure/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDiagnosticsInfrastructureList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/diagnostics/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDiagnosticsBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/diagnostics/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDiagnosticsFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/upload/known-demand": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Receives two xls files and parses them into multiline JSON to forward to the EDP PubSub Topic */
        post: operations["PostInventoryUploadKnownDemand"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/upload/recent-activity": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryUploadRecentActivity"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/storage/utilization": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryStorageUtilization"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/stock/distribution/under/percentage/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryStockDistributionUnderPercentageSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/stock/distribution/over/percentage/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryStockDistributionOverPercentageSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/stock/distribution/no/percentage/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryStockDistributionNoPercentageSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/stock/distribution/at/percentage/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryStockDistributionAtPercentageSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/skus/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostInventorySkusList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/skus/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostInventorySkusListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/replenishment/task-type-series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryReplenishmentTaskTypeSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/replenishment/details": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryReplenishmentDetails"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryProcessFlowAreas"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/area/{areaId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryProcessFlowAreaById"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/details/{type}/{elementId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryProcessFlowGraphDetails"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/areas/{areaId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: operations["UpdateInventoryProcessFlowArea"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/edges/{edgeId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: operations["UpdateInventoryProcessFlowEdge"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/metrics/{metricId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: operations["UpdateInventoryProcessFlowMetric"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/process-flow/clear-cache": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["ClearInventoryProcessFlowCache"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/performance/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryPerformanceSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/sku/high-impact/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostInventorySkuHighImpactList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/handling-units/trayed": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryHandlingUnitsTrayed"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/data-analysis-timestamp": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetDataAnalysisTimestampData"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostInventoryForecastList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Creates an excel export for inventory forecast data. */
        post: operations["PostInventoryForecastListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/{skuId}/locations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryForecastSkuLocations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/{skuId}/orders": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryForecastSkuOrders"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/forecast/{skuId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventorySkuForecast"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/filter": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryFilter"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/containers/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Post inventory container data with filters */
        post: operations["PostInventoryContainersList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/containers/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostInventoryContainersListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/container-events/list/{containerId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Post inventory container events data with filters and sorting. */
        post: operations["PostInventoryContainerEventsList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/container-events/list/export/{containerId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Creates an excel export for inventory container events. */
        post: operations["PostInventoryContainerEventsListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/bin-locations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Endpoint for retrieving bin locations for a given DMS aisle and level. */
        get: operations["GetInventoryBinLocations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/outstanding": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesOutstanding"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/in-progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesInProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/finished": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesFinished"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/{adviceId}/details": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesDetails"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/advices/cycle-time": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAdvicesCycleTime"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/accuracy": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetInventoryAccuracy"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/inventory/container-events/{containerId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Get container KPI metrics for a specific container ID. */
        get: operations["GetInventoryContainerEventsDetails"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/workstation/{workstationId}/starved-blocked-time/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentWorkstationStarvedBlockedTimeSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/workstation/{workstationId}/operator/activity": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentWorkstationOperatorActivity"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/workstation/{workstationId}/line-rates/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentWorkstationLineRatesSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/workstation/{workstationId}/movements/detail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentWorkstationMovementsDetail"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/workstation/{workstationId}/aisle/active-faults": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentWorkstationAisleActiveFaults"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/summary/workstations/{workstationId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentSummaryWorkstations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/summary/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentSummaryAreas"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/summary/aisles/{aisleId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentSummaryAisle"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/events/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentEventsList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/outbound-rate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentOutboundRate"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/status/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsStatusList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFaultsSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/events": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFaultsEvents"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/movements/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsMovementsSeries"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/level/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsLevelList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/grouped/count/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["getFaultGroupedByCounts"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/device-id/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsDeviceIdsList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/device-type/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsDeviceTypeList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFaults"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/average/duration/status/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** @description Endpoint to get the average duration of faults by status for a given date range. */
        post: operations["getFaultAvgDurationByStatus"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/area": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFaultsArea"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/aisle/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostEquipmentFaultsAisleList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/equipment/faults/active/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetEquipmentFaultsActiveList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOperatorsBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOperatorsFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: operations["PutOperatorsAreas"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/active/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOperatorsActiveSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/active": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOperatorsActive"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/operators/active/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOperatorsActiveAreas"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/shipped": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetPickOrdersShipped"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/pick/cycle-count": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersPickCycleCount"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/shipped": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
         *     the total number of orders open between start_date and end_date */
        get: operations["GetOrdersFacilityShipped"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFacilityProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/completion": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFacilityEstimatedCompletion"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/shipped": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
         *     the total number of orders open between start_date and end_date */
        get: operations["GetOrdersCustomerShipped"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/throughput": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersThroughput"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/throughput/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersThroughputSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/remaining": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersRemaining"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/fulfillment": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFulfillment"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/pick/progress/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Gets a series of order progress percentage, averaging by each hour incrementing. */
        get: operations["GetOrdersProgressSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/pick/line/throughput/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersPickLineThroughputSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/performance/fulfillment": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersPerformanceFulfillment"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/outstanding": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersOutstanding"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/line/throughput": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersLineThroughput"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/lineprogress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersLineProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/lineprogress/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersLineProgressSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/pick/cycletime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersPickCycleTime"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/cycletime/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCycleTimeSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/line/throughput": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerLineThroughput"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/throughput/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersThroughputAreas"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/lineprogress/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersLineProgressAreas"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/cycletime/areas": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCycleTime"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/throughput": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Calculates the number of orders per hour that were shipped in the given time frame.
         *     If an area param is used, then the matching status is used in the calculation instead of 'shipped'.
         *     If the end_date is in the future, then the current datetime is used instead. */
        get: operations["GetOrdersFacilityThroughput"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/line/throughput/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFacilityLineThroughputSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/line/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFacilityLineProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/facility/cycletime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersFacilityCycleTime"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/completion": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCompletion"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/throughput": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Calculates the number of orders per hour that were shipped in the given time frame.
         *     If an area param is used, then the matching status is used in the calculation instead of 'shipped'.
         *     If the end_date is in the future, then the current datetime is used instead. */
        get: operations["GetOrdersCustomerThroughput"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/throughput/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerThroughputSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/progress/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Gets a series of order progress percentage, averaging by each hour incrementing. */
        get: operations["GetOrdersCustomerProgressSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/line/throughput/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerLineThroughputSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/line/progress/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Gets a series of order progress percentage, averaging by each hour incrementing. */
        get: operations["GetOrderLinesCustomerProgressSeries"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/line/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerLineProgress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/completion": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerEstimatedCompletion"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/orders/customer/cycletime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetOrdersCustomerCycleTime"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/workstations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/series": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationSeriesData"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/orders/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationOrdersStatus"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/orders/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationOrdersList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/orders/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationOrdersListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/orders/{orderId}/picks/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationOrdersDetailsPicksList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/orders/{orderId}/picks/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationOrdersDetailsPicksListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/metrics/summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationSummary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/metrics/operators": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description
         *     returns {Promise<WorkstationOperatorStats>} contract */
        get: operations["GetWorkstationOperatorSummary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/metrics/performance": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description
         *     returns {Promise<WorkstationPerformanceStats>} contract */
        get: operations["GetWorkstationPerformanceSummary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/metrics/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description
         *     returns {Promise<WorkstationHealthStats>} contract */
        get: operations["GetWorkstationHealthSummary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/daily-performance/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationDailyPerformanceList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetWorkstationList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/list/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationListExport"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/workstation/containers/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["PostWorkstationContainersList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetSimulationOutputByJobId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/simulation/jobs/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetSimulationJobs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/simulation/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetSimulationBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/simulation/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetSimulationFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/management/auth/trusted": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetTrustedTicket"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/healthcheck": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAdminBasicHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/healthcheck/full": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAdminFullHealthCheck"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/resend-verification": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["ResendEmailVerification"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetAssignableRoles"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/users/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["GetUsersList"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/users/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GetUser"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/users/{userId}/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["AssignUserRoles"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/auth/users/{userId}/roles/{roleName}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["RemoveUserRole"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** @enum {string} */
        HealthCheckStatus: "ONLINE" | "OFFLINE" | "NOT_IN_USE" | "UNHEALTHY" | "UNKOWN";
        ExtractiveAnswer: {
            content: string;
            pageNumber: string;
        };
        DerivedStructData: {
            extractive_answers: components["schemas"]["ExtractiveAnswer"][];
            link: string;
        };
        EnterpriseSearchDocument: {
            derivedStructData: components["schemas"]["DerivedStructData"];
            id: string;
            name: string;
        };
        EnterpriseSearchResponse: {
            document: components["schemas"]["EnterpriseSearchDocument"];
            id: string;
        };
        AppConfigSettingName: (string & Record<string, never>) | ("facility-maps" | "menu" | "selected-facility-id" | "site-time-zone" | "shift-start-end-times" | "user-favorites" | "file-upload-dashboard" | "inbound-overview-dashboard" | "inventory-overview-dashboard" | "outbound-overview-dashboard" | "picking-buffer-area-details-dashboard" | "workstation-overview-dashboard" | "inventory-replenishment-dashboard" | "ict-nav-help-content" | "ict-facility-process-flow-admin-controls" | "ict-facility-process-flow-config-management" | "ict-facility-process-flow-detail-panel" | "ict-facility-process-flow-detail-panel-layout" | "ict-facility-process-flow-edge-animation-effect" | "ict-facility-process-flow-edge-label-alerts" | "ict-facility-process-flow-edge-label-status-indicators" | "ict-facility-process-flow-edge-labels" | "ict-facility-process-flow-graph-config" | "ict-facility-process-flow-node-alerts" | "ict-facility-process-flow-node-drilldown-button" | "ict-facility-process-flow-node-metrics" | "ict-facility-process-flow-polling" | "ict-facility-process-flow-polling-interval" | "data-explorer-debug-data" | "inventory-high-impact-sku-percentage" | "inventory-sku-list-column-order" | "inventory-storage-utilization-aisle-filter-list" | "inventory-storage-utilization-area-filter-list" | "inventory-work-area-code-filter-list" | "inventory-zone-mapping" | "cycle-time-state-range" | "order-complete-event-codes" | "excluded-workstation-list" | "workstation-destination-locations-suffixes" | "workstation-order-delayed-time" | "data-explorer-default-recommendations" | "tableau-top-level-project-name");
        AppConfigSettingGroupName: (string & Record<string, never>) | ("system" | "feature-flags" | "custom-dashboards" | "inventory" | "orders" | "workstations" | "data-explorer" | "tableau");
        /** @enum {string} */
        AppConfigSettingDataType: "json" | "string" | "number" | "boolean";
        /** @enum {string} */
        AppConfigSettingSource: "default" | "tenant" | "facility" | "user";
        /** @description DTO for a setting with its value set to the most relevant value for the user. */
        AppConfigSetting: {
            id: string;
            name: components["schemas"]["AppConfigSettingName"] | string;
            group?: (components["schemas"]["AppConfigSettingGroupName"] | string) | null;
            description?: string | null;
            dataType: components["schemas"]["AppConfigSettingDataType"];
            source?: components["schemas"]["AppConfigSettingSource"];
            value?: unknown;
            tags?: {
                [key: string]: string;
            } | null;
            parentSetting?: string | null;
        };
        UpdateSettingData: {
            id?: string;
            name: string;
            group?: string | null;
            dataType: components["schemas"]["AppConfigSettingDataType"];
            value: unknown;
            levelToUpdate: components["schemas"]["AppConfigSettingSource"];
            description?: string | null;
        };
        /** @description Construct a type with a set of properties K of type T */
        "Record_string.boolean_": {
            [key: string]: boolean;
        };
        /** @description Represents a metric configuration */
        MetricConfigSummary: {
            /** @description Unique identifier for the metric configuration */
            id: string;
            /** @description Name of the metric */
            metricName: string;
            /** @description Type of configuration (e.g., 'node', 'edge') */
            configType: string;
            /** @description Name of the node this metric is associated with */
            nodeName?: string;
            /** @description Type of fact this metric represents */
            factType?: string;
            /** @description Whether the metric is enabled
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is enabled for that facility
             *     Can be undefined for default configurations if no facilities have enabled/disabled the metric
             *     Can be null for custom configurations where enabled status is not applicable */
            enabled?: (boolean | components["schemas"]["Record_string.boolean_"]) | null;
            /** @description Whether the metric is active
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is active for that facility
             *     Can be undefined for default configurations if no facilities have active/inactive status */
            active?: boolean | components["schemas"]["Record_string.boolean_"];
            /** @description Whether this is a custom configuration (true) or default configuration (false) */
            isCustom: boolean;
            /** @description The facility ID this configuration applies to
             *     For default configurations, this is 'default'
             *     For custom configurations, this is the specific facility ID */
            facilityId: string;
        };
        /** @description Entity for custom metric configurations.
         *     Extends the base metric configuration to add facility-specific overrides.
         *     The composite unique index on (metric_config_name, facility_id) ensures each facility
         *     can only have one custom configuration per metric configuration. */
        CustomMetricConfigurationEntity: {
            /** @description Primary key id of the entity, set to be generated to a UUID. */
            id: string;
            /**
             * @description Optional description of the entity.
             * @default null
             */
            description: string | null;
            /**
             * Format: date-time
             * @description Date and time the entity was created.
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Date and time the entity was last updated.
             */
            updatedAt: string;
            /** @description The unique identifier for this metric configuration.
             *     This is used to reference the configuration in custom configurations and facility settings.
             *     Example: shuttle_inventory_totes_shuffle_movements */
            metricConfigName: string;
            /** @description The type of fact this metric processes
             *     e.g. multishuttle_movement */
            factType?: string;
            /** @description The source system of the facts
             *     e.g. diq */
            sourceSystem?: string;
            /** @description Display name for the UI */
            displayName?: string;
            /** @description Type of configuration */
            configType: components["schemas"]["ConfigType"];
            /**
             * Format: double
             * @description Acceptable low range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableLowRange?: number;
            /**
             * Format: double
             * @description Acceptable high range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableHighRange?: number;
            /** @description Example message for testing */
            exampleMessage?: unknown;
            /** @description The hierarchy graph view at which this metric applies
             *     Example: `["facility", "multishuttle"]` */
            views: string[];
            /** @description Defines the conditions an event must meet for this metric to be processed */
            matchConditions: components["schemas"]["MatchConditions"];
            /** @description Optional parameters required for metric processing
             *     Example: `{"row_hash": "{row_hash}"}` dynamically fills `row_hash` with event data */
            redisParams?: components["schemas"]["RedisParams"];
            /** @description Defines how to dynamically generate metric names based on source fields */
            nameFormula?: components["schemas"]["NameConfig"];
            /** @description The label for the node. Defaults to "Area" */
            label?: components["schemas"]["NodeLabel"];
            /** @description Defines the parent node hierarchy for this metric
             *     Can be:
             *     - undefined: No parent nodes
             *     - string: Single parent node name
             *     - string[]: Array of parent node names in hierarchical order
             *     Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` */
            parentNodes?: string[];
            nodeName?: string;
            metricType?: string;
            timeWindow?: components["schemas"]["TimeWindow"];
            aggregation?: components["schemas"]["Aggregation"];
            graphOperation?: components["schemas"]["GraphOperation"];
            redisOperation?: components["schemas"]["RedisOperation"];
            metricUnits?: string;
            huId?: string;
            inboundArea?: string;
            outboundArea?: string;
            units?: string;
            outboundNodeLabel?: components["schemas"]["NodeLabel"];
            inboundParentNodes?: string[];
            outboundParentNodes?: string[];
            /** @description The facility ID this configuration applies to.
             *     Part of the composite unique index with metric_config_name to ensure
             *     each facility can only have one custom configuration per metric. */
            facilityId: string;
            /** @description Whether this metric is active for this facility.
             *     A metric is active if an incoming fact was matched on it. */
            active: boolean;
            /** @description Whether this metric is enabled for this facility.
             *     A metric is enabled on the facility level if a user has enabled it. */
            enabled: boolean;
            /** @description The default configuration this custom configuration extends (optional).
             *     This creates a many-to-one relationship with the default configuration.
             *     Custom configurations can exist independently without a default configuration.
             *     When the default configuration is deleted, this custom configuration will be deleted as well. */
            defaultConfig?: components["schemas"]["DefaultMetricConfigurationEntity"];
        };
        /** @enum {string} */
        ConfigType: "node" | "inbound-edge" | "outbound-edge" | "complete-edge";
        /** @description Construct a type with a set of properties K of type T */
        "Record_string.string_": Record<string, never>;
        /** @description Construct a type with a set of properties K of type T */
        "Record_string.string-or-Array_Record_string.string___": {
            [key: string]: string | components["schemas"]["Record_string.string_"][];
        };
        MatchConditions: components["schemas"]["Record_string.string-or-Array_Record_string.string___"];
        /** @description Construct a type with a set of properties K of type T */
        "Record_string.string-or-number_": {
            [key: string]: string | number;
        };
        RedisParams: components["schemas"]["Record_string.string-or-number_"];
        /** @description Defines the structure for dynamically generating metric names based on event data for inbound or outbound edges */
        NameConfig: {
            /** @description A format string representing the source field from the event data.
             *     Example: `"{source_location_code}"` (must match a key in event data). */
            source: string;
            /** @description A regular expression pattern for extracting dynamic values from `source`.
             *     Example: `"MSAI(?P<aisle>\\d{2}).*"` extracts an `aisle` number from a multishuttle location. */
            pattern: string;
            /** @description A format string that defines how to generate the final metric name using extracted values.
             *     Example: `"MSAI{aisle}"` produces `"MSAI01"`, `"MSAI02"`, etc. */
            template: string;
        };
        /** @enum {string} */
        NodeLabel: "Aisle" | "Area" | "Lift" | "Station" | "StationLocation" | "Level" | "Metric" | "Shuttle";
        /** @enum {string} */
        TimeWindow: "15m_set" | "30m_set" | "60m_set" | "value";
        /** @enum {string} */
        Aggregation: "avg" | "count" | "destination_position_ratio" | "hourly_rate" | "ratio" | "static" | "sum" | "sum_item_values";
        /** @enum {string} */
        GraphOperation: "area_node" | "area_edge" | "shuttle_node";
        /** @enum {string} */
        RedisOperation: "boolean_toggle" | "complex_event_set" | "cycle_time_start" | "cycle_time_stop" | "distinct_item_count" | "distinct_item_subtract" | "event_set" | "storage_location_distribution_available" | "storage_location_distribution_occupied" | "storage_utilization" | "store_static_value" | "total_storage_locations_occupied";
        /** @description Entity for default metric configurations.
         *     This is the base configuration that defines how a metric should be processed.
         *     It has a unique metric_config_name and can be referenced by both custom configurations
         *     and facility settings.
         *
         *     The enabled and active fields are h-stores where:
         *     - Keys are facility IDs
         *     - Values are booleans indicating if the metric is enabled/active for that facility */
        DefaultMetricConfigurationEntity: {
            /** @description Primary key id of the entity, set to be generated to a UUID. */
            id: string;
            /**
             * @description Optional description of the entity.
             * @default null
             */
            description: string | null;
            /**
             * Format: date-time
             * @description Date and time the entity was created.
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Date and time the entity was last updated.
             */
            updatedAt: string;
            /** @description The unique identifier for this metric configuration.
             *     This is used to reference the configuration in custom configurations and facility settings.
             *     Example: shuttle_inventory_totes_shuffle_movements */
            metricConfigName: string;
            /** @description The type of fact this metric processes
             *     e.g. multishuttle_movement */
            factType?: string;
            /** @description The source system of the facts
             *     e.g. diq */
            sourceSystem?: string;
            /** @description Display name for the UI */
            displayName?: string;
            /** @description Type of configuration */
            configType: components["schemas"]["ConfigType"];
            /**
             * Format: double
             * @description Acceptable low range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableLowRange?: number;
            /**
             * Format: double
             * @description Acceptable high range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableHighRange?: number;
            /** @description Example message for testing */
            exampleMessage?: unknown;
            /** @description The hierarchy graph view at which this metric applies
             *     Example: `["facility", "multishuttle"]` */
            views: string[];
            /** @description Defines the conditions an event must meet for this metric to be processed */
            matchConditions: components["schemas"]["MatchConditions"];
            /** @description Optional parameters required for metric processing
             *     Example: `{"row_hash": "{row_hash}"}` dynamically fills `row_hash` with event data */
            redisParams?: components["schemas"]["RedisParams"];
            /** @description Defines how to dynamically generate metric names based on source fields */
            nameFormula?: components["schemas"]["NameConfig"];
            /** @description The label for the node. Defaults to "Area" */
            label?: components["schemas"]["NodeLabel"];
            /** @description Defines the parent node hierarchy for this metric
             *     Can be:
             *     - undefined: No parent nodes
             *     - string: Single parent node name
             *     - string[]: Array of parent node names in hierarchical order
             *     Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` */
            parentNodes?: string[];
            nodeName?: string;
            metricType?: string;
            timeWindow?: components["schemas"]["TimeWindow"];
            aggregation?: components["schemas"]["Aggregation"];
            graphOperation?: components["schemas"]["GraphOperation"];
            redisOperation?: components["schemas"]["RedisOperation"];
            metricUnits?: string;
            huId?: string;
            inboundArea?: string;
            outboundArea?: string;
            units?: string;
            outboundNodeLabel?: components["schemas"]["NodeLabel"];
            inboundParentNodes?: string[];
            outboundParentNodes?: string[];
            /** @description Whether this metric is enabled for each facility.
             *     Keys are facility IDs, values are booleans.
             *     A metric can only be disabled by a configurator via the API. */
            enabled?: components["schemas"]["Record_string.boolean_"];
            /** @description Whether this metric is active for each facility.
             *     Keys are facility IDs, values are booleans.
             *     A metric is active if an incoming fact was matched on it. */
            active?: components["schemas"]["Record_string.boolean_"];
            /** @description Custom configurations that extend this default configuration (optional).
             *     Each custom configuration is specific to a facility.
             *     Custom configurations can exist independently without a default configuration. */
            customConfigs?: components["schemas"]["CustomMetricConfigurationEntity"][];
        };
        /** @description Base interface for all metric configurations */
        BaseMetricConfigDetail: {
            /** @description Identifier for the metric config
             *     Example: multishuttleAisleStorageLocationDistributionAvailable */
            metricConfigName: string;
            /** @description The hierarchy graph view at which this metric applies
             *     Each view pertains to a unique graph node.
             *     Example: ["facility", "multishuttle"] */
            views: string[];
            /** @description Conditions an event must meet for this metric to be processed */
            matchConditions: components["schemas"]["MatchConditions"];
            /** @description The type of metric being processed (node or edge) */
            configType: components["schemas"]["ConfigType"];
            /** @description The type of graph database operation */
            graphOperation?: components["schemas"]["GraphOperation"];
            /** @description Optional parameters required for metric processing
             *     Example: {"rowHash": "{rowHash}"} */
            redisParams?: components["schemas"]["RedisParams"];
            /** @description The label for the node. Defaults to "Area" */
            label?: components["schemas"]["NodeLabel"];
            /** @description Defines the parent node hierarchy for this metric */
            parentNodes?: string[];
            /** @description Defines how to dynamically generate metric names based on source fields.
             *     Note: Either this or node_name should be provided, but not both. */
            nameFormula?: components["schemas"]["NameConfig"];
            /** @description The type of fact this metric represents */
            factType?: string;
            /** @description The source system of the facts
             *     Example: "diq" */
            sourceSystem: string;
            /** @description Display name for the UI */
            displayName: string;
            /**
             * Format: double
             * @description Acceptable low range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableLowRange?: number;
            /**
             * Format: double
             * @description Acceptable high range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableHighRange?: number;
            /** @description Example message for testing */
            exampleMessage?: unknown;
            /** @description Whether the metric is enabled
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is enabled for that facility
             *     Can be undefined for default configurations if no facilities have enabled/disabled the metric
             *     Can be null for custom configurations where enabled status is not applicable */
            enabled?: (boolean | components["schemas"]["Record_string.boolean_"]) | null;
            /** @description Whether the metric is active
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is active for that facility
             *     Can be undefined for default configurations if no facilities have active/inactive status */
            active?: boolean | components["schemas"]["Record_string.boolean_"];
            /** @description Whether this is a custom configuration (true) or default configuration (false) */
            isCustom: boolean;
        };
        /** @description Node Metric Configuration */
        NodeMetricConfigDetail: components["schemas"]["BaseMetricConfigDetail"] & {
            /** @description Static node name (alternative to nameFormula)
             *     Note: Either this or nameFormula should be provided, but not both.
             *     If provided, nameFormula should not be used. */
            nodeName?: string;
            /** @description Optional units for the metric
             *     Example: "/hr" */
            metricUnits?: string;
            /** @description The Redis method to be performed
             *     Example: "eventSet" */
            redisOperation: components["schemas"]["RedisOperation"];
            /** @description The aggregation method for the metric */
            aggregation: components["schemas"]["Aggregation"];
            /** @description The time window for the metric */
            timeWindow: components["schemas"]["TimeWindow"];
            /** @description The type of metric being measured
             *     Example: "stockTime" */
            metricType: string;
            /** @description The type of operation performed on the graph database
             *     Example: "area_node" */
            graphOperation: components["schemas"]["GraphOperation"];
            /** @enum {string} */
            configType: "node";
        };
        /** @description Inbound Edge Metric Configuration
         *     Tracks movement INTO an area */
        InboundEdgeMetricConfigDetail: components["schemas"]["BaseMetricConfigDetail"] & {
            /** @description The parent nodes for the inbound area */
            inboundParentNodes?: string[];
            /** @description Optional units for the metric
             *     Default: "units/hr" */
            metricUnits?: string;
            /** @description The type of operation performed on the graph database
             *     Example: "area_node" */
            graphOperation: components["schemas"]["GraphOperation"];
            /** @description The Redis operation performed upon arrival
             *     Example: "eventSet" */
            redisOperation: components["schemas"]["RedisOperation"];
            /** @description The name of the area where the handling unit is arriving
             *     Example: "multishuttle" */
            inboundArea: string;
            /** @description The key in event data that identifies the handling unit
             *     Example: "handlingUnitCode" */
            huId: string;
            /** @enum {string} */
            configType: "inbound-edge";
        };
        /** @description Outbound Edge Metric Configuration
         *     Tracks movement OUT OF an area */
        OutboundEdgeMetricConfigDetail: components["schemas"]["BaseMetricConfigDetail"] & {
            /** @description The parent nodes for the outbound area */
            outboundParentNodes?: string[];
            /** @description The type of units being counted
             *     Example: "handlingUnit" */
            units: string;
            /** @description The key in event data that identifies the handling unit
             *     Example: "handlingUnitCode" */
            huId: string;
            /** @description The name of the area the handling unit is leaving
             *     Example: "multishuttle" */
            outboundArea: string;
            /** @enum {string} */
            configType: "outbound-edge";
        };
        /** @description Complete Edge Metric Configuration
         *     Tracks movement BETWEEN two areas */
        CompleteEdgeMetricConfigDetail: {
            /** @description Identifier for the metric config
             *     Example: multishuttleAisleStorageLocationDistributionAvailable */
            metricConfigName: string;
            /** @description The hierarchy graph view at which this metric applies
             *     Each view pertains to a unique graph node.
             *     Example: ["facility", "multishuttle"] */
            views: string[];
            /** @description Conditions an event must meet for this metric to be processed */
            matchConditions: components["schemas"]["MatchConditions"];
            /**
             * @description The type of metric being processed (node or edge)
             * @enum {string}
             */
            configType: "complete-edge";
            /** @description The type of operation performed on the graph database
             *     Example: "area_node" */
            graphOperation: components["schemas"]["GraphOperation"];
            /** @description Optional parameters required for metric processing
             *     Example: {"rowHash": "{rowHash}"} */
            redisParams?: components["schemas"]["RedisParams"];
            /** @description The label for the node. Defaults to "Area" */
            label?: components["schemas"]["NodeLabel"];
            /** @description Defines the parent node hierarchy for this metric */
            parentNodes?: string[];
            /** @description Defines how to dynamically generate metric names based on source fields.
             *     Note: Either this or node_name should be provided, but not both. */
            nameFormula?: components["schemas"]["NameConfig"];
            /** @description The type of fact this metric represents */
            factType?: string;
            /** @description The source system of the facts
             *     Example: "diq" */
            sourceSystem: string;
            /** @description Display name for the UI */
            displayName: string;
            /**
             * Format: double
             * @description Acceptable low range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableLowRange?: number;
            /**
             * Format: double
             * @description Acceptable high range
             *     Used to indicate if the actual metric value is acceptable
             */
            acceptableHighRange?: number;
            /** @description Example message for testing */
            exampleMessage?: unknown;
            /** @description Whether the metric is enabled
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is enabled for that facility
             *     Can be undefined for default configurations if no facilities have enabled/disabled the metric
             *     Can be null for custom configurations where enabled status is not applicable */
            enabled?: (boolean | components["schemas"]["Record_string.boolean_"]) | null;
            /** @description Whether the metric is active
             *     For default configurations, this is an hstore where keys are facility IDs and values are booleans
             *     For custom configurations, this is a boolean indicating if the metric is active for that facility
             *     Can be undefined for default configurations if no facilities have active/inactive status */
            active?: boolean | components["schemas"]["Record_string.boolean_"];
            /** @description Whether this is a custom configuration (true) or default configuration (false) */
            isCustom: boolean;
            /** @description The name of the area where the handling unit is arriving
             *     Example: "multishuttle" */
            inboundArea: string;
            /** @description The name of the area the handling unit is leaving
             *     Example: "multishuttle" */
            outboundArea: string;
            /** @description The Redis operation performed
             *     Example: "eventSet" */
            redisOperation: components["schemas"]["RedisOperation"];
            /** @description The label for the outbound node
             *     Default: "Area" */
            outboundNodeLabel?: string;
            /** @description The parent nodes for the inbound area */
            inboundParentNodes?: string[];
            /** @description The parent nodes for the outbound area */
            outboundParentNodes?: string[];
            /** @description Optional units for the metric
             *     Default: "units/hr" */
            metricUnits?: string;
        };
        /** @description Union type for all metric configurations (API request/response format) */
        MetricConfigDetail: components["schemas"]["NodeMetricConfigDetail"] | components["schemas"]["InboundEdgeMetricConfigDetail"] | components["schemas"]["OutboundEdgeMetricConfigDetail"] | components["schemas"]["CompleteEdgeMetricConfigDetail"];
        AppConfigSettingArrayOrAppConfigSetting: components["schemas"]["AppConfigSetting"][] | components["schemas"]["AppConfigSetting"];
        DefaultConfigSeedingResult: {
            successful: components["schemas"]["AppConfigSetting"][];
            unsuccessful: components["schemas"]["AppConfigSetting"][];
            existing: components["schemas"]["AppConfigSetting"][];
        };
        DeletedRecordsResult: {
            /** Format: double */
            recordsDeleted: number;
            message?: string;
        };
        /** @description Relevant informaiton about a setting from the setting log */
        AppConfigSettingLog: {
            changedBy: string;
            /** Format: date-time */
            timestamp: string;
            source: string;
            newValue?: string;
        };
        AppConfigSettingLogsData: {
            data: components["schemas"]["AppConfigSettingLog"][];
        };
        /** @description DTO for a the facility config settings with its value set to the most relevant value for the user. */
        FacilityConfigSettings: components["schemas"]["AppConfigSetting"][];
        /** @enum {string} */
        CuratedTableColumnValueRenderType: "number" | "string" | "boolean" | "null";
        CuratedTableColumn: {
            name: string;
            renderType: components["schemas"]["CuratedTableColumnValueRenderType"];
            value: unknown;
        };
        CuratedTableRow: {
            columns: components["schemas"]["CuratedTableColumn"][];
        };
        AppConfigApiEndpointConfig: {
            operationId: string;
            baseUrl: string;
        };
        AppConfigApi: {
            baseUrl: string;
            overrides?: components["schemas"]["AppConfigApiEndpointConfig"][];
        };
        AppConfigDataUrls: {
            tableauProxy: string;
            tableauMovementsAndFaultsDashboard: string;
            tableauMovementsAndFaultsDetailsDashboard: string;
            tableauMovementsDashboard: string;
            tableauHistoricalMovementsAndFaultsDashboard: string;
            tableauShuttleHardwareAnalysis: string;
            mockApi: string;
            insightsSiteName: string;
        };
        /** @enum {number} */
        LogLevel: 20 | 30 | 40 | 50 | 60 | 100 | 1000;
        AppConfigSettings: {
            logLevel: components["schemas"]["LogLevel"];
        };
        AppConfigSite: {
            timezone: string;
        };
        /** @description Interface used to set the menu group index (order) for the menu. */
        AppConfigPackage: {
            /** @description The name of the package, which is used to dynamically load the package as it must match the folder name. */
            name: string;
            /** @description lag to set if the package is enabled. */
            enabled: boolean;
            /** @description an array of roles that match auth0 roles */
            requiredRoles?: string[];
        };
        AppConfig: {
            api: components["schemas"]["AppConfigApi"];
            dataUrls?: components["schemas"]["AppConfigDataUrls"];
            settings?: components["schemas"]["AppConfigSettings"];
            site: components["schemas"]["AppConfigSite"];
            packages?: components["schemas"]["AppConfigPackage"][];
            featureFlags?: components["schemas"]["AppConfigSetting"][];
        };
        /** @enum {string} */
        Recommended: "is_recommended" | "not_recommended" | "not_provided";
        AIMLResponse: {
            Error?: string;
            KnownDB?: string;
            /** Format: double */
            ResponseCode: number;
            echart_code?: unknown;
            generated_sql_query?: string;
            question?: string;
            sql_query_output?: string;
            summary_response?: string;
        };
        DataExplorerResult: {
            debugData?: components["schemas"]["AIMLResponse"];
            eChartCode?: unknown;
            /** Format: date-time */
            timestamp: string;
            queryResults?: unknown[];
            isRecommended: components["schemas"]["Recommended"];
            isBookmarked?: boolean;
            answer?: string;
            prompt: string;
            id: string;
        };
        DataExplorerResults: {
            data: components["schemas"]["DataExplorerResult"][];
        };
        DataExplorerRecommendationsData: {
            prompt: string;
        };
        DataExplorerRecommendations: {
            recommendations: components["schemas"]["DataExplorerRecommendationsData"][];
        };
        DataExplorerAgentSearchResponse: {
            sessionId: string;
            status: string;
            messages: string[];
        };
        /**
         * @description DTO that describes the Diagnostics Infrastructure Object Status.
         * @enum {string}
         */
        DiagnosticsInfrastructureObjectStatus: "Online" | "Offline";
        /** @description DTO that describes the Diagnostics Infrastructure Object. */
        DiagnosticsInfrastructureObject: {
            name: string;
            lastUpdate: string;
            status: string | components["schemas"]["DiagnosticsInfrastructureObjectStatus"];
            /** Format: double */
            cpuPercentage: number;
            /** Format: double */
            memoryUsagePercentage: number;
            /** Format: double */
            diskUsagePercentage: number;
            labels: string[];
        };
        DiagnosticsInfrastructureContract: components["schemas"]["DiagnosticsInfrastructureObject"][];
        /** @description DTO that defines the relevant details of the inventory upload history object. */
        InventoryUploadRecentActivity: {
            date: string;
            /** Format: double */
            knownOrderCount: number;
            /** Format: double */
            knownOrderLineCount: number;
        };
        /** @description DTO that defines the relevant details the inventory storage utilization tile. */
        InventoryStorageUtilization: {
            /** Format: double */
            utilizationPercentage: number;
        };
        /** @description Chart series data with percentage validation */
        ChartSeriesPercentageData: {
            name: string;
            /** Format: double */
            value: number;
            isValidPercentage: boolean;
        };
        InventoryStockDistributionUnderData: {
            underInventory: components["schemas"]["ChartSeriesPercentageData"][];
        };
        InventoryStockDistributionOverData: {
            overInventory: components["schemas"]["ChartSeriesPercentageData"][];
        };
        InventoryStockDistributionNoData: {
            noInventory: components["schemas"]["ChartSeriesPercentageData"][];
        };
        InventoryStockDistributionAtData: {
            atInventory: components["schemas"]["ChartSeriesPercentageData"][];
        };
        InventorySku: {
            sku: string;
            description: string;
            /** Format: double */
            daysOnHand: number;
            status: string;
            /** Format: double */
            averageDailyQuantity: number;
            /** Format: double */
            averageDailyOrders: number;
            /** Format: double */
            targetMultiplicity: number;
            velocityClassification: string;
            /** Format: double */
            quantityAvailable: number;
            /** Format: double */
            quantityAllocated: number;
            /** Format: double */
            totalQuantity: number;
            /** Format: double */
            locations: number;
            /** Format: double */
            skuPositions: number;
            /** Format: double */
            maxContainers: number;
            /** Format: double */
            contOverage: number;
            latestInventorySnapshotTimestamp?: string;
            latestCycleCountTimestamp: string;
            latestActivityDateTimestamp: string;
        };
        InventorySkuPaginationInfo: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_InventorySku-Array.InventorySkuPaginationInfo_": {
            metadata: components["schemas"]["InventorySkuPaginationInfo"];
            data: components["schemas"]["InventorySku"][];
        };
        PostInventorySkusListResponse: components["schemas"]["ApiResponseArray_InventorySku-Array.InventorySkuPaginationInfo_"];
        /** @description Defines a column to add to a ORDER BY clause in a SQL statement. */
        SortField: {
            columnName: string;
            isDescending: boolean;
        };
        PaginatedRequestNoDates: {
            filters?: unknown;
            sortFields?: components["schemas"]["SortField"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            limit?: number;
            searchString?: string;
        };
        /** @description DTO for objects related to the excel export. */
        IncludedColumns: {
            [key: string]: boolean;
        };
        /** @description Base chart series data */
        ChartSeriesData: {
            name: string;
            /** Format: double */
            value: number;
        };
        TaskTypeData: {
            demand?: components["schemas"]["ChartSeriesData"][];
            topOff?: components["schemas"]["ChartSeriesData"][];
            relocation?: components["schemas"]["ChartSeriesData"][];
        };
        /** @description DTO that defines the relevant details of the inventory replenishment details. */
        ShiftData: {
            firstShift: components["schemas"]["ChartSeriesData"][];
            secondShift?: components["schemas"]["ChartSeriesData"][];
            thirdShift?: components["schemas"]["ChartSeriesData"][];
        };
        InventoryReplenishmentDetails: {
            dailyReplenishments: components["schemas"]["ChartSeriesData"][];
            dailyPendingOrders: components["schemas"]["ChartSeriesData"][];
            dailyCycleTimes: components["schemas"]["ChartSeriesData"][];
            shiftData: components["schemas"]["ShiftData"][];
            shiftTimes: Record<string, never>;
        };
        NodeAlert: {
            color: string;
            value: string;
        };
        Metric: {
            id: string;
            type: string;
            value: (number | string) | null;
            panelGroup?: string;
            units?: string;
            description?: string;
        };
        AreaPosition: {
            /** Format: double */
            x: number;
            /** Format: double */
            y: number;
        };
        Shuttle: {
            id: string;
            name: string;
            metrics: components["schemas"]["Metric"][];
        };
        AisleLevel: {
            id: string;
            label: string;
            shuttles: components["schemas"]["Shuttle"][];
        };
        Area: {
            id: string;
            alerts?: components["schemas"]["NodeAlert"][];
            label: string;
            metrics: components["schemas"]["Metric"][];
            position: components["schemas"]["AreaPosition"];
            /** @enum {string} */
            nodeType: "Area" | "Aisle" | "Lift";
            aisleLevels?: components["schemas"]["AisleLevel"][];
            view?: string;
        };
        /** @enum {string} */
        EdgeDirection: "upstream" | "downstream";
        Edge: {
            id: string;
            source: string;
            target: string;
            direction: components["schemas"]["EdgeDirection"];
            metrics: components["schemas"]["Metric"][];
        };
        ProcessFlowResponse: {
            areas: components["schemas"]["Area"][];
            edges: components["schemas"]["Edge"][];
            lastProcessedTime: string;
        };
        MetricGroup: {
            title: string;
            metrics: components["schemas"]["Metric"][];
        };
        ProcessFlowDetailsResponse: {
            metricGroups: components["schemas"]["MetricGroup"][];
        };
        UpdatedArea: {
            /** Format: double */
            identity: number;
            labels: string[];
            properties: {
                [key: string]: string | string[] | number;
            };
            elementId: string;
        };
        UpdatePositionPayload: {
            id: string;
            position: {
                /** Format: double */
                y: number;
                /** Format: double */
                x: number;
            };
        };
        Boolean: Record<string, never>;
        ClearCacheResponse: {
            success: components["schemas"]["Boolean"];
        };
        ClearCacheRequest: {
            /** @enum {string} */
            scope: "all" | "metrics" | "graph";
        };
        InventoryPerformanceSeriesData: {
            name: string;
            series: components["schemas"]["ChartSeriesData"][];
        };
        InventoryPerformanceSeriesContract: components["schemas"]["InventoryPerformanceSeriesData"][];
        InventoryHighImpactSKU: {
            sku: string;
            /** Format: double */
            accuracy: number;
            storageArea: string;
            /** Format: double */
            quantity: number;
            /** Format: double */
            cubeUtilization: number;
            /** Format: double */
            daysOnHand: number;
        };
        InventoryHighImpactSkuPaginationInfo: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_InventoryHighImpactSKU-Array.InventoryHighImpactSkuPaginationInfo_": {
            metadata: components["schemas"]["InventoryHighImpactSkuPaginationInfo"];
            data: components["schemas"]["InventoryHighImpactSKU"][];
        };
        InventoryHighImpactSKUContract: components["schemas"]["ApiResponseArray_InventoryHighImpactSKU-Array.InventoryHighImpactSkuPaginationInfo_"];
        PaginatedRequest: {
            /** Format: date-time */
            start_date: string;
            /** Format: date-time */
            end_date: string;
            filters?: unknown;
            sortFields?: components["schemas"]["SortField"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            limit?: number;
            searchString?: string;
        };
        /** @description Data to hold in a contract for Handling Units Trayed. */
        InventoryHandlingUnitsTrayedData: {
            /** Format: double */
            handlingUnitsTrayed: number;
        };
        /** @description Configuration to hold in a contract for Handling Units Trayed. */
        InventoryHandlingUnitsTrayedConfig: {
            /** Format: double */
            timePeriodInHours: number;
        };
        /** @description Contract for Handling Units Trayed. */
        InventoryHandlingUnitsTrayedContract: {
            metadata: components["schemas"]["InventoryHandlingUnitsTrayedConfig"];
            data: components["schemas"]["InventoryHandlingUnitsTrayedData"];
        };
        /** @description DTO that defines the relevant details of the inventory forecast objects. */
        InventoryForecastDataAnalysisTimestamp: {
            dataUpdateTimestamp?: string;
            analysisPerformedTimestamp: string;
        };
        CurrentInventory: {
            /** Format: double */
            forwardPick: number;
            /** Format: double */
            reserveStorage: number;
        };
        ProjectedInventory: {
            /** Format: double */
            projectedForwardPick: number;
            /** Format: double */
            allocatedOrders: number;
            /** Format: double */
            pendingPicks: number;
            /** Format: double */
            pendingReplenishment: number;
        };
        ForecastedInventory: {
            /** Format: double */
            twoDayForwardPick: number | null;
            /** Format: double */
            twoDayDemand: number | null;
            /** Format: double */
            forwardPickTomorrow: number | null;
            /** Format: double */
            knownDemand: number | null;
            /** Format: double */
            demandTomorrow: number | null;
            /** Format: double */
            averageDemand: number;
            /** Format: double */
            averageReplenishment: number;
        };
        InventoryForecastListing: {
            forecast: components["schemas"]["ForecastedInventory"];
            projected: components["schemas"]["ProjectedInventory"];
            current: components["schemas"]["CurrentInventory"];
            sku: string;
        };
        InventoryForecastListingConfig: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_InventoryForecastListing-Array.InventoryForecastListingConfig_": {
            metadata: components["schemas"]["InventoryForecastListingConfig"];
            data: components["schemas"]["InventoryForecastListing"][];
        };
        InventoryForecastListingData: components["schemas"]["ApiResponseArray_InventoryForecastListing-Array.InventoryForecastListingConfig_"];
        InventoryForecastSkuLocationDetails: {
            lastActivityDate: string;
            zone: string;
            conditionCode: string;
            containerDimensions?: string;
            skuSize?: string;
            uom?: string;
            /** Format: double */
            containerQuantity?: number;
            /** Format: double */
            quantity: number;
            /** Format: double */
            containerCount: number;
            containerId: string | null;
            locationType: string;
            locationId: string;
        };
        InventoryForecastSkuLocationByArea: {
            details: components["schemas"]["InventoryForecastSkuLocationDetails"][];
            area: string;
        };
        InventoryForecastSkuLocationAreas: {
            data: components["schemas"]["InventoryForecastSkuLocationByArea"][];
        };
        InventoryForecastSkuOrderDetails: {
            /** Format: double */
            allocatedQty: number;
            /** Format: double */
            orderLines: number;
            shipDate: string;
            allocationDate: string;
            priority: string;
            orderId: string;
            skuId: string;
        };
        InventoryForecastSkuOrders: {
            skuId: string;
            /** Format: double */
            openOrderCount: number;
            data: components["schemas"]["InventoryForecastSkuOrderDetails"][];
        };
        InventoryKnownDemand: {
            /** Format: double */
            percentage: number;
            /** Format: double */
            quantity: number;
        };
        InventorySkuForecastDetails: {
            /** Format: double */
            confidence: number;
            knownDemand: components["schemas"]["InventoryKnownDemand"];
            /** Format: double */
            shortTermDaily: number;
            /** Format: double */
            shortTermDailyDays: number;
            /** Format: double */
            longTermDaily: number;
            /** Format: double */
            longTermDailyDays: number;
            /** Format: double */
            nonZeroDemand: number;
            /** Format: double */
            zeroDemandIntermittency: number;
            predictedDemandSeries: components["schemas"]["ChartSeriesData"][];
            actualDemandSeries: components["schemas"]["ChartSeriesData"][];
            confidenceLowSeries: components["schemas"]["ChartSeriesData"][];
            confidenceHighSeries: components["schemas"]["ChartSeriesData"][];
        };
        /** @description DTO that defines the relevant details the inventory area dropdown. */
        InventoryAreaFilterDefinition: {
            areaFilterTable: string[];
        };
        FlattenedInventoryContainerData: {
            free_cycle_count: string | null;
            data_updated: string | null;
            last_cycle_count: string | null;
            last_activity_date: string | null;
            /** Format: double */
            quantity: number;
            sku: string;
            zone: string;
            location_id: string;
            container_id: string;
        };
        InventoryContainerPaginationInfo: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_FlattenedInventoryContainerData-Array.InventoryContainerPaginationInfo_": {
            metadata: components["schemas"]["InventoryContainerPaginationInfo"];
            data: components["schemas"]["FlattenedInventoryContainerData"][];
        };
        PostInventoryContainersListResponse: components["schemas"]["ApiResponseArray_FlattenedInventoryContainerData-Array.InventoryContainerPaginationInfo_"];
        FlattenedInventoryContainerEventsDataItem: {
            timestamp: string;
            event: string;
            destinationContainer: string;
            operator: string;
            /** Format: double */
            quantity: number;
            workstationCode: string;
            sku: string;
        };
        InventoryContainerEventsPaginationInfo: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_FlattenedInventoryContainerEventsDataItem-Array.InventoryContainerEventsPaginationInfo_": {
            metadata: components["schemas"]["InventoryContainerEventsPaginationInfo"];
            data: components["schemas"]["FlattenedInventoryContainerEventsDataItem"][];
        };
        PostInventoryContainerEventsListResponse: components["schemas"]["ApiResponseArray_FlattenedInventoryContainerEventsDataItem-Array.InventoryContainerEventsPaginationInfo_"];
        /** @description DTO that defines the relevant details of the bin location objects */
        InventoryBinLocation: {
            binLocation: string;
            /** @enum {string} */
            locationSide: "Left" | "Right";
            /** @enum {string} */
            status: "Occupied" | "Empty";
            containerType?: string;
            skus: {
                /** Format: double */
                quantity: number;
                sku: string;
            }[];
        };
        InventoryBinLocationsResponse: {
            data: components["schemas"]["InventoryBinLocation"][];
        };
        AdvicesOutstandingData: {
            /** Format: double */
            outstandingAdvices: number;
        };
        AdvicesListData: {
            adviceId: string;
            adviceStatus: string;
            ownerId: string;
            adviceType: string;
            supplierId: string;
            createdTime: string;
            startTime: string | null;
            finishedTime: string | null;
            /** Format: double */
            adviceLines: number;
            /** Format: double */
            deliveredLines: number;
            /** Format: double */
            overdeliveredLines: number;
            /** Format: double */
            underdeliveredLines: number;
        };
        AdvicesList: {
            adviceList: components["schemas"]["AdvicesListData"][];
        };
        InventoryAdvicesInProgressData: {
            /** Format: double */
            inProgressAdvices: number;
        };
        AdvicesFinishedData: {
            /** Format: double */
            finishedAdvices: number;
        };
        AdviceDetailsData: {
            itemsReceived: {
                packagingLevel: string;
                /** Format: double */
                quantity: number;
                sku: string;
                adviceLine: string;
                handlingUnitType: string;
                handlingUnit: string;
            }[];
            supplierId: string;
        };
        AdvicesCycleTimeData: {
            /** Format: double */
            cycleTime: number;
        };
        /** @description DTO that defines the relevant details of the inventory accuracy object. */
        InventoryAccuracy: {
            /** Format: double */
            accuracy: number;
            status: string;
            areaFilter: string;
        };
        InventoryAccuracyConfig: {
            /** Format: double */
            seg1: number;
            /** Format: double */
            seg2: number;
            /** Format: double */
            seg3: number;
        };
        "ApiResponse_InventoryAccuracy.InventoryAccuracyConfig_": components["schemas"]["InventoryAccuracy"] & {
            metadata: components["schemas"]["InventoryAccuracyConfig"];
        };
        InventoryContainerEventsKpiData: {
            /** Format: double */
            averageDailyPickEvents: number;
            /** Format: double */
            pickEventsToday: number;
            /** Format: double */
            averageDailyCycleCount: number;
            /** Format: double */
            cycleCountEventsToday: number;
            dataUpdated: string | null;
            lastCycleCount: string | null;
            lastActivityDate: string | null;
            /** Format: double */
            quantity: number;
            sku: string;
            zone: string;
            locationId: string;
            containerId: string;
        };
        InventoryContainerEventsKpiContract: {
            events: components["schemas"]["InventoryContainerEventsKpiData"][];
        };
        EquipmentWorkstationStarvedBlockedTimeSeriesData: {
            blockedTime: components["schemas"]["ChartSeriesData"][];
            starvedTime: components["schemas"]["ChartSeriesData"][];
        };
        BaseChartConfig: {
            /**
             * Format: double
             * @description Min value for the y-axis
             */
            yMin?: number;
            /**
             * Format: double
             * @description Max value for the y-axis
             */
            yMax?: number;
            /**
             * Format: double
             * @description Value for the high range
             */
            yHighBand?: number;
            /**
             * Format: double
             * @description Value for the low range
             */
            yLowBand?: number;
            /** @description What the X-axis value should be formatted for display in the chart */
            xAxisValueFormatType?: string;
            /** @description What the Y-axis value should be formatted for display in the chart */
            yAxisValueFormatType?: string;
        };
        /** @description Config for a series in chart */
        SeriesConfig: {
            /**
             * Format: double
             * @description Min value for the y-axis
             */
            yMin?: number;
            /**
             * Format: double
             * @description Max value for the y-axis
             */
            yMax?: number;
            /**
             * Format: double
             * @description Value for the high range
             */
            yHighBand?: number;
            /**
             * Format: double
             * @description Value for the low range
             */
            yLowBand?: number;
            /** @description What the X-axis value should be formatted for display in the chart */
            xAxisValueFormatType?: string;
            /** @description What the Y-axis value should be formatted for display in the chart */
            yAxisValueFormatType?: string;
            type?: string;
            color?: string;
            icon?: string;
        };
        EquipmentWorkstationStarvedBlockedTimeSeriesConfig: components["schemas"]["BaseChartConfig"] & {
            blockedTime: components["schemas"]["SeriesConfig"];
            starvedTime: components["schemas"]["SeriesConfig"];
        };
        "ApiResponse_EquipmentWorkstationStarvedBlockedTimeSeriesData.EquipmentWorkstationStarvedBlockedTimeSeriesConfig_": components["schemas"]["EquipmentWorkstationStarvedBlockedTimeSeriesData"] & {
            metadata: components["schemas"]["EquipmentWorkstationStarvedBlockedTimeSeriesConfig"];
        };
        EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse: components["schemas"]["ApiResponse_EquipmentWorkstationStarvedBlockedTimeSeriesData.EquipmentWorkstationStarvedBlockedTimeSeriesConfig_"];
        /** @description Expected return data object format for the response */
        EquipmentWorkstationOperatorActivityData: {
            /** @description Id of the operator */
            operatorId: string;
            /** @description Time the operator started working at the workstation */
            startTime: string;
            /** @description Time the operator stopped working at the workstation, null if operator is still active */
            endTime: string | null;
            /**
             * Format: double
             * @description How long the operator was/currently has been at the workstation, in seconds
             */
            totalTimeDuration: number;
            /**
             * Format: double
             * @description Time the workstation was idle, in seconds
             */
            idleTime: number;
            /**
             * Format: double
             * @description Time the workstation was starved for picks, in seconds
             */
            starvedTime: number;
            /**
             * Format: double
             * @description Time the workstation was blocked for picks, in seconds
             */
            blockedTime: number;
            /**
             * Format: double
             * @description The workstations per hour line rates
             */
            lineRatePerHour: number;
            /**
             * Format: double
             * @description The workstations per hour weighted line rates
             */
            weightedLineRatePerHour: number;
        };
        EquipmentWorkstationLineRatesSeriesData: {
            lineRates: components["schemas"]["ChartSeriesData"][];
        };
        EquipmentWorkstationAisleMovementData: {
            /** Format: double */
            retrieval: number;
            /** Format: double */
            storage: number;
            /** Format: double */
            positioning: number;
            /** Format: double */
            iat: number;
            /** Format: double */
            shuffle: number;
            /** Format: double */
            bypass: number;
        };
        /** @description Definiton of the data for the data contract for workstation active faults */
        EquipmentWorkstationActiveFaultsData: {
            /** @description Description of the fault */
            description: string;
            /** @description Start time of the fault */
            startTime: string;
            /**
             * Format: double
             * @description How long the fault has been active, in seconds
             */
            duration: number;
            /** @description Fault ID */
            faultId: string;
        };
        /**
         * @description DTO of the necessary data to display a health status.
         * @enum {string}
         */
        HealthStatus: "ok" | "caution" | "critical" | "warning";
        EquipmentSummaryWorkstationDefinition: {
            status: components["schemas"]["HealthStatus"];
            /** Format: double */
            operatorId: number;
            /** Format: double */
            iatDonorTotes: number;
            /** Format: double */
            linesPerHour: number;
            activeTime: string;
            idleTime: string;
            starvedTime: string;
        };
        WorkstationMetricConfiguration: {
            id: string;
            label: string;
            type: string;
            format: string;
            display: boolean;
        };
        EquipmentSummaryWorkstationConfigDefinition: {
            metrics: components["schemas"]["WorkstationMetricConfiguration"][];
        };
        "ApiResponse_EquipmentSummaryWorkstationDefinition.EquipmentSummaryWorkstationConfigDefinition_": components["schemas"]["EquipmentSummaryWorkstationDefinition"] & {
            metadata: components["schemas"]["EquipmentSummaryWorkstationConfigDefinition"];
        };
        /** @enum {string} */
        HealthStatusValue: "ok" | "caution" | "critical" | "warning";
        /** @description DTO of the necessary data to display an Alert Status. */
        AlertStatus: {
            /** @description Status of area. */
            status: components["schemas"]["HealthStatusValue"];
            /** @description Identifies the equipment, area, or location related to this alert
             *     TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory */
            identifier?: string;
        };
        /** @description DTO of the necessary base data to display a Facility Area. */
        AreaOperators: {
            /**
             * Format: double
             * @description Number of operators actively working in area.
             */
            activeOperators: number;
            /**
             * Format: double
             * @description Lowest number of recommended operators for the area.
             */
            lowRecOperators: number;
            /**
             * Format: double
             * @description Highest number of recommended operators for the area.
             */
            highRecOperators: number;
            /**
             * Format: double
             * @description Max number of recommended operators for the area.
             */
            maxOperators?: number;
            /** @description Alert status object. */
            alertStatus?: components["schemas"]["AlertStatus"];
            /**
             * Format: double
             * @description Number of orders in queue.
             */
            queuedOrders?: number;
            /**
             * Format: double
             * @description Orders picked per operator per hour.
             */
            pickRate?: number;
            operatorStatus?: components["schemas"]["HealthStatus"];
        };
        /** @description DTO that describes the equipment summary area data. */
        EquipmentSummaryAreaDefinition: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus?: components["schemas"]["AlertStatus"];
            status: components["schemas"]["HealthStatus"];
            /** Format: double */
            movementsPerFault: number;
            /** Format: double */
            outboundRatePerHour: number;
            /** Format: double */
            downTimeMinutes: number;
            /** Format: double */
            qualityPercentage: number;
            alerts?: components["schemas"]["AlertStatus"][];
        };
        /** @description DTO that describes the areas summary data. */
        EquipmentSummaryAreaContract: {
            areas: components["schemas"]["EquipmentSummaryAreaDefinition"][];
        };
        EquipmentSummaryAisleDefinition: {
            status: components["schemas"]["HealthStatus"];
            /** Format: double */
            totalMovements: number;
            /** Format: double */
            storageUtilization: number;
            /** Format: double */
            storedTotesPerHour: number;
            /** Format: double */
            inventoryTotes: number;
            /** Format: double */
            retrievedTotesPerHour: number;
            /** Format: double */
            orderTotes: number;
        };
        AisleMetricConfiguration: {
            id: string;
            label: string;
            type: string;
            format: string;
            display: boolean;
        };
        EquipmentSummaryAisleConfigDefinition: {
            metrics: components["schemas"]["AisleMetricConfiguration"][];
        };
        "ApiResponse_EquipmentSummaryAisleDefinition.EquipmentSummaryAisleConfigDefinition_": components["schemas"]["EquipmentSummaryAisleDefinition"] & {
            metadata: components["schemas"]["EquipmentSummaryAisleConfigDefinition"];
        };
        /** @description Timestamp class for BigQuery. */
        BigQueryTimestamp: {
            value: string;
        };
        RecentEventQueryResponse: {
            time: components["schemas"]["BigQueryTimestamp"];
            tenant: string;
            facility: string;
            description: string;
            eventType: string;
            equipmentId: string;
            eventStartTime: components["schemas"]["BigQueryTimestamp"];
            /** Format: double */
            totalTime: number;
            faultCode: string;
        };
        RecentEventQueryConfig: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults: number;
        };
        "ApiResponseArray_RecentEventQueryResponse-Array.RecentEventQueryConfig_": {
            metadata: components["schemas"]["RecentEventQueryConfig"];
            data: components["schemas"]["RecentEventQueryResponse"][];
        };
        BaseFilterType: {
            /** @enum {string} */
            type: "multiple" | "single";
        };
        EquipmentOutboundRate: {
            /** Format: double */
            outboundDivertsHour: number;
            status: string;
        };
        EquipmentOutboundRateConfig: {
            /** Format: double */
            max: number;
            rangeColor: string;
        };
        "ApiResponse_EquipmentOutboundRate.EquipmentOutboundRateConfig_": components["schemas"]["EquipmentOutboundRate"] & {
            metadata: components["schemas"]["EquipmentOutboundRateConfig"];
        };
        /** @description Data contract for the response from the api */
        EquipmentOutboundRateApiResponse: components["schemas"]["ApiResponse_EquipmentOutboundRate.EquipmentOutboundRateConfig_"];
        FaultsStatusListRequest: {
            filters?: unknown;
            /** Format: date-time */
            end_date?: string;
            /** Format: date-time */
            start_date?: string;
        };
        /** @description Describes the equipment faults series data returned by the service. */
        EquipmentFaultsSeriesData: {
            /** @description Hour of the day that the throughput is measured. Eg: "16". */
            name: string;
            /**
             * Format: double
             * @description Number of faults for that hour.
             */
            value: number;
        };
        /** @description Describes the throughput rates series data returned by the service. */
        EquipmentThroughputSeriesData: {
            /** @description Hour of the day that the throughput is measured. Eg: "16". */
            name: string;
            /**
             * Format: double
             * @description Throughput for that hour.
             */
            value: number;
        };
        /** @description Describes the contract for the data returned by the api. */
        EquipmentFaultsSeriesContractData: {
            faultsOrderData: components["schemas"]["EquipmentFaultsSeriesData"][];
            orderData: components["schemas"]["EquipmentThroughputSeriesData"][];
        };
        /** @description DTO for a Fault Event */
        FaultEvent: {
            /** @description Time the event happened. UTC, ISO 8601. */
            time: string;
            /** @description Area that the fault occured in */
            area: string;
            /** @description Desciption of the fault event */
            description: string;
        };
        FaultEventContract: components["schemas"]["FaultEvent"][];
        FaultsMovementsSeriesData: {
            movementCounts: components["schemas"]["ChartSeriesData"][];
            movementsPerFault: components["schemas"]["ChartSeriesData"][];
        };
        FaultsMovementsSeriesRequest: {
            /** Format: date-time */
            start_date: string;
            /** Format: date-time */
            end_date: string;
            filters?: unknown;
        };
        EquipmentFaultsListItem: {
            timestamp: string;
            /** Format: double */
            durationMinutes: number;
            status: string;
            aisle: string;
            level: string;
            device: string;
            deviceType: string;
        };
        EquipmentFaultsPaginationInfo: {
            /** Format: double */
            page: number;
            /** Format: double */
            limit: number;
            /** Format: double */
            totalResults?: number;
        };
        "ApiResponseArray_EquipmentFaultsListItem-Array.EquipmentFaultsPaginationInfo_": {
            metadata: components["schemas"]["EquipmentFaultsPaginationInfo"];
            data: components["schemas"]["EquipmentFaultsListItem"][];
        };
        PostEquipmentFaultsListResponse: components["schemas"]["ApiResponseArray_EquipmentFaultsListItem-Array.EquipmentFaultsPaginationInfo_"];
        AvailableLevels: {
            availableLevels: string[];
        };
        FaultsLevelListRequest: {
            filters?: unknown;
            /** Format: date-time */
            end_date?: string;
            /** Format: date-time */
            start_date?: string;
        };
        FaultCountGroupedSeriesData: {
            faultCounts: components["schemas"]["ChartSeriesData"][];
        };
        FaultCountGroupedByRequest: {
            filters?: unknown;
            isAscendingOrder?: boolean;
            /** @enum {string} */
            groupByColumn: "aisle" | "level" | "device_functional_type" | "device_code" | "reason_name";
            /** Format: date-time */
            endDate: string;
            /** Format: date-time */
            startDate: string;
        };
        EquipmentFaultsDeviceIdRequest: {
            filters?: unknown;
            /** Format: date-time */
            end_date?: string;
            /** Format: date-time */
            start_date?: string;
        };
        FaultsDeviceTypeListRequest: {
            /** Format: date-time */
            start_date?: string;
            /** Format: date-time */
            end_date?: string;
            filters?: unknown;
        };
        /** @description DTO that describes the equipment faults data. */
        EquipmentFaultsDefinition: {
            /** Format: double */
            faults: number;
            status: string;
        };
        /** @description DTO that describes the equipment faults config data. */
        EquipmentFaultsConfigDefinition: {
            /** Format: double */
            lowRange?: number;
            /** Format: double */
            highRange?: number;
            /** Format: double */
            max: number;
        };
        "ApiResponse_EquipmentFaultsDefinition.EquipmentFaultsConfigDefinition_": components["schemas"]["EquipmentFaultsDefinition"] & {
            metadata: components["schemas"]["EquipmentFaultsConfigDefinition"];
        };
        FaultAvgDurationSeriesData: {
            avgDuration: components["schemas"]["ChartSeriesData"][];
        };
        FaultAvgDurationByStatusRequest: {
            filters?: unknown;
            isAscendingOrder?: boolean;
            /** Format: date-time */
            endDate: string;
            /** Format: date-time */
            startDate: string;
        };
        FaultsArea: {
            /** Format: double */
            totalFaults: number;
            /** Format: double */
            maxFaultsAllowed: number;
            /** Format: double */
            downtimeMinutes: number;
            status: string;
        };
        /** @description DTO that describes the fault areas data. */
        FaultsFacilityArea: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus?: components["schemas"]["AlertStatus"];
            faults: components["schemas"]["FaultsArea"];
        };
        /** @description Type that describes the return structure of the data contract response. */
        FaultsAreasResponse: {
            areas: components["schemas"]["FaultsFacilityArea"][];
        };
        /** @description DTO that describes the fault areas config data. */
        FaultsAreasConfig: {
            /** Format: double */
            min: number;
            /** Format: double */
            max: number;
        };
        "ApiResponse_FaultsAreasResponse.FaultsAreasConfig_": components["schemas"]["FaultsAreasResponse"] & {
            metadata: components["schemas"]["FaultsAreasConfig"];
        };
        AvailableAisles: {
            availableAisles: string[];
        };
        FaultsAisleListRequest: {
            filters?: unknown;
            /** Format: date-time */
            end_date?: string;
            /** Format: date-time */
            start_date?: string;
        };
        EquipmentActiveFaultsDefinition: {
            area: string;
            /** Format: double */
            duration: number;
            deviceReason: string;
        };
        /** @description DTO that defines the relevant details of the inventory recent activities. */
        EquipmentActiveFaults: {
            activeFaultsTable: components["schemas"]["EquipmentActiveFaultsDefinition"][];
        };
        FromArea: {
            area_id: string;
            /** Format: double */
            number_to_move: number;
        };
        RequestBody: {
            from_areas: components["schemas"]["FromArea"][];
            to_area_id: string;
        };
        OperatorsChartSeriesData: {
            operators: components["schemas"]["ChartSeriesData"][];
        };
        "ApiResponse_OperatorsChartSeriesData.BaseChartConfig_": components["schemas"]["OperatorsChartSeriesData"] & {
            metadata: components["schemas"]["BaseChartConfig"];
        };
        OperatorsChartSeriesApiResponse: components["schemas"]["ApiResponse_OperatorsChartSeriesData.BaseChartConfig_"];
        /** @description DTO of the necessary data to display total number of operators at a given time. */
        OperatorCountData: {
            /** Format: double */
            operatorCount: number;
            alertStatus: components["schemas"]["AlertStatus"];
        };
        OperatorCountsConfig: {
            startDateTime: string;
            endDateTime: string;
            /** Format: double */
            lowRecOperators: number;
            /** Format: double */
            highRecOperators: number;
            /** Format: double */
            maxOperators: number;
        };
        "ApiResponse_OperatorCountData.OperatorCountsConfig_": components["schemas"]["OperatorCountData"] & {
            metadata: components["schemas"]["OperatorCountsConfig"];
        };
        /** @description From T, pick a set of properties whose keys are in the union K */
        "Pick_AlertStatus.Exclude_keyofAlertStatus.status-or-message__": {
            /** @description Identifies the equipment, area, or location related to this alert
             *     TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory */
            identifier?: string;
        };
        OperatorAlertStatus: {
            /** @description Identifies the equipment, area, or location related to this alert
             *     TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory */
            identifier?: string;
            status: components["schemas"]["HealthStatusValue"];
            message?: string;
        };
        /** @description From T, pick a set of properties whose keys are in the union K */
        "Pick_FacilityArea.Exclude_keyofFacilityArea.alertStatus__": {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
        };
        OperatorFacilityArea: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus: components["schemas"]["OperatorAlertStatus"];
        };
        OrderShippedData: {
            /** Format: double */
            total: number;
            /** Format: double */
            current: number;
            /** Format: double */
            past: number;
            /** Format: double */
            change: number;
        };
        "ApiResponse_OrderShippedData.___": components["schemas"]["OrderShippedData"] & {
            metadata: Record<string, never>;
        };
        OrdersPickCycleCountData: {
            /** Format: double */
            cycleCount: number;
        };
        /** @description DTO that defines the relevant details for the current number of orders shipped. */
        OrdersShipped: {
            /** Format: double */
            total: number;
            /** Format: double */
            shipped: number;
        };
        "ApiResponse_OrdersShipped.___": components["schemas"]["OrdersShipped"] & {
            metadata: Record<string, never>;
        };
        /** @description DTO that defines the relevant details of the projected order fulfillment percentage. */
        OrderProgress: {
            /** Format: double */
            orderProgressPercentage: number;
        };
        OrderProgressConfig: {
            /** Format: double */
            seg1: number;
            /** Format: double */
            seg2: number;
            /** Format: double */
            seg3: number;
        };
        "ApiResponse_OrderProgress.OrderProgressConfig_": components["schemas"]["OrderProgress"] & {
            metadata: components["schemas"]["OrderProgressConfig"];
        };
        /** Format: double */
        CustomerOrderEstimatedCompletion: number;
        CustomerOrderEstimatedCompletionData: {
            estimatedCompletionMinutes: components["schemas"]["CustomerOrderEstimatedCompletion"];
        };
        /** @description DTO that describes the order throughput data. */
        OrderThroughputData: {
            /** Format: double */
            throughputRateLinesPerHour: number;
        };
        /** @description DTO that describes the order config data. */
        OrderThroughputConfig: {
            /** Format: double */
            lowRange: number;
            /** Format: double */
            max: number;
        };
        "ApiResponse_OrderThroughputData.OrderThroughputConfig_": components["schemas"]["OrderThroughputData"] & {
            metadata: components["schemas"]["OrderThroughputConfig"];
        };
        /** @enum {string} */
        PickOrderArea: "shipping" | "packing" | "picking";
        OrderThroughputChartSeriesResponseData: {
            throughput: components["schemas"]["ChartSeriesData"][];
        };
        "ApiResponse_OrderThroughputChartSeriesResponseData.BaseChartConfig_": components["schemas"]["OrderThroughputChartSeriesResponseData"] & {
            metadata: components["schemas"]["BaseChartConfig"];
        };
        /** @description Describes the contract for the data returned by the api. */
        OrderThroughputChartApiResponse: components["schemas"]["ApiResponse_OrderThroughputChartSeriesResponseData.BaseChartConfig_"];
        UnitsRemainingData: {
            /** Format: double */
            unitsRemaining: number;
        };
        /** @description DTO that defines the relevant details of the projected order fulfillment percentage. */
        ProjectedOrderFulfillment: {
            /** Format: double */
            projectedOrderFulfillmentPercentage: number;
        };
        OrderProgressSeriesData: {
            progress: components["schemas"]["ChartSeriesData"][];
        };
        OrderPickLineThroughputSeriesData: {
            throughput: components["schemas"]["ChartSeriesData"][];
        };
        OrderPerformanceFulfillmentData: {
            /** Format: double */
            orderFulfillment: number;
        };
        /** @description DTO that defines the relevant details of the orders outstanding. */
        OrdersOutstandingData: {
            /** Format: double */
            incompletedTotal: number;
        };
        "ApiResponse_OrdersOutstandingData.___": components["schemas"]["OrdersOutstandingData"] & {
            metadata: Record<string, never>;
        };
        /** @description DTO that describes the order line throughput data. */
        OrderLineThroughputData: {
            /** Format: double */
            totalCompletedOrderLines: number;
        };
        OrderLineProgress: {
            /** Format: double */
            lineProgressPercent: number;
        };
        OrderLineProgressConfig: {
            /** Format: double */
            lowRange: number;
            /** Format: double */
            highRange: number;
            /** Format: double */
            max: number;
        };
        "ApiResponse_OrderLineProgress.OrderLineProgressConfig_": components["schemas"]["OrderLineProgress"] & {
            metadata: components["schemas"]["OrderLineProgressConfig"];
        };
        OrderLineProgressSeriesData: {
            trend: components["schemas"]["ChartSeriesData"][];
            progress: components["schemas"]["ChartSeriesData"][];
        };
        "ApiResponse_OrderLineProgressSeriesData.BaseChartConfig_": components["schemas"]["OrderLineProgressSeriesData"] & {
            metadata: components["schemas"]["BaseChartConfig"];
        };
        OrderLineProgressChartApiResponse: components["schemas"]["ApiResponse_OrderLineProgressSeriesData.BaseChartConfig_"];
        OrderCycleTimeData: {
            /** Format: double */
            orderCycleTimeMinutes: number;
            status: string;
        };
        OrderCycleTimeConfig: {
            /** Format: double */
            max: number;
            /** Format: double */
            highRange: number;
        };
        "ApiResponse_OrderCycleTimeData.OrderCycleTimeConfig_": components["schemas"]["OrderCycleTimeData"] & {
            metadata: components["schemas"]["OrderCycleTimeConfig"];
        };
        OrderCycleTimeChartData: {
            orderCycleTimeChart: components["schemas"]["ChartSeriesData"][];
        };
        "ApiResponse_OrderCycleTimeChartData.BaseChartConfig_": components["schemas"]["OrderCycleTimeChartData"] & {
            metadata: components["schemas"]["BaseChartConfig"];
        };
        OrderCycleTimeChartApiResponse: components["schemas"]["ApiResponse_OrderCycleTimeChartData.BaseChartConfig_"];
        /** @description DTO that describes the order line throughput data. */
        OrderCustomerLineThroughputData: {
            /** Format: double */
            throughputRateOrderLinesPerHour: number;
        };
        /** @enum {string} */
        WMSCustomerOrderArea: "shipping" | "picking";
        /** @description DTO that describes the order throughput data by area. */
        ThroughputAreaData: {
            /** Format: double */
            throughputRate: number;
            /** Format: double */
            maxThroughputCapacity?: number;
            /** Format: double */
            minThroughputTarget?: number;
            /** Format: double */
            maxThroughputTarget?: number;
            status?: components["schemas"]["HealthStatus"];
        };
        ThroughputFacilityArea: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus?: components["schemas"]["AlertStatus"];
            /** @description Order throughput area data */
            throughput: components["schemas"]["ThroughputAreaData"];
        };
        /** @description DTO that defines the relevant details for the current orderline progress per area. */
        OrderAreasLineProgress: {
            /** Format: double */
            orderLineProgress: number;
            /** Format: double */
            orderLinesCompleted: number;
            /** Format: double */
            totalOrderLines: number;
        };
        LineProgressFacilityArea: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus?: components["schemas"]["AlertStatus"];
            /** @description Orderline progress area data */
            orderLines: components["schemas"]["OrderAreasLineProgress"];
        };
        OrderLineProgressAreasData: {
            areas: components["schemas"]["LineProgressFacilityArea"][];
        };
        /** @description DTO of the necessary base data to display a Facility Area. */
        FacilityArea: {
            /** @description Id of area. */
            id: string;
            /** @description Name of the area. */
            name: string;
            /** @description Operator area data. */
            operators?: components["schemas"]["AreaOperators"];
            alertStatus?: components["schemas"]["AlertStatus"];
        };
        CycleTimeAreasConfig: {
            targetTime: string;
        };
        "ApiResponse__areas-FacilityArea-Array_.CycleTimeAreasConfig_": {
            areas: components["schemas"]["FacilityArea"][];
        } & {
            metadata: components["schemas"]["CycleTimeAreasConfig"];
        };
        /** @description DTO that describes the order throughput data. */
        CustomerOrderThroughputData: {
            /** Format: double */
            throughputRateOrdersPerHour: number;
        };
        FacilityOrderLineThroughputSeriesResponse: {
            throughput: components["schemas"]["ChartSeriesData"][];
        };
        OrderCycleTime: {
            /** Format: double */
            orderCycleTimeMinutes: number;
        };
        /** @description Definition of the necessary data to display the Estimated Completion */
        EstimatedOrderCompletionTimes: {
            completionTime: string;
        };
        EstimatedOrderCompletionConfig: {
            targetTime: string;
        };
        "ApiResponse_EstimatedOrderCompletionTimes.EstimatedOrderCompletionConfig_": components["schemas"]["EstimatedOrderCompletionTimes"] & {
            metadata: components["schemas"]["EstimatedOrderCompletionConfig"];
        };
        CustomerOrderProgress: {
            /** Format: double */
            orderProgressPercentage: number;
        };
        CustomerOrderLineThroughputSeriesData: {
            throughput: components["schemas"]["ChartSeriesData"][];
        };
        OrderCustomerLineProgressSeriesData: {
            progress: components["schemas"]["ChartSeriesData"][];
        };
        WorkstationStringArray: string[];
        WorkstationData: {
            data: components["schemas"]["ChartSeriesData"][];
            id: string;
        };
        WorkstationSeriesData: {
            data: components["schemas"]["WorkstationData"][];
        };
        /** @enum {string} */
        WorkstationSeriesCharts: "LinesPerHour" | "QuantityPerHour" | "SourceContainersPerHour" | "DestinationContainersPerHour" | "WeightedLinesPerHour" | "WeightedQuantityPerHour" | "WeightedSourceContainersPerHour" | "WeightedDestinationContainersPerHour";
        WorkstationOrdersStatusResponse: {
            /** Format: double */
            activeOrders: number;
            /** Format: double */
            delayedOrders: number;
        };
        WorkstationOrdersListItem: {
            arrivalTime: string;
            /** Format: double */
            totalPicks: number;
            /** Format: double */
            completedPicks: number;
            container: string;
            orderId: string;
            pickTask: string;
            orderStatus: string;
            /**
             * Format: double
             * @description Minutes
             */
            dwellTime: number;
            position: string;
            station: string;
        };
        "ApiResponseArray_WorkstationOrdersListItem-Array._page-number--limit-number--totalResults-number__": {
            metadata: {
                /** Format: double */
                totalResults: number;
                /** Format: double */
                limit: number;
                /** Format: double */
                page: number;
            };
            data: components["schemas"]["WorkstationOrdersListItem"][];
        };
        WorkstationOrdersListData: components["schemas"]["ApiResponseArray_WorkstationOrdersListItem-Array._page-number--limit-number--totalResults-number__"];
        WorkstationOrdersDetailsPicksListData: {
            metadata: {
                /** Format: double */
                totalResults: number;
                /** Format: double */
                limit: number;
                /** Format: double */
                page: number;
                orderId: string;
            };
            data: {
                tableData: {
                    /** Format: double */
                    containers: number;
                    /** Format: double */
                    qtyPicked: number;
                    /** Format: double */
                    qtyOrdered: number;
                    size: string;
                    style: string;
                    orderLine: string;
                    status: string;
                    container: string;
                }[];
                deliveryNumber: string;
            };
        };
        WorkstationStatusStats: {
            /** Format: double */
            count: number;
            type: string;
        };
        WorkstationModeStats: {
            /** Format: double */
            count: number;
            type: string;
        };
        WorkstationStats: {
            workstationMode: components["schemas"]["WorkstationModeStats"][];
            status: components["schemas"]["WorkstationStatusStats"][];
            /** Format: double */
            totalWorkstations: number;
        };
        WorkstationOperatorStats: {
            /** Format: double */
            targetLinesPerHour: number;
            /** Format: double */
            actualLinesPerHour: number;
            /** Format: double */
            totalOperators: number;
        };
        WorkstationPerformanceStats: {
            /** Format: double */
            totalStarvationTime: number;
            /** Format: double */
            totalActiveTime: number;
        };
        WorkstationHealthStats: {
            /** Format: double */
            totalDowntime: number;
        };
        WorkstationDailyPerformanceData: {
            date: string;
            /** Format: double */
            totalLoggedInHours?: number;
            /** Format: double */
            idlePercentage?: number;
            /** Format: double */
            starvedPercentage?: number;
            /** Format: double */
            starvedHours?: number;
            /** Format: double */
            donorContainers?: number;
            /** Format: double */
            gtpContainers?: number;
            /** Format: double */
            linesPicked?: number;
            /** Format: double */
            qtyPerLine?: number;
            /** Format: double */
            pickLineQty?: number;
            /** Format: double */
            linesPerHour?: number;
            /** Format: double */
            avgLinesPickedPerHr1stShiftPercentage?: number;
            /** Format: double */
            avgLinesPickedPerHr2ndShiftPercentage?: number;
            /** Format: double */
            retrievalFromDMS?: number;
            /** Format: double */
            storageToDMS?: number;
            /** Format: double */
            retrievalFromASRS?: number;
            /** Format: double */
            storageToASRS?: number;
        };
        WorkstationDailyPerformanceList: components["schemas"]["WorkstationDailyPerformanceData"][];
        WorkstationDailyPerformanceListRequestBody: {
            workstations?: string[];
        };
        /** @enum {string} */
        WorkstationStatus: "Available" | "Closed" | "Paused" | "Unknown";
        /** @enum {string} */
        WorkstationMode: "Consolidation" | "Counting" | "Picking" | "Unknown";
        /** @enum {string} */
        WorkstationWorkflowStatus: "Disabled" | "Enabled" | "Unknown";
        Workstation: {
            /** Format: double */
            orderTotesPerHour: number;
            /** Format: double */
            donorTotesPerHour: number;
            /** Format: double */
            weightedLinesPerHour: number;
            /** Format: double */
            linesPerHour: number;
            /** Format: double */
            weightedQuantityPerHour: number;
            /** Format: double */
            quantityPerHour: number;
            /** Format: double */
            blockedTimeSecs: number;
            /** Format: double */
            idleTimeSecs: number;
            /** Format: double */
            starvedTimeSecs: number;
            /** Format: double */
            activeTimeSecs: number;
            operatorId: string;
            workflowStatus: components["schemas"]["WorkstationWorkflowStatus"];
            workMode: components["schemas"]["WorkstationMode"];
            status: components["schemas"]["WorkstationStatus"];
            workstation: string;
        };
        WorkstationList: {
            workstationList: components["schemas"]["Workstation"][];
        };
        WorkstationContainersListItem: {
            container: string;
            status: string;
            transport: string;
            /** Format: double */
            quantity: number;
            lastLocation: string;
            eventTime: string;
        };
        "ApiResponseArray_WorkstationContainersListItem-Array._page-number--limit-number--totalResults-number__": {
            metadata: {
                /** Format: double */
                totalResults: number;
                /** Format: double */
                limit: number;
                /** Format: double */
                page: number;
            };
            data: components["schemas"]["WorkstationContainersListItem"][];
        };
        WorkstationContainersListData: components["schemas"]["ApiResponseArray_WorkstationContainersListItem-Array._page-number--limit-number--totalResults-number__"];
        ComputeResources: {
            maxDuration: string | null;
            preemptible: boolean;
            instanceType: string | null;
            /** Format: double */
            memoryPerTaskMb: number | null;
            /** Format: double */
            cpuPerTask: number | null;
        };
        ExtraArgs: {
            simulationTime?: string;
            variant: string;
            runWithDocker: boolean;
            solution: string;
            type: string;
            version: string;
        };
        Event: {
            eventType: string;
            state: string;
            timestamp: string;
            /** Format: double */
            taskIndex: number | null;
            jobId: string;
            randomSeeds?: number[];
            tags?: string[];
            computeResources?: components["schemas"]["ComputeResources"];
            extraArgs?: components["schemas"]["ExtraArgs"] | null;
            /** Format: double */
            taskCount?: number;
            userId?: string;
            uploadId?: string;
            runEnv?: string | null;
            runnerBackend?: string;
            /** @enum {number|null} */
            unifiedRunnerVersion?: null;
            /** Format: double */
            taskExitCode?: number;
        };
        Task: {
            /** Format: double */
            taskIndex: number;
            lastUpdate: string;
            state: string;
            /** Format: double */
            timeTaken: number | null;
            visState: string;
            /** Format: double */
            exitCode: number;
        };
        Tasks: {
            [key: string]: components["schemas"]["Task"];
        };
        Attributes: {
            randomSeeds: number[];
            uploadId: string;
            runnerBackend: string;
        };
        SimulationJob: {
            jobId: string;
            events: components["schemas"]["Event"][];
            tags: string[];
            lastUpdate: string;
            tasks: components["schemas"]["Tasks"];
            createTime: string;
            userId: string;
            /** Format: double */
            taskCount: number;
            state: string;
            outputReady: boolean;
            attributes: components["schemas"]["Attributes"];
            /** Format: double */
            timeTaken: number | null;
            uploadId: string;
        };
        "ApiResponseArray_SimulationJob-Array.___": {
            metadata: Record<string, never>;
            data: components["schemas"]["SimulationJob"][];
        };
        AuthTicket: {
            userId: string;
            ticket: string;
        };
        ResendEmailVerificationResponse: {
            success: boolean;
            message: string;
            isSocialAuth?: boolean;
        };
        /** @enum {string} */
        RoleType: "internal" | "facility" | "admin";
        Role: {
            name: string;
            displayName: string;
            description?: string;
            roleType: components["schemas"]["RoleType"];
            assignable?: boolean;
        };
        GetRolesResponse: components["schemas"]["Role"][];
        UserInfo: {
            id: string;
            email: string;
            name?: string;
            emailVerified: boolean;
            createdAt: string;
            lastLogin?: string;
            /** Format: double */
            loginCount: number;
            /** @description Roles are not returned in the list view, but are returned in the GetUser response */
            roles?: components["schemas"]["Role"][];
            isSocialAuth: boolean;
        };
        "ApiResponseArray_UserInfo-Array._page-number--limit-number--totalResults-number__": {
            metadata: {
                /** Format: double */
                totalResults: number;
                /** Format: double */
                limit: number;
                /** Format: double */
                page: number;
            };
            data: components["schemas"]["UserInfo"][];
        };
        UsersListData: components["schemas"]["ApiResponseArray_UserInfo-Array._page-number--limit-number--totalResults-number__"];
        AssignRoleResponse: {
            success: boolean;
            message: string;
            assignedRoles: components["schemas"]["Role"][];
        };
        AssignRoleRequest: {
            roleNames: string[];
        };
        RemoveRoleResponse: {
            success: boolean;
            message: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    GetAiBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetAiFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetAiEnterpriseSearch: {
        parameters: {
            query: {
                /** @example What is a pin fault? */
                searchText: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EnterpriseSearchResponse"];
                };
            };
        };
    };
    PutUserWritable: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Identifying information about the requested Setting resource, as well as requested updates.
         *     The setting must be in the 'user-writable' group and will always be updated at the user level.
         *     If the user is a ct_configurator, they may specify a userId to update another user's setting. */
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateSettingData"] & {
                    userId?: string;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AppConfigSetting"];
                };
            };
            /** @description New setting created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetMetricConfigs: {
        parameters: {
            query?: {
                /** @description Filter by metric name */
                metricName?: string;
                /** @description Filter by metric ID */
                metricId?: string;
                /** @description Filter by fact type */
                factType?: string;
                /** @description Filter by node name */
                nodeName?: string;
                /** @description Filter by active status */
                active?: boolean;
                /** @description Filter by enabled status */
                enabled?: boolean;
                /** @description Filter by config type */
                configType?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Returns a list of metric configurations */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MetricConfigSummary"][];
                };
            };
            /** @description No metric configurations found matching the criteria */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Internal server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PutConfigProcessFlowMetricConfig: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description The metric configuration data */
        requestBody: {
            content: {
                "application/json": components["schemas"]["MetricConfigDetail"];
            };
        };
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomMetricConfigurationEntity"];
                };
            };
            /** @description Metric configuration created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Invalid metric configuration data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetConfigBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetConfigFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetConfigSettings: {
        parameters: {
            query?: {
                /** @description Id of the setting to retrieve. Optional, if this and settingName are both omitted, all settings will be returned. */
                settingId?: string;
                /** @description Name of the setting to retrieve. Optional, if this and settingName are both omitted, all settings will be returned.
                 *     If both setting id and name are provided, only settings matching BOTH parameters will be returned. */
                settingName?: string;
                /** @description Level of setting to be returned (user, tenant, default). Optional, defaults to all levels.
                 *     Only used when a setting id and/or name is provided. */
                settingType?: components["schemas"]["AppConfigSettingSource"];
                /** @description If true, will fetch the default, tenant, and user setting values for a setting.
                 *     If false, only the most specific stored value is returned; for example, if there is a tenant level value and a
                 *     user level value applicable to the given user, then only the user level value is returned. Optional, defaults to false. */
                allStoredValues?: boolean;
                /** @description filter on settings that are a part of the specified group name. Only used when a setting id and/or name is provided. */
                group?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AppConfigSettingArrayOrAppConfigSetting"];
                };
            };
        };
    };
    PutConfigSettings: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Identifying information about the requested Setting resource, as well as requested updates.<br><br>
         *     <b>id</b> - the setting Id.  ID is required in order to update an existing setting, but not to add a new one<br>
         *     <b>name</b> - the name of the setting.  Required.<br>
         *     <b>dataType</b> - one of: 'json', 'string', 'boolean', or 'number'<br>
         *     <b>group</b> - the Setting group.  This value is updated if it is different.<br>
         *     <b>value</b> - the value to update the setting with. Can be any value<br>
         *     <b>levelToUpdate</b> - one of: 'default', 'tenant', 'site', or 'user'. This determines the appropriate value to change.<br>
         *     <b>description</b> - optional string description of the setting.<br> */
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateSettingData"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AppConfigSetting"];
                };
            };
            /** @description New setting created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PostConfigDefaultConfig: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DefaultConfigSeedingResult"];
                };
            };
        };
    };
    DeleteConfigSetting: {
        parameters: {
            query?: {
                /** @description specific record to delete.  If blank, all stored setting values associated with the setting ID are deleted */
                levelToDelete?: components["schemas"]["AppConfigSettingSource"];
            };
            header?: never;
            path: {
                /** @description setting ID to delete */
                settingId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeletedRecordsResult"];
                };
            };
        };
    };
    getSettingSchema: {
        parameters: {
            query?: {
                settingId?: string;
                settingName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    GetConfigSettingLogs: {
        parameters: {
            query: {
                /** @description Id of the setting being searched for */
                setting_id: string;
                /** @description maximum number of logs that may be returned
                 *     (if 0, all logs will be returned) */
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AppConfigSettingLogsData"];
                };
            };
        };
    };
    GetConfigFacilityConfig: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FacilityConfigSettings"];
                };
            };
        };
    };
    GetConfigCuratedData: {
        parameters: {
            query: {
                /** @description Name of the table to get data for. */
                table: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    GetConfigCuratedDataV2: {
        parameters: {
            query: {
                /** @description Name of the table to get data for. */
                table: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CuratedTableRow"][];
                };
            };
        };
    };
    GetConfigCuratedTablesList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
        };
    };
    GetConfigAppConfig: {
        parameters: {
            query?: {
                /** @example my-special-config */
                config?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AppConfig"];
                };
            };
        };
    };
    GetDataExplorerSearch: {
        parameters: {
            query: {
                searchText: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerResult"];
                };
            };
        };
    };
    GetDataExplorerResults: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                limit?: number;
                bookmark?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerResults"];
                };
            };
        };
    };
    GetDataExplorerResult: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                resultId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerResult"];
                };
            };
        };
    };
    PutDataExplorerResult: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                resultId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["DataExplorerResult"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerResult"];
                };
            };
            /** @description Result does not exist or user does not have permission to specified result */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    DeleteDataExplorerResult: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                resultId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Result had been removed from the database */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Result does not exist or user does not have permission to specified result */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetDataExplorerRecommendations: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerRecommendations"];
                };
            };
        };
    };
    GetDematicChatGoldQuestions: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        goldQuestions: components["schemas"]["DataExplorerRecommendationsData"][];
                    };
                };
            };
        };
    };
    GetDataExplorerBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetDataExplorerFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetDataExplorerAgentSearch: {
        parameters: {
            query: {
                searchText: string;
                sessionId?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully retrieved example data */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataExplorerAgentSearchResponse"];
                };
            };
        };
    };
    GetDiagnosticsInfrastructureList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DiagnosticsInfrastructureContract"];
                };
            };
        };
    };
    GetDiagnosticsBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetDiagnosticsFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    PostInventoryUploadKnownDemand: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": {
                    /** Format: binary */
                    manager: string;
                    /** Format: binary */
                    details: string;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** Format: double */
                        orderLinesProcessed: number;
                        /** Format: double */
                        ordersProcessed: number;
                    };
                };
            };
        };
    };
    GetInventoryUploadRecentActivity: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryUploadRecentActivity"][];
                };
            };
        };
    };
    GetInventoryStorageUtilization: {
        parameters: {
            query?: {
                /** @deprecated */
                start_date?: string;
                /** @deprecated */
                end_date?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryStorageUtilization"];
                };
            };
        };
    };
    GetInventoryStockDistributionUnderPercentageSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryStockDistributionUnderData"];
                };
            };
        };
    };
    GetInventoryStockDistributionOverPercentageSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryStockDistributionOverData"];
                };
            };
        };
    };
    GetInventoryStockDistributionNoPercentageSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryStockDistributionNoData"];
                };
            };
        };
    };
    GetInventoryStockDistributionAtPercentageSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryStockDistributionAtData"];
                };
            };
        };
    };
    PostInventorySkusList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PostInventorySkusListResponse"];
                };
            };
        };
    };
    PostInventorySkusListExport: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: components["schemas"]["IncludedColumns"];
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetInventoryReplenishmentTaskTypeSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTypeData"];
                };
            };
        };
    };
    GetInventoryReplenishmentDetails: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryReplenishmentDetails"];
                };
            };
        };
    };
    GetInventoryProcessFlowAreas: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProcessFlowResponse"];
                };
            };
        };
    };
    GetInventoryProcessFlowAreaById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                areaId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProcessFlowResponse"];
                };
            };
        };
    };
    GetInventoryProcessFlowGraphDetails: {
        parameters: {
            query?: {
                view?: string;
            };
            header?: never;
            path: {
                type: string;
                elementId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProcessFlowDetailsResponse"];
                };
            };
        };
    };
    UpdateInventoryProcessFlowArea: {
        parameters: {
            query?: {
                view?: string;
            };
            header?: never;
            path: {
                areaId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdatePositionPayload"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UpdatedArea"];
                };
            };
        };
    };
    UpdateInventoryProcessFlowEdge: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                edgeId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["Edge"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Edge"];
                };
            };
        };
    };
    UpdateInventoryProcessFlowMetric: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                metricId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["Metric"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Metric"];
                };
            };
        };
    };
    ClearInventoryProcessFlowCache: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ClearCacheRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClearCacheResponse"];
                };
            };
        };
    };
    GetInventoryPerformanceSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                department_filter: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryPerformanceSeriesContract"];
                };
            };
        };
    };
    PostInventorySkuHighImpactList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryHighImpactSKUContract"];
                };
            };
        };
    };
    GetInventoryBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetInventoryFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetInventoryHandlingUnitsTrayed: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryHandlingUnitsTrayedContract"];
                };
            };
        };
    };
    GetDataAnalysisTimestampData: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryForecastDataAnalysisTimestamp"];
                };
            };
        };
    };
    PostInventoryForecastList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryForecastListingData"];
                };
            };
        };
    };
    PostInventoryForecastListExport: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Request body with filters, sorting, and columns parameter. */
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: components["schemas"]["IncludedColumns"];
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetInventoryForecastSkuLocations: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                skuId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryForecastSkuLocationAreas"];
                };
            };
        };
    };
    GetInventoryForecastSkuOrders: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                skuId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryForecastSkuOrders"];
                };
            };
        };
    };
    GetInventorySkuForecast: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                skuId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventorySkuForecastDetails"];
                };
            };
        };
    };
    GetInventoryFilter: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryAreaFilterDefinition"];
                };
            };
        };
    };
    PostInventoryContainersList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description - Request body with filters, sorting, and pagination */
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    byPassConfigSetting?: boolean;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PostInventoryContainersListResponse"];
                };
            };
        };
    };
    PostInventoryContainersListExport: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: components["schemas"]["IncludedColumns"];
                    byPassConfigSetting?: boolean;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    PostInventoryContainerEventsList: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Container identifier. */
                containerId: string;
            };
            cookie?: never;
        };
        /** @description Request body with filters, sorting, pagination, and months parameter. */
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    /** Format: double */
                    months?: number;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PostInventoryContainerEventsListResponse"];
                };
            };
        };
    };
    PostInventoryContainerEventsListExport: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Container identifier. */
                containerId: string;
            };
            cookie?: never;
        };
        /** @description Request body with filters, sorting, pagination, columns, and months parameter. */
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: components["schemas"]["IncludedColumns"];
                    /** Format: double */
                    months?: number;
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetInventoryBinLocations: {
        parameters: {
            query: {
                /** @description The double digit aisle code for the bin locations to retrieve. */
                aisle: string;
                /** @description The double digit level code for the bin locations to retrieve. */
                level: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryBinLocationsResponse"];
                };
            };
        };
    };
    GetInventoryAdvicesOutstanding: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AdvicesOutstandingData"];
                };
            };
        };
    };
    GetInventoryAdvicesList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AdvicesList"];
                };
            };
        };
    };
    GetInventoryAdvicesInProgress: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryAdvicesInProgressData"];
                };
            };
        };
    };
    GetInventoryAdvicesFinished: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AdvicesFinishedData"];
                };
            };
        };
    };
    GetInventoryAdvicesDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                adviceId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AdviceDetailsData"];
                };
            };
        };
    };
    GetInventoryAdvicesCycleTime: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AdvicesCycleTimeData"];
                };
            };
        };
    };
    GetInventoryAccuracy: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area_filter?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_InventoryAccuracy.InventoryAccuracyConfig_"];
                };
            };
        };
    };
    GetInventoryContainerEventsDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description - ID of the container for which KPI metrics are retrieved. */
                containerId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InventoryContainerEventsKpiContract"];
                };
            };
        };
    };
    GetEquipmentWorkstationStarvedBlockedTimeSeries: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse"];
                };
            };
        };
    };
    GetEquipmentWorkstationOperatorActivity: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentWorkstationOperatorActivityData"][];
                };
            };
        };
    };
    GetEquipmentWorkstationLineRatesSeries: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentWorkstationLineRatesSeriesData"];
                };
            };
        };
    };
    GetEquipmentWorkstationMovementsDetail: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentWorkstationAisleMovementData"];
                };
            };
        };
    };
    GetEquipmentWorkstationAisleActiveFaults: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentWorkstationActiveFaultsData"][];
                };
            };
        };
    };
    GetEquipmentSummaryWorkstations: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path: {
                workstationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_EquipmentSummaryWorkstationDefinition.EquipmentSummaryWorkstationConfigDefinition_"];
                };
            };
        };
    };
    GetEquipmentSummaryAreas: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentSummaryAreaContract"];
                };
            };
        };
    };
    GetEquipmentSummaryAisle: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path: {
                aisleId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_EquipmentSummaryAisleDefinition.EquipmentSummaryAisleConfigDefinition_"];
                };
            };
        };
    };
    PostEquipmentEventsList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    /** Format: date-time */
                    end_date: string;
                    /** Format: date-time */
                    start_date: string;
                    /** Format: double */
                    limit?: number;
                    /** Format: double */
                    page?: number;
                    sortFields?: components["schemas"]["SortField"][];
                    filters?: components["schemas"]["BaseFilterType"];
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponseArray_RecentEventQueryResponse-Array.RecentEventQueryConfig_"];
                };
            };
        };
    };
    GetEquipmentOutboundRate: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentOutboundRateApiResponse"];
                };
            };
        };
    };
    GetEquipmentBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetEquipmentFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    PostEquipmentFaultsStatusList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultsStatusListRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        availableStatuses: string[];
                    };
                };
            };
        };
    };
    GetEquipmentFaultsSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentFaultsSeriesContractData"];
                };
            };
        };
    };
    GetEquipmentFaultsEvents: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FaultEventContract"];
                };
            };
        };
    };
    PostEquipmentFaultsMovementsSeries: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultsMovementsSeriesRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FaultsMovementsSeriesData"];
                };
            };
        };
    };
    PostEquipmentFaultsList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PostEquipmentFaultsListResponse"];
                };
            };
        };
    };
    PostEquipmentFaultsLevelList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultsLevelListRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AvailableLevels"];
                };
            };
        };
    };
    getFaultGroupedByCounts: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultCountGroupedByRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FaultCountGroupedSeriesData"];
                };
            };
        };
    };
    PostEquipmentFaultsDeviceIdsList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EquipmentFaultsDeviceIdRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
        };
    };
    PostEquipmentFaultsDeviceTypeList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultsDeviceTypeListRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        availableDeviceTypes: string[];
                    };
                };
            };
        };
    };
    GetEquipmentFaults: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_EquipmentFaultsDefinition.EquipmentFaultsConfigDefinition_"];
                };
            };
        };
    };
    getFaultAvgDurationByStatus: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultAvgDurationByStatusRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FaultAvgDurationSeriesData"];
                };
            };
        };
    };
    GetEquipmentFaultsArea: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_FaultsAreasResponse.FaultsAreasConfig_"];
                };
            };
        };
    };
    PostEquipmentFaultsAisleList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FaultsAisleListRequest"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AvailableAisles"];
                };
            };
        };
    };
    GetEquipmentFaultsActiveList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EquipmentActiveFaults"];
                };
            };
        };
    };
    GetOperatorsBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetOperatorsFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    PutOperatorsAreas: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RequestBody"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetOperatorsActiveSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperatorsChartSeriesApiResponse"];
                };
            };
        };
    };
    GetOperatorsActive: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OperatorCountData.OperatorCountsConfig_"];
                };
            };
        };
    };
    GetOperatorsActiveAreas: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperatorFacilityArea"][];
                };
            };
        };
    };
    GetPickOrdersShipped: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderShippedData.___"];
                };
            };
        };
    };
    GetOrdersPickCycleCount: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrdersPickCycleCountData"];
                };
            };
        };
    };
    GetOrdersFacilityShipped: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrdersShipped.___"];
                };
            };
        };
    };
    GetOrdersFacilityProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderProgress.OrderProgressConfig_"];
                };
            };
        };
    };
    GetOrdersFacilityEstimatedCompletion: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description contract */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderEstimatedCompletionData"];
                };
            };
        };
    };
    GetOrdersCustomerShipped: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrdersShipped.___"];
                };
            };
        };
    };
    GetOrdersThroughput: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["PickOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderThroughputData.OrderThroughputConfig_"];
                };
            };
        };
    };
    GetOrdersThroughputSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderThroughputChartApiResponse"];
                };
            };
        };
    };
    GetOrdersRemaining: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UnitsRemainingData"];
                };
            };
        };
    };
    GetOrdersFulfillment: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProjectedOrderFulfillment"];
                };
            };
        };
    };
    GetOrdersProgressSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderProgressSeriesData"];
                };
            };
        };
    };
    GetOrdersPickLineThroughputSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderPickLineThroughputSeriesData"];
                };
            };
        };
    };
    GetOrdersPerformanceFulfillment: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                department_filter: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderPerformanceFulfillmentData"];
                };
            };
        };
    };
    GetOrdersOutstanding: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrdersOutstandingData.___"];
                };
            };
        };
    };
    GetOrdersLineThroughput: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderLineThroughputData"];
                };
            };
        };
    };
    GetOrdersLineProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderLineProgress.OrderLineProgressConfig_"];
                };
            };
        };
    };
    GetOrdersLineProgressSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderLineProgressChartApiResponse"];
                };
            };
        };
    };
    GetOrdersBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetOrdersFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetOrdersPickCycleTime: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderCycleTimeData.OrderCycleTimeConfig_"];
                };
            };
        };
    };
    GetOrdersCycleTimeSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderCycleTimeChartApiResponse"];
                };
            };
        };
    };
    GetOrdersProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_OrderProgress.OrderProgressConfig_"];
                };
            };
        };
    };
    GetOrdersCustomerLineThroughput: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderCustomerLineThroughputData"];
                };
            };
        };
    };
    GetOrdersThroughputAreas: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        areas: components["schemas"]["ThroughputFacilityArea"][];
                    };
                };
            };
        };
    };
    GetOrdersLineProgressAreas: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderLineProgressAreasData"];
                };
            };
        };
    };
    GetOrdersCycleTime: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse__areas-FacilityArea-Array_.CycleTimeAreasConfig_"];
                };
            };
        };
    };
    GetOrdersFacilityThroughput: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderThroughputData"];
                };
            };
        };
    };
    GetOrdersFacilityLineThroughputSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description contract */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FacilityOrderLineThroughputSeriesResponse"];
                };
            };
        };
    };
    GetOrdersFacilityLineProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderLineProgress"];
                };
            };
        };
    };
    GetOrdersFacilityCycleTime: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderCycleTime"];
                };
            };
        };
    };
    GetOrdersCompletion: {
        parameters: {
            query: {
                start_date: string;
                end_date?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponse_EstimatedOrderCompletionTimes.EstimatedOrderCompletionConfig_"];
                };
            };
        };
    };
    GetOrdersCustomerThroughput: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderThroughputData"];
                };
            };
        };
    };
    GetOrdersCustomerThroughputSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        throughput: components["schemas"]["ChartSeriesData"][];
                    };
                };
            };
        };
    };
    GetOrdersCustomerProgressSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderProgressSeriesData"];
                };
            };
        };
    };
    GetOrdersCustomerProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderProgress"];
                };
            };
        };
    };
    GetOrdersCustomerLineThroughputSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderLineThroughputSeriesData"];
                };
            };
        };
    };
    GetOrderLinesCustomerProgressSeries: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderCustomerLineProgressSeriesData"];
                };
            };
        };
    };
    GetOrdersCustomerLineProgress: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderLineProgress"];
                };
            };
        };
    };
    GetOrdersCustomerEstimatedCompletion: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description contract */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerOrderEstimatedCompletionData"];
                };
            };
        };
    };
    GetOrdersCustomerCycleTime: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                area?: components["schemas"]["WMSCustomerOrderArea"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrderCycleTime"];
                };
            };
        };
    };
    GetWorkstations: {
        parameters: {
            query?: {
                start_date?: string;
                end_date?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationStringArray"];
                };
            };
        };
    };
    GetWorkstationSeriesData: {
        parameters: {
            query: {
                chart: components["schemas"]["WorkstationSeriesCharts"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationSeriesData"];
                };
            };
        };
    };
    GetWorkstationOrdersStatus: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationOrdersStatusResponse"];
                };
            };
        };
    };
    PostWorkstationOrdersList: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationOrdersListData"];
                };
            };
        };
    };
    PostWorkstationOrdersListExport: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: {
                        [key: string]: boolean;
                    };
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    PostWorkstationOrdersDetailsPicksList: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path: {
                orderId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationOrdersDetailsPicksListData"];
                };
            };
        };
    };
    PostWorkstationOrdersDetailsPicksListExport: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path: {
                orderId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"] & {
                    columns: {
                        [key: string]: boolean;
                    };
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetWorkstationSummary: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationStats"];
                };
            };
        };
    };
    GetWorkstationOperatorSummary: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationOperatorStats"];
                };
            };
        };
    };
    GetWorkstationPerformanceSummary: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationPerformanceStats"];
                };
            };
        };
    };
    GetWorkstationHealthSummary: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationHealthStats"];
                };
            };
        };
    };
    GetWorkstationBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetWorkstationFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    PostWorkstationDailyPerformanceList: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkstationDailyPerformanceListRequestBody"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationDailyPerformanceList"];
                };
            };
        };
    };
    GetWorkstationList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationList"];
                };
            };
        };
    };
    PostWorkstationListExport: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    columns: {
                        [key: string]: boolean;
                    };
                };
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    PostWorkstationContainersList: {
        parameters: {
            query: {
                start_date: string;
                end_date: string;
                size: string;
                style: string;
                zone: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkstationContainersListData"];
                };
            };
        };
    };
    GetSimulationOutputByJobId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                jobId: string;
                taskIndex: number;
                fileName: string;
                fileType: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string | unknown[];
                };
            };
        };
    };
    GetSimulationJobs: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiResponseArray_SimulationJob-Array.___"];
                };
            };
        };
    };
    GetSimulationBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetSimulationFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetTrustedTicket: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AuthTicket"];
                };
            };
        };
    };
    GetAdminBasicHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    GetAdminFullHealthCheck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Ok */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        subSystems: {
                            redis: components["schemas"]["HealthCheckStatus"];
                            bigQuery: components["schemas"]["HealthCheckStatus"];
                            auth0: components["schemas"]["HealthCheckStatus"];
                        };
                        sha: string;
                        status: components["schemas"]["HealthCheckStatus"];
                    };
                };
            };
        };
    };
    ResendEmailVerification: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Email verification resent successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResendEmailVerificationResponse"];
                };
            };
        };
    };
    GetAssignableRoles: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Roles retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetRolesResponse"];
                };
            };
        };
    };
    GetUsersList: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PaginatedRequestNoDates"];
            };
        };
        responses: {
            /** @description Users retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsersListData"];
                };
            };
        };
    };
    GetUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description User retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserInfo"];
                };
            };
        };
    };
    AssignUserRoles: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AssignRoleRequest"];
            };
        };
        responses: {
            /** @description Roles assigned successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AssignRoleResponse"];
                };
            };
        };
    };
    RemoveUserRole: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: string;
                roleName: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Role removed successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RemoveRoleResponse"];
                };
            };
        };
    };
}
