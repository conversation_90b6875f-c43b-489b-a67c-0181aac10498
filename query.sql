DECLARE siteTimeZone STRING DEFAULT 'America/Chicago';
DECLARE container_id STRING DEFAULT '101412';

WITH latest_inventory AS (
  SELECT 
    inventory_unit_code AS handling_unit_code,
    location_code,
    sku_code,
    total_inventory_qty AS base_total_quantity,
    unallocated_inventory_qty AS base_unallocated_quantity,
    uom,
    snapshot_timestamp_utc AS base_timestamp
  FROM (
    SELECT *,
      ROW_NUMBER() OVER (PARTITION BY inventory_unit_code, sku_code ORDER BY snapshot_timestamp_utc DESC) as rn
    FROM `edp-d-us-east2-etl.tti_welford_sc.gold_inventory_snapshot`
    WHERE inventory_unit_code = container_id
      AND TIMESTAMP_TRUNC(snapshot_timestamp_utc, DAY, siteTimeZone) >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY), DAY, siteTimeZone)
  )
  WHERE rn = 1
),
picks_since_base AS (
  SELECT 
    source_handling_unit_code AS handling_unit_code,
    item_sku AS sku_code,
    SUM(picked_qty) AS picked_quantity_since_base
  FROM `edp-d-us-east2-etl.tti_welford_sc.gold_pick` p
  INNER JOIN latest_inventory li 
    ON p.source_handling_unit_code = li.handling_unit_code 
    AND p.item_sku = li.sku_code
  WHERE p.event_timestamp_utc > li.base_timestamp
    AND p.event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    AND p.picked_qty > 0
  GROUP BY source_handling_unit_code, item_sku
),
calculated_inventory AS (
  SELECT
    li.handling_unit_code,
    li.location_code,
    li.sku_code,
    GREATEST(li.base_total_quantity - COALESCE(ps.picked_quantity_since_base, 0), 0) AS calculated_total_quantity,
    li.base_unallocated_quantity + COALESCE(ps.picked_quantity_since_base, 0) AS calculated_unallocated_quantity,
    li.uom,
    li.base_timestamp AS last_activity_date,
    COALESCE(ps.picked_quantity_since_base, 0) AS picked_quantity_since_base
  FROM latest_inventory li
  LEFT JOIN picks_since_base ps 
    ON li.handling_unit_code = ps.handling_unit_code 
    AND li.sku_code = ps.sku_code
),
pick_events AS (
  SELECT
    source_handling_unit_code AS container_id,
    item_sku AS sku,
    SUM(CASE 
      WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, siteTimeZone)
      AND UPPER(work_type_code) IN ('CYCLECOUNT') THEN 1 ELSE 0 
    END) AS cycle_count_events_today,
    SUM(CASE 
      WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, siteTimeZone)
      AND work_type_code = 'PICKING' THEN 1 ELSE 0 
    END) AS pick_events_today,
    AVG(CASE WHEN UPPER(work_type_code) IN ('CYCLECOUNT') THEN 1 ELSE 0 END) AS average_daily_cycle_count,
    AVG(CASE WHEN work_type_code = 'PICKING' THEN 1 ELSE 0 END) AS average_daily_pick_events
  FROM `edp-d-us-east2-etl.tti_welford_sc.gold_pick`
  WHERE TIMESTAMP_TRUNC(event_timestamp_utc, DAY, siteTimeZone) >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY), DAY, siteTimeZone)
    AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
  GROUP BY source_handling_unit_code, item_sku
)
SELECT
  COALESCE(ci.handling_unit_code, '') AS container_id,
  COALESCE(ci.location_code, '') AS location_id,
  -- '' AS zone,
  COALESCE(ci.sku_code, '') AS sku,
  COALESCE(ci.calculated_total_quantity, 0) AS quantity,
  COALESCE(ci.calculated_unallocated_quantity, 0) AS unallocated_quantity,
  COALESCE(ci.uom, '') AS uom,
  ci.last_activity_date AS last_activity_date,
  COALESCE(ci.picked_quantity_since_base, 0) AS picked_quantity_since_base,
  COALESCE(pe.cycle_count_events_today, 0) AS cycle_count_events_today,
  COALESCE(pe.pick_events_today, 0) AS pick_events_today,
  COALESCE(pe.average_daily_cycle_count, 0) AS average_daily_cycle_count,
  COALESCE(pe.average_daily_pick_events, 0) AS average_daily_pick_events
FROM calculated_inventory ci
LEFT JOIN pick_events pe ON ci.handling_unit_code = pe.container_id AND ci.sku_code = pe.sku
ORDER BY COALESCE(pe.cycle_count_events_today, 0) DESC
LIMIT 50 