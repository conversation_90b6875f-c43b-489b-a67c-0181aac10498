{
  "note": "This configuration has been migrated to eslint.config.js for ESLint 9.x compatibility",
  "extends": [
    "@open-wc/eslint-config",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "./node_modules/gts/"
  ],
  "parserOptions": { "ecmaVersion": 2020 },
  "plugins": ["@typescript-eslint"],
  "rules": {
    "n/no-extraneous-import": "off",
    "import/no-extraneous-dependencies": "off",
    "class-methods-use-this": "off",
    "no-use-before-define": "off",
    "no-shadow": "off", // fixes incorrect no-shadow errors when using typescript
    "@typescript-eslint/no-shadow": "error",
    "no-return-await": "off", // deprecated rule as of eslint v8.46.0
    "prefer-destructuring": "off",
    "no-useless-constructor": "off", // handles our service and store constructors
    "no-empty-function": ["error", { "allow": ["constructors"] }],
    "prefer-object-spread": "off",
    "lines-between-class-members": "off",
    "guard-for-in": "off",
    "import/extensions": "off",
    "import/no-default-export": "off",
    "import/no-unresolved": "off",
    "no-plusplus": ["error", { "allowForLoopAfterthoughts": true }],
    "no-restricted-syntax": "off",
    "camelcase": "off",
    "@typescript-eslint/prefer-namespace-keyword": "off",
    "@typescript-eslint/no-namespace": "off",
    "default-param-last": "off" // TODO: Decide if we are ok disabling this rule?  If not, create ticket to fix this and then renable rule.
  }
}
