import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  startEndDateValidation,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Post,
  Body,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Example,
} from 'tsoa';
import {
  AlarmsService,
  type AlarmQueryParams,
} from '../services/alarms-service.js';
import {type PostAlarmsListResponse} from '../defs/alarm-def.ts';

// Import SortField from the standard location
import {SortField} from '@ict/sdk-foundations/types';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class AlarmsController extends Controller {
  // Example response for the standard format
  public static readonly exampleResponse: PostAlarmsListResponse = {
    data: [
      {
        id: 'c8437cfb',
        title: 'CA003 ANTI GRID LOCK LEVEL 1',
        description: 'CA003 ANTI GRID LOCK LEVEL 1',
        tag: 'CA003_AGL_L1',
        location: {
          area: 'DMSInboundMergeSorter',
          section: 'CC001',
          equipment: 'CA003_AGL_L1',
        },
        timing: {
          startTime: new Date('2025-07-10T02:13:40.000Z'),
          endTime: new Date('2025-07-10T02:14:07.000Z'),
          duration: '27000',
          updatedStartTime: new Date('2025-07-10T02:13:40.000Z'),
          updatedEndTime: new Date('2025-07-10T02:14:07.000Z'),
          updatedDuration: '27000',
        },
        status: 'Active',
        reason: 'KEPT',
      },
    ],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
    },
  };

  /**
   * Post alarms data with filters using standard pagination format
   *
   * @param {object} request - Standard pagination request with filters, sorting, grouping, and date range
   * @returns {Promise<PostAlarmsListResponse>} Paginated response with alarm data
   * @throws IctError
   */
  @Example<PostAlarmsListResponse>(AlarmsController.exampleResponse)
  @SuccessResponse('200', 'Successfully retrieved alarms')
  @Post('/alarms/list')
  @OperationId('PostAlarmsList')
  @Tags('availability')
  public async postAlarmsList(
    @Body()
    request: {
      /**
       * @isDateTime
       */
      start_date: Date;
      /**
       * @isDateTime
       */
      end_date: Date;
      filters?: unknown;
      /**
       * @isArray
       */
      sortFields?: SortField[];
      groupByFields?: string[];
      /**
       * @isInt
       * @minimum 0
       */
      limit?: number;
      /**
       * @isInt
       * @minimum 0
       */
      offset?: number;
      searchString?: string;
    },
  ): Promise<PostAlarmsListResponse> {
    const logger = Container.get(WinstonLogger);
    const alarmsService = Container.get(AlarmsService);

    // Validate the start and end dates
    startEndDateValidation(request.start_date, request.end_date);

    logger.info('Getting alarms list', {
      request,
    });

    return await alarmsService.getAlarms(request as AlarmQueryParams);
  }
}
