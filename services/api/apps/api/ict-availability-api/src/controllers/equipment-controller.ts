import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.js';
import {PostAvailableEquipmentNamesResponse} from '../defs/equipment-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class EquipmentController extends Controller {
  /**
   * Get available equipment names with filters using standard pagination format
   *
   * @param {object} params - Standard pagination request with filters
   * @returns {Promise<PostAvailableEquipmentNamesResponse>} Paginated response with available equipment names
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available equipment names')
  @Get('/equipment/names/list')
  @OperationId('GetAvailableEquipmentNames')
  @Tags('availability')
  public async getAvailableEquipmentNames(
    @Query() page?: number,
    @Query() limit?: number,
    @Query() searchTerm?: string,
  ): Promise<PostAvailableEquipmentNamesResponse> {
    const logger = Container.get(WinstonLogger);
    const equipmentService = Container.get(EquipmentService);

    logger.info('Getting available equipment names list', {
      page,
      limit,
      searchTerm,
    });

    return await equipmentService.getAvailableEquipmentNames({
      page,
      limit,
      searchTerm,
    });
  }
}
