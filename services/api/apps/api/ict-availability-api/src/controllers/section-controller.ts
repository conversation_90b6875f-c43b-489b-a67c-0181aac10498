import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {SectionService} from '../services/section-service.js';
import {PostAvailableSectionsResponse} from '../defs/section-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class SectionController extends Controller {
  /**
   * Get available sections with filters using standard pagination format
   *
   * @param {object} params - Standard pagination request with filters
   * @returns {Promise<PostAvailableSectionsResponse>} Paginated response with available sections
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available sections')
  @Get('/sections/list')
  @OperationId('GetAvailableSections')
  @Tags('availability')
  public async getAvailableSections(
    @Query() page?: number,
    @Query() limit?: number,
    @Query() searchTerm?: string,
  ): Promise<PostAvailableSectionsResponse> {
    const logger = Container.get(WinstonLogger);
    const sectionService = Container.get(SectionService);

    logger.info('Getting available sections list', {
      page,
      limit,
      searchTerm,
    });

    return await sectionService.getAvailableSections({
      page,
      limit,
      searchTerm,
    });
  }
}
