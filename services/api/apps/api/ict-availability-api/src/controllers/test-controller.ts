import {
  configPostgresDatabase,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';

@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
@Route('availability')
export class AvailabilityController extends Controller {
  @SuccessResponse('200')
  @Get('/test')
  @OperationId('Test')
  @Tags('test')
  public async test(): Promise<{now: string}> {
    return {
      now: new Date().toISOString(),
    };
  }
}
