import {ApiResponseArray} from '@ict/sdk-foundations/types';

// Application-level alarm interface (user-facing)
export interface Alarm {
  id: string;
  faultId: string;
  title: string;
  description: string;
  tag: string;
  location: {
    area: string;
    section: string;
    equipment: string;
  };
  timing: {
    startTime: Date;
    endTime?: Date;
    duration: string;
    updatedStartTime?: Date;
    updatedEndTime?: Date;
    updatedDuration?: string;
  };
  status: string;
  reason: string;
}

// Pagination metadata for alarms
export interface AlarmPaginationInfo {
  page: number;
  limit: number;
  totalResults: number;
  totalPages: number;
}

// Standard API response type for alarm lists
export type PostAlarmsListResponse = ApiResponseArray<
  Alarm[],
  AlarmPaginationInfo
>;
