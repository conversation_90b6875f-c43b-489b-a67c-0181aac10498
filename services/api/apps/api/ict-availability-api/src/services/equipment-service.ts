import {DiSer<PERSON>, WinstonLogger, ContextService} from 'ict-api-foundations';
import {
  EDPAvailabilityService,
  EDPEquipmentNamesQueryParams,
} from './edp-availability-service.js';
import {PostAvailableEquipmentNamesResponse} from '../defs/equipment-def.js';
import {ContextUtils} from '../utils/context-utils.js';

export type AvailableEquipmentNamesQueryParams = {
  limit?: number;
  page?: number;
  searchTerm?: string;
};

@DiService()
export class EquipmentService {
  constructor(
    private edpAvailabilityService: EDPAvailabilityService,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  public async getAvailableEquipmentNames(
    queryParams: AvailableEquipmentNamesQueryParams,
  ): Promise<PostAvailableEquipmentNamesResponse> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting available equipment names', {
      tenantId,
      facilityId,
      queryParams,
    });

    try {
      const edpParams =
        this.transformAvailableEquipmentNamesRequestToEDPParams(queryParams);

      const edpResponse =
        await this.edpAvailabilityService.getAvailableEquipmentNames(
          tenantId,
          facilityId,
          edpParams,
        );

      const page =
        queryParams.page && queryParams.limit
          ? Math.floor(queryParams.page / queryParams.limit) + 1
          : 1;

      return {
        data: edpResponse.data,
        metadata: {
          page,
          limit: queryParams.limit || 50,
          totalResults: edpResponse.metadata?.total ?? 0,
          totalPages:
            edpResponse.metadata?.totalPages ??
            Math.ceil(
              (edpResponse.metadata?.total ?? 0) / (queryParams.limit || 50),
            ),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get available equipment names', {
        error,
        queryParams,
      });
      throw error;
    }
  }

  private transformAvailableEquipmentNamesRequestToEDPParams(
    queryParams: AvailableEquipmentNamesQueryParams,
  ): EDPEquipmentNamesQueryParams {
    const params: EDPEquipmentNamesQueryParams = {};

    if (queryParams.page !== undefined && queryParams.limit) {
      params.page = Math.floor(queryParams.page / queryParams.limit) + 1;
    } else {
      params.page = 1;
    }

    if (queryParams.limit !== undefined) params.limit = queryParams.limit;
    if (queryParams.searchTerm) params.searchTerm = queryParams.searchTerm;

    return params;
  }
}
