import {Di<PERSON><PERSON><PERSON>, WinstonLogger, ContextService} from 'ict-api-foundations';
import {
  EDPAvailabilityService,
  EDPSectionsQueryParams,
} from './edp-availability-service.js';
import {PostAvailableSectionsResponse} from '../defs/section-def.js';
import {ContextUtils} from '../utils/context-utils.js';

export type AvailableSectionsQueryParams = {
  limit?: number;
  page?: number;
  searchTerm?: string;
};

@DiService()
export class SectionService {
  constructor(
    private edpAvailabilityService: EDPAvailabilityService,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  public async getAvailableSections(
    queryParams: AvailableSectionsQueryParams,
  ): Promise<PostAvailableSectionsResponse> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting available sections', {
      tenantId,
      facilityId,
      queryParams,
    });

    try {
      const edpParams =
        this.transformAvailableSectionsRequestToEDPParams(queryParams);

      const edpResponse =
        await this.edpAvailabilityService.getAvailableSections(
          tenantId,
          facilityId,
          edpParams,
        );

      const page =
        queryParams.page && queryParams.limit
          ? Math.floor(queryParams.page / queryParams.limit) + 1
          : 1;

      return {
        data: edpResponse.data,
        metadata: {
          page,
          limit: queryParams.limit || 50,
          totalResults: edpResponse.metadata?.total ?? 0,
          totalPages:
            edpResponse.metadata?.totalPages ??
            Math.ceil(
              (edpResponse.metadata?.total ?? 0) / (queryParams.limit || 50),
            ),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get available sections', {
        error,
        queryParams,
      });
      throw error;
    }
  }

  private transformAvailableSectionsRequestToEDPParams(
    queryParams: AvailableSectionsQueryParams,
  ): EDPSectionsQueryParams {
    const params: EDPSectionsQueryParams = {};

    if (queryParams.page !== undefined && queryParams.limit) {
      params.page = Math.floor(queryParams.page / queryParams.limit) + 1;
    } else {
      params.page = 1;
    }

    if (queryParams.limit !== undefined) params.limit = queryParams.limit;
    if (queryParams.searchTerm) params.searchTerm = queryParams.searchTerm;

    return params;
  }
}
