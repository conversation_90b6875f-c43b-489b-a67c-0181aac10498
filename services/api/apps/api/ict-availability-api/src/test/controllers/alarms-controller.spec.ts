import 'reflect-metadata';
import sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  appSetup,
  <PERSON><PERSON><PERSON><PERSON>,
  IctError,
} from 'ict-api-foundations';
import {expect} from 'chai';
import {AlarmsService} from '../../services/alarms-service.ts';
import {AlarmsController} from '../../controllers/alarms-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import type {PostAlarmsListResponse, Alarm} from '../../defs/alarm-def.ts';

describe('AlarmsController', () => {
  const app = appSetup(RegisterRoutes);
  const baseUrl = '/availability/alarms';

  let alarmsServiceStub: sinon.SinonStubbedInstance<AlarmsService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;

  const mockAlarm: Alarm = {
    id: 'test-alarm-001',
    title: 'Test Alarm Description',
    description: 'Test Alarm Description',
    tag: 'TEST_ALARM_001',
    location: {
      area: 'TestArea',
      section: 'TestSection',
      equipment: 'TestEquipment',
    },
    timing: {
      startTime: new Date('2023-01-15T10:30:00Z'),
      endTime: new Date('2023-01-15T11:30:00Z'),
      duration: '1:00:00',
      updatedStartTime: new Date('2023-01-15T10:30:00Z'),
      updatedEndTime: new Date('2023-01-15T11:30:00Z'),
      updatedDuration: '1:00:00',
    },
    status: 'ACTIVE',
    reason: 'Under investigation',
  };

  // Expected response with serialized dates (as they would appear in JSON)
  const expectedSerializedAlarm = {
    id: 'test-alarm-001',
    title: 'Test Alarm Description',
    description: 'Test Alarm Description',
    tag: 'TEST_ALARM_001',
    location: {
      area: 'TestArea',
      section: 'TestSection',
      equipment: 'TestEquipment',
    },
    timing: {
      startTime: '2023-01-15T10:30:00.000Z',
      endTime: '2023-01-15T11:30:00.000Z',
      duration: '1:00:00',
      updatedStartTime: '2023-01-15T10:30:00.000Z',
      updatedEndTime: '2023-01-15T11:30:00.000Z',
      updatedDuration: '1:00:00',
    },
    status: 'ACTIVE',
    reason: 'Under investigation',
  };

  const mockResponse: PostAlarmsListResponse = {
    data: [mockAlarm],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
    },
  };

  const expectedResponse = {
    data: [expectedSerializedAlarm],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
    },
  };

  beforeEach(() => {
    alarmsServiceStub = sinon.createStubInstance(AlarmsService);
    loggerStub = sinon.createStubInstance(WinstonLogger);

    Container.set(AlarmsService, alarmsServiceStub);
    Container.set(WinstonLogger, loggerStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /availability/alarms/list', () => {
    const endpoint = `${baseUrl}/list`;

    const validRequest = {
      start_date: '2023-01-01T00:00:00.000Z',
      end_date: '2023-01-31T23:59:59.000Z',
      limit: 20,
      offset: 0,
    };

    beforeEach(() => {
      alarmsServiceStub.getAlarms.resolves(mockResponse);
    });

    it('should successfully retrieve alarms with minimal request', async () => {
      const response = await request(app)
        .post(endpoint)
        .send(validRequest)
        .expect(200);

      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedResponse);

      // Verify service was called with correct parameters
      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.start_date).to.be.an.instanceOf(Date);
      expect(serviceCall.end_date).to.be.an.instanceOf(Date);
      expect(serviceCall.limit).to.equal(20);
      expect(serviceCall.offset).to.equal(0);
    });

    it('should handle request with all optional parameters', async () => {
      const fullRequest = {
        ...validRequest,
        filters: {
          type: 'multiple',
          areas: ['Area1', 'Area2'],
          statuses: ['ACTIVE', 'RESOLVED'],
        },
        sortFields: [
          {
            columnName: 'startTime',
            isDescending: true,
          },
        ],
        groupByFields: ['area', 'status'],
        searchString: 'test alarm',
      };

      const response = await request(app)
        .post(endpoint)
        .send(fullRequest)
        .expect(200);

      expect(response.body).to.deep.equal(expectedResponse);

      // Verify all parameters were passed to service
      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.filters).to.deep.equal(fullRequest.filters);
      expect(serviceCall.sortFields).to.deep.equal(fullRequest.sortFields);
      expect(serviceCall.groupByFields).to.deep.equal(
        fullRequest.groupByFields,
      );
      expect(serviceCall.searchString).to.equal(fullRequest.searchString);
    });

    it('should handle pagination parameters correctly', async () => {
      const paginatedRequest = {
        ...validRequest,
        limit: 50,
        offset: 100,
      };

      await request(app).post(endpoint).send(paginatedRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.limit).to.equal(50);
      expect(serviceCall.offset).to.equal(100);
    });

    it('should validate required start_date field', async () => {
      const invalidRequest = {
        end_date: '2023-01-31T23:59:59.000Z',
        limit: 20,
        offset: 0,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate required end_date field', async () => {
      const invalidRequest = {
        start_date: '2023-01-01T00:00:00.000Z',
        limit: 20,
        offset: 0,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate date format for start_date', async () => {
      const invalidRequest = {
        start_date: 'invalid-date',
        end_date: '2023-01-31T23:59:59.000Z',
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate date format for end_date', async () => {
      const invalidRequest = {
        start_date: '2023-01-01T00:00:00.000Z',
        end_date: 'invalid-date',
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate that start_date is before end_date', async () => {
      const invalidRequest = {
        start_date: '2023-01-31T23:59:59.000Z',
        end_date: '2023-01-01T00:00:00.000Z', // end before start
      };

      await request(app).post(endpoint).send(invalidRequest).expect(400);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate limit parameter is non-negative integer', async () => {
      const invalidRequest = {
        ...validRequest,
        limit: -5,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate offset parameter is non-negative integer', async () => {
      const invalidRequest = {
        ...validRequest,
        offset: -10,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should handle service errors and propagate them correctly', async () => {
      const serviceError = IctError.internalServerError('Service unavailable');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(500);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service bad request errors', async () => {
      const serviceError = IctError.badRequest('Invalid parameters');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(400);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service not found errors', async () => {
      const serviceError = IctError.notFound('No alarms found');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(404);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service unauthorized errors', async () => {
      const serviceError = IctError.unauthorized('Access denied');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(401);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should log the request information', async () => {
      await request(app).post(endpoint).send(validRequest).expect(200);

      expect(loggerStub.info.calledWith('Getting alarms list')).to.be.true;
      const logCall = loggerStub.info.firstCall.args[1] as any;
      expect(logCall).to.exist;
      expect(logCall.request).to.exist;
    });

    it('should handle empty response from service', async () => {
      const emptyResponse: PostAlarmsListResponse = {
        data: [],
        metadata: {
          page: 1,
          limit: 20,
          totalResults: 0,
        },
      };

      alarmsServiceStub.getAlarms.resolves(emptyResponse);

      const response = await request(app)
        .post(endpoint)
        .send(validRequest)
        .expect(200);

      expect(response.body).to.deep.equal(emptyResponse);
      expect(response.body.data).to.be.an('array').that.is.empty;
    });

    it('should handle large pagination requests', async () => {
      const largeRequest = {
        ...validRequest,
        limit: 1000,
        offset: 5000,
      };

      await request(app).post(endpoint).send(largeRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.limit).to.equal(1000);
      expect(serviceCall.offset).to.equal(5000);
    });

    it('should preserve date objects in service call', async () => {
      await request(app).post(endpoint).send(validRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.start_date).to.be.an.instanceOf(Date);
      expect(serviceCall.end_date).to.be.an.instanceOf(Date);
      expect(serviceCall.start_date.toISOString()).to.equal(
        validRequest.start_date,
      );
      expect(serviceCall.end_date.toISOString()).to.equal(
        validRequest.end_date,
      );
    });

    it('should handle complex filter structures', async () => {
      const complexRequest = {
        ...validRequest,
        filters: {
          type: 'multiple',
          areas: ['Area1', 'Area2', 'Area3'],
          sections: ['Section1', 'Section2'],
          equipment: ['Equipment1'],
          statuses: ['ACTIVE', 'RESOLVED', 'PENDING'],
          faultIds: ['FAULT-001', 'FAULT-002'],
          reasons: ['Maintenance', 'Investigation'],
        },
      };

      await request(app).post(endpoint).send(complexRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.filters).to.deep.equal(complexRequest.filters);
    });

    it('should handle multiple sort fields', async () => {
      const sortRequest = {
        ...validRequest,
        sortFields: [
          {
            columnName: 'startTime',
            isDescending: true,
          },
          {
            columnName: 'area',
            isDescending: false,
          },
          {
            columnName: 'status',
            isDescending: true,
          },
        ],
      };

      await request(app).post(endpoint).send(sortRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.sortFields).to.deep.equal(sortRequest.sortFields);
      expect(serviceCall.sortFields).to.have.length(3);
    });
  });

  describe('Controller static properties', () => {
    it('should have a valid example response', () => {
      expect(AlarmsController.exampleResponse).to.exist;
      expect(AlarmsController.exampleResponse.data).to.be.an('array');
      expect(AlarmsController.exampleResponse.metadata).to.exist;
      expect(AlarmsController.exampleResponse.metadata.page).to.be.a('number');
      expect(AlarmsController.exampleResponse.metadata.limit).to.be.a('number');
      expect(AlarmsController.exampleResponse.metadata.totalResults).to.be.a(
        'number',
      );

      // Validate structure of example alarm
      if (AlarmsController.exampleResponse.data.length > 0) {
        const exampleAlarm = AlarmsController.exampleResponse.data[0];
        expect(exampleAlarm.id).to.be.a('string');
        expect(exampleAlarm.title).to.be.a('string');
        expect(exampleAlarm.description).to.be.a('string');
        expect(exampleAlarm.tag).to.be.a('string');
        expect(exampleAlarm.location).to.exist;
        expect(exampleAlarm.timing).to.exist;
        expect(exampleAlarm.status).to.be.a('string');
        expect(exampleAlarm.reason).to.be.a('string');
      }
    });
  });
});
