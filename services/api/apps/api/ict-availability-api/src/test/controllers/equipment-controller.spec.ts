import sinon from 'sinon';
import {expect} from 'chai';
import {Container, WinstonLogger} from 'ict-api-foundations';
import {EquipmentController} from '../../controllers/equipment-controller.ts';
import {EquipmentService} from '../../services/equipment-service.js';
import {PostAvailableEquipmentNamesResponse} from '../../defs/equipment-def.js';

describe('EquipmentController', () => {
  let controller: EquipmentController;
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    equipmentServiceStub = sinon.createStubInstance(EquipmentService);
    loggerStub = sinon.createStubInstance(WinstonLogger);

    sinon.stub(Container, 'get').callsFake(token => {
      if (token === EquipmentService) return equipmentServiceStub;
      if (token === WinstonLogger) return loggerStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    controller = new EquipmentController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAvailableEquipmentNames', () => {
    it('should return available equipment names from the service', async () => {
      const mockResponse: PostAvailableEquipmentNamesResponse = {
        data: ['Equipment 1', 'Equipment 2'],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 2,
          totalPages: 1,
        },
      };

      equipmentServiceStub.getAvailableEquipmentNames.resolves(mockResponse);

      const result = await controller.getAvailableEquipmentNames(1, 10, 'test');

      expect(result).to.deep.equal(mockResponse);
      expect(
        equipmentServiceStub.getAvailableEquipmentNames.calledOnceWith({
          limit: 10,
          page: 1,
          searchTerm: 'test',
        }),
      ).to.be.true;
    });

    it('should handle errors from the service', async () => {
      const error = new Error('Service error');
      equipmentServiceStub.getAvailableEquipmentNames.rejects(error);

      try {
        await controller.getAvailableEquipmentNames(0, 10, 'test');
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });
  });
});
