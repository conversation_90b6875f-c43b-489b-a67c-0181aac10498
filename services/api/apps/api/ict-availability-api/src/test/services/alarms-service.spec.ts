import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  IctError,
  WinstonLogger,
  type BaseFilterType,
} from 'ict-api-foundations';
import {
  AlarmsService,
  type AlarmQueryParams,
} from '../../services/alarms-service.ts';
import {
  EDPAvailabilityService,
  type EDPAlarm,
  type EDPAlarmsResponse,
} from '../../services/edp-availability-service.ts';
import type {PostAlarmsListResponse, Alarm} from '../../defs/alarm-def.ts';

describe('AlarmsService', () => {
  let service: AlarmsService;
  let edpServiceStub: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;

  const mockTenantId = 'test_tenant_facility';
  const mockFacilityId = 'facility';

  beforeEach(() => {
    // Create stubs for dependencies
    edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);

    // Set up context service mock with getter stubs
    sinon.stub(contextServiceStub, 'datasetId').get(() => mockTenantId);
    sinon
      .stub(contextServiceStub, 'selectedFacilityId')
      .get(() => mockFacilityId);

    // Create service instance with stubs
    service = new AlarmsService(edpServiceStub, loggerStub, contextServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAlarms', () => {
    const mockQueryParams: AlarmQueryParams = {
      limit: 10,
      offset: 0,
      start_date: new Date('2023-01-01T00:00:00Z'),
      end_date: new Date('2023-01-31T23:59:59Z'),
      searchString: 'test fault',
      sortFields: [
        {
          columnName: 'startTime',
          isDescending: true,
        },
      ],
      filters: {
        type: 'multiple',
        areas: ['Area A', 'Area B'],
        statuses: ['ACTIVE'],
      } as BaseFilterType,
    };

    const mockEDPAlarm: EDPAlarm = {
      sectionArea: 'Area A',
      sectionName: 'Section 1',
      equipmentName: 'Equipment 1',
      faultId: 'FAULT-001',
      faultDescription: 'Test fault description',
      faultTag: 'TAG-001',
      faultStartTimeUtc: '2023-01-15T10:30:00Z',
      faultEndTimeUtc: '2023-01-15T11:30:00Z',
      faultDuration: 3600000,
      removalStatus: 'ACTIVE',
      removalReason: 'Under investigation',
      updatedStartTimeUtc: '2023-01-15T10:30:00Z',
      updatedEndTimeUtc: '2023-01-15T11:30:00Z',
      updatedDuration: 3600000,
    };

    const mockEDPResponse: EDPAlarmsResponse = {
      data: [mockEDPAlarm],
      metadata: {
        page: 1,
        limit: 10,
        totalItems: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    const expectedAlarm: Alarm = {
      id: 'FAULT-001',
      title: 'Test fault description',
      description: 'Test fault description',
      tag: 'TAG-001',
      location: {
        area: 'Area A',
        section: 'Section 1',
        equipment: 'Equipment 1',
      },
      timing: {
        startTime: new Date('2023-01-15T10:30:00Z'),
        endTime: new Date('2023-01-15T11:30:00Z'),
        duration: '3600000',
        updatedStartTime: new Date('2023-01-15T10:30:00Z'),
        updatedEndTime: new Date('2023-01-15T11:30:00Z'),
        updatedDuration: '3600000',
      },
      status: 'ACTIVE',
      reason: 'Under investigation',
    };

    beforeEach(() => {
      edpServiceStub.getAlarms.resolves(mockEDPResponse);
    });

    it('should successfully retrieve and transform alarms', async () => {
      const result = await service.getAlarms(mockQueryParams);

      expect(result).to.deep.equal({
        data: [expectedAlarm],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 1,
        },
      });

      expect(edpServiceStub.getAlarms.calledOnce).to.be.true;
      expect(loggerStub.info.calledWith('Getting alarms')).to.be.true;
    });

    it('should call EDP service with correct tenant and facility IDs', async () => {
      await service.getAlarms(mockQueryParams);

      expect(
        edpServiceStub.getAlarms.calledWith('test', 'tenant', sinon.match.any),
      ).to.be.true;
    });

    it('should transform query parameters correctly', async () => {
      await service.getAlarms(mockQueryParams);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];

      expect(edpParams).to.exist;
      expect(edpParams).to.deep.include({
        page: 1, // offset 0 / limit 10 + 1
        limit: 10,
        searchTerm: 'test fault',
        startTimestamp: '2023-01-01T00:00:00.000Z',
        endTimestamp: '2023-01-31T23:59:59.000Z',
        sortBy: 'faultStartTimeUtc',
        orderBy: 'DESC',
        sectionAreas: '["Area A","Area B"]',
        removalStatuses: '["ACTIVE"]',
      });
    });

    it('should handle offset to page conversion correctly', async () => {
      const params = {...mockQueryParams, offset: 20, limit: 10};
      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.page).to.equal(3); // offset 20 / limit 10 + 1 = page 3
    });

    it('should handle missing sort fields', async () => {
      const params = {...mockQueryParams};
      delete params.sortFields;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sortBy).to.be.undefined;
      expect(edpParams!.orderBy).to.be.undefined;
    });

    it('should handle missing filters', async () => {
      const params = {...mockQueryParams};
      delete params.filters;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.be.undefined;
      expect(edpParams!.removalStatuses).to.be.undefined;
    });

    it('should handle various filter types', async () => {
      const params: AlarmQueryParams = {
        ...mockQueryParams,
        filters: {
          type: 'multiple',
          areas: ['Area A'],
          sections: ['Section 1'],
          equipment: ['Equipment 1'],
          statuses: ['ACTIVE', 'RESOLVED'],
          faultIds: ['FAULT-001'],
          reasons: ['Maintenance'],
        } as BaseFilterType,
      };

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.equal('["Area A"]');
      expect(edpParams!.sectionNames).to.equal('["Section 1"]');
      expect(edpParams!.equipmentNames).to.equal('["Equipment 1"]');
      expect(edpParams!.removalStatuses).to.equal('["ACTIVE","RESOLVED"]');
      expect(edpParams!.faultIds).to.equal('["FAULT-001"]');
      expect(edpParams!.removalReasons).to.equal('["Maintenance"]');
    });

    it('should handle different sort field mappings', async () => {
      const testCases = [
        {columnName: 'area', expected: 'sectionArea'},
        {columnName: 'section', expected: 'sectionName'},
        {columnName: 'equipment', expected: 'equipmentName'},
        {columnName: 'faultId', expected: 'faultId'},
        {columnName: 'startTime', expected: 'faultStartTimeUtc'},
        {columnName: 'endTime', expected: 'faultEndTimeUtc'},
        {columnName: 'status', expected: 'removalStatus'},
        {columnName: 'updatedStartTime', expected: 'updatedStartTimeUtc'},
        {columnName: 'updatedEndTime', expected: 'updatedEndTimeUtc'},
        {columnName: 'unmappedField', expected: 'unmappedField'},
      ];

      for (const testCase of testCases) {
        const params = {
          ...mockQueryParams,
          sortFields: [
            {
              columnName: testCase.columnName,
              isDescending: false,
            },
          ],
        };

        await service.getAlarms(params);

        const edpParams = edpServiceStub.getAlarms.lastCall.args[2];
        expect(edpParams).to.exist;
        expect(edpParams!.sortBy).to.equal(testCase.expected);
        expect(edpParams!.orderBy).to.equal('ASC');
      }
    });

    it('should handle EDP service errors', async () => {
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.getAlarms.rejects(error);

      try {
        await service.getAlarms(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to get alarms')).to.be.true;
      }
    });

    it('should transform EDP alarms without optional fields', async () => {
      const minimalEDPAlarm: EDPAlarm = {
        sectionArea: 'Area A',
        sectionName: 'Section 1',
        equipmentName: 'Equipment 1',
        faultId: 'FAULT-002',
        faultDescription: 'Minimal fault',
        faultTag: 'TAG-002',
        faultStartTimeUtc: '2023-01-15T10:30:00Z',
        faultEndTimeUtc: '', // Empty string
        faultDuration: 1800000,
        removalStatus: 'ACTIVE',
        removalReason: '',
        updatedStartTimeUtc: '', // Empty string
        updatedEndTimeUtc: '',
        updatedDuration: 0,
      };

      const minimalResponse: EDPAlarmsResponse = {
        data: [minimalEDPAlarm],
        metadata: {
          page: 1,
          limit: 10,
          totalItems: 1,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(minimalResponse);

      const result = await service.getAlarms(mockQueryParams);

      expect(result.data[0].timing.endTime).to.be.undefined;
      expect(result.data[0].timing.updatedStartTime).to.be.undefined;
      expect(result.data[0].timing.updatedEndTime).to.be.undefined;
      expect(result.data[0].timing.updatedDuration).to.equal('');
    });
  });

  describe('context methods', () => {
    describe('getTenantId', () => {
      it('should extract tenant ID from context datasetId', async () => {
        sinon.restore();
        // Recreate necessary stubs after restore
        edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
        loggerStub = sinon.createStubInstance(WinstonLogger);
        contextServiceStub = sinon.createStubInstance(ContextService);

        sinon
          .stub(contextServiceStub, 'datasetId')
          .get(() => 'test_tenant_facility');
        service = new AlarmsService(
          edpServiceStub,
          loggerStub,
          contextServiceStub,
        );

        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(
          edpServiceStub.getAlarms.calledWith(
            'test',
            sinon.match.any,
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when tenant ID not found', async () => {
        sinon.restore();
        // Recreate necessary stubs after restore
        edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
        loggerStub = sinon.createStubInstance(WinstonLogger);
        contextServiceStub = sinon.createStubInstance(ContextService);

        sinon.stub(contextServiceStub, 'datasetId').get(() => '_facility'); // Empty tenant part
        service = new AlarmsService(
          edpServiceStub,
          loggerStub,
          contextServiceStub,
        );

        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(IctError);
          expect((error as IctError).message).to.equal(
            'Tenant ID not found in context',
          );
          expect(loggerStub.error.calledWith('Tenant ID not found in context'))
            .to.be.true;
        }
      });
    });

    describe('getFacilityId', () => {
      it('should extract facility ID from context datasetId split', async () => {
        sinon.restore();
        // Recreate necessary stubs after restore
        edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
        loggerStub = sinon.createStubInstance(WinstonLogger);
        contextServiceStub = sinon.createStubInstance(ContextService);

        sinon.stub(contextServiceStub, 'datasetId').get(() => 'test_facility');
        service = new AlarmsService(
          edpServiceStub,
          loggerStub,
          contextServiceStub,
        );

        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(
          edpServiceStub.getAlarms.calledWith(
            sinon.match.any,
            'facility',
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when facility ID not found', async () => {
        sinon.restore();
        // Recreate necessary stubs after restore
        edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
        loggerStub = sinon.createStubInstance(WinstonLogger);
        contextServiceStub = sinon.createStubInstance(ContextService);

        sinon.stub(contextServiceStub, 'datasetId').get(() => 'test_'); // Empty facility part
        service = new AlarmsService(
          edpServiceStub,
          loggerStub,
          contextServiceStub,
        );

        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(IctError);
          expect((error as IctError).message).to.equal(
            'Facility ID not found in context',
          );
          expect(
            loggerStub.error.calledWith('Facility ID not found in context'),
          ).to.be.true;
        }
      });
    });
  });

  describe('data transformation', () => {
    it('should correctly transform multiple EDP alarms', async () => {
      const edpAlarms: EDPAlarm[] = [
        {
          sectionArea: 'Area A',
          sectionName: 'Section 1',
          equipmentName: 'Equipment 1',
          faultId: 'FAULT-001',
          faultDescription: 'First fault',
          faultTag: 'TAG-001',
          faultStartTimeUtc: '2023-01-15T10:30:00Z',
          faultEndTimeUtc: '2023-01-15T11:30:00Z',
          faultDuration: 3600000,
          removalStatus: 'ACTIVE',
          removalReason: 'Investigating',
          updatedStartTimeUtc: '2023-01-15T10:30:00Z',
          updatedEndTimeUtc: '2023-01-15T11:30:00Z',
          updatedDuration: 3600000,
        },
        {
          sectionArea: 'Area B',
          sectionName: 'Section 2',
          equipmentName: 'Equipment 2',
          faultId: 'FAULT-002',
          faultDescription: 'Second fault',
          faultTag: 'TAG-002',
          faultStartTimeUtc: '2023-01-16T14:15:00Z',
          faultEndTimeUtc: '',
          faultDuration: 9000000,
          removalStatus: 'RESOLVED',
          removalReason: 'Fixed',
          updatedStartTimeUtc: '',
          updatedEndTimeUtc: '',
          updatedDuration: 0,
        },
      ];

      const response: EDPAlarmsResponse = {
        data: edpAlarms,
        metadata: {
          page: 1,
          limit: 10,
          totalItems: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(response);

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.data).to.have.length(2);
      expect(result.data[0].id).to.equal('FAULT-001');
      expect(result.data[1].id).to.equal('FAULT-002');
      expect(result.data[0].location.area).to.equal('Area A');
      expect(result.data[1].location.area).to.equal('Area B');
    });
  });

  describe('pagination calculation', () => {
    it('should correctly calculate page from offset', async () => {
      const testCases = [
        {offset: 0, limit: 10, expectedPage: 1},
        {offset: 10, limit: 10, expectedPage: 2},
        {offset: 25, limit: 10, expectedPage: 3},
        {offset: 50, limit: 25, expectedPage: 3},
      ];

      edpServiceStub.getAlarms.resolves({
        data: [],
        metadata: {
          page: 1,
          limit: 10,
          totalItems: 100,
          totalPages: 10,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      for (const testCase of testCases) {
        const params: AlarmQueryParams = {
          offset: testCase.offset,
          limit: testCase.limit,
          start_date: new Date(),
          end_date: new Date(),
        };

        const result = await service.getAlarms(params);

        expect(result.metadata.page).to.equal(testCase.expectedPage);
        expect(result.metadata.limit).to.equal(testCase.limit);
      }
    });

    it('should default to page 1 when offset is undefined', async () => {
      edpServiceStub.getAlarms.resolves({
        data: [],
        metadata: {
          page: 1,
          limit: 50,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.metadata.page).to.equal(1);
      expect(result.metadata.limit).to.equal(50);
    });
  });
});
