import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  IctError,
  WinstonLogger,
  type BaseFilterType,
} from 'ict-api-foundations';
import {
  AlarmsService,
  type AlarmQueryParams,
} from '../../services/alarms-service.ts';
import {
  EDPAvailabilityService,
  type EDPAlarm,
  type EDPAlarmsResponse,
} from '../../services/edp-availability-service.ts';
import type {PostAlarmsListResponse, Alarm} from '../../defs/alarm-def.ts';
import type {AlarmCreateDto} from '../../defs/alarm-create-dto.ts';
import {ContextUtils} from '../../utils/context-utils.js';

describe('AlarmsService', () => {
  let service: AlarmsService;
  let edpServiceStub: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let getTenantIdStub: sinon.SinonStub;
  let getFacilityIdStub: sinon.SinonStub;

  const mockTenantId = 'test-tenant';
  const mockFacilityId = 'test-facility';

  beforeEach(() => {
    // Create stubs for dependencies
    edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);
    getTenantIdStub = sinon
      .stub(ContextUtils, 'getTenantId')
      .returns(mockTenantId);
    getFacilityIdStub = sinon
      .stub(ContextUtils, 'getFacilityId')
      .returns(mockFacilityId);

    // Create service instance with stubs
    service = new AlarmsService(edpServiceStub, loggerStub, contextServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAlarms', () => {
    const mockQueryParams: AlarmQueryParams = {
      limit: 10,
      offset: 0,
      start_date: new Date('2023-01-01T00:00:00Z'),
      end_date: new Date('2023-01-31T23:59:59Z'),
      searchString: 'test fault',
      sortFields: [
        {
          columnName: 'startTime',
          isDescending: true,
        },
      ],
      filters: {
        type: 'multiple',
        areas: ['Area A', 'Area B'],
        statuses: ['ACTIVE'],
      } as BaseFilterType,
    };

    const mockEDPAlarm: EDPAlarm = {
      sectionArea: 'Area A',
      sectionName: 'Section 1',
      equipmentName: 'Equipment 1',
      faultId: 'FAULT-001',
      faultDescription: 'Test fault description',
      faultTag: 'TAG-001',
      faultStartTimeUtc: '2023-01-15T10:30:00Z',
      faultEndTimeUtc: '2023-01-15T11:30:00Z',
      faultDuration: 3600000,
      removalStatus: 'ACTIVE',
      removalReason: 'Under investigation',
      updatedStartTimeUtc: '2023-01-15T10:30:00Z',
      updatedEndTimeUtc: '2023-01-15T11:30:00Z',
      updatedDuration: 3600000,
    };

    const mockEDPResponse: EDPAlarmsResponse = {
      data: [mockEDPAlarm],
      metadata: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    const expectedAlarm: Alarm = {
      id: 'FAULT-001',
      title: 'Test fault description',
      description: 'Test fault description',
      tag: 'TAG-001',
      location: {
        area: 'Area A',
        section: 'Section 1',
        equipment: 'Equipment 1',
      },
      timing: {
        startTime: new Date('2023-01-15T10:30:00Z'),
        endTime: new Date('2023-01-15T11:30:00Z'),
        duration: '3600000',
        updatedStartTime: new Date('2023-01-15T10:30:00Z'),
        updatedEndTime: new Date('2023-01-15T11:30:00Z'),
        updatedDuration: '3600000',
      },
      status: 'ACTIVE',
      reason: 'Under investigation',
    };

    beforeEach(() => {
      edpServiceStub.getAlarms.resolves(mockEDPResponse);
    });

    it('should successfully retrieve and transform alarms', async () => {
      const result = await service.getAlarms(mockQueryParams);

      expect(result).to.deep.equal({
        data: [expectedAlarm],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 1,
          totalPages: 1,
        },
      });

      expect(edpServiceStub.getAlarms.calledOnce).to.be.true;
      expect(loggerStub.info.calledWith('Getting alarms')).to.be.true;
    });

    it('should call EDP service with correct tenant and facility IDs', async () => {
      await service.getAlarms(mockQueryParams);

      expect(
        edpServiceStub.getAlarms.calledWith(
          mockTenantId,
          mockFacilityId,
          sinon.match.any,
        ),
      ).to.be.true;
    });

    it('should transform query parameters correctly', async () => {
      await service.getAlarms(mockQueryParams);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];

      expect(edpParams).to.exist;
      expect(edpParams).to.deep.include({
        page: 1, // offset 0 / limit 10 + 1
        limit: 10,
        searchTerm: 'test fault',
        startTimestamp: '2023-01-01T00:00:00.000Z',
        endTimestamp: '2023-01-31T23:59:59.000Z',
        sortBy: 'faultStartTimeUtc',
        orderBy: 'DESC',
        sectionAreas: '["Area A","Area B"]',
        removalStatuses: '["ACTIVE"]',
      });
    });

    it('should handle offset to page conversion correctly', async () => {
      const params = {...mockQueryParams, offset: 20, limit: 10};
      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.page).to.equal(3); // offset 20 / limit 10 + 1 = page 3
    });

    it('should handle missing sort fields', async () => {
      const params = {...mockQueryParams};
      delete params.sortFields;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sortBy).to.be.undefined;
      expect(edpParams!.orderBy).to.be.undefined;
    });

    it('should handle missing filters', async () => {
      const params = {...mockQueryParams};
      delete params.filters;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.be.undefined;
      expect(edpParams!.removalStatuses).to.be.undefined;
    });

    it('should handle various filter types', async () => {
      const params: AlarmQueryParams = {
        ...mockQueryParams,
        filters: {
          type: 'multiple',
          areas: ['Area A'],
          sections: ['Section 1'],
          equipment: ['Equipment 1'],
          statuses: ['ACTIVE', 'RESOLVED'],
          faultIds: ['FAULT-001'],
          reasons: ['Maintenance'],
        } as BaseFilterType,
      };

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.equal('["Area A"]');
      expect(edpParams!.sectionNames).to.equal('["Section 1"]');
      expect(edpParams!.equipmentNames).to.equal('["Equipment 1"]');
      expect(edpParams!.removalStatuses).to.equal('["ACTIVE","RESOLVED"]');
      expect(edpParams!.faultIds).to.equal('["FAULT-001"]');
      expect(edpParams!.removalReasons).to.equal('["Maintenance"]');
    });

    it('should handle different sort field mappings', async () => {
      const testCases = [
        {columnName: 'area', expected: 'sectionArea'},
        {columnName: 'section', expected: 'sectionName'},
        {columnName: 'equipment', expected: 'equipmentName'},
        {columnName: 'faultId', expected: 'faultId'},
        {columnName: 'startTime', expected: 'faultStartTimeUtc'},
        {columnName: 'endTime', expected: 'faultEndTimeUtc'},
        {columnName: 'status', expected: 'removalStatus'},
        {columnName: 'updatedStartTime', expected: 'updatedStartTimeUtc'},
        {columnName: 'updatedEndTime', expected: 'updatedEndTimeUtc'},
        {columnName: 'unmappedField', expected: 'unmappedField'},
      ];

      for (const testCase of testCases) {
        const params = {
          ...mockQueryParams,
          sortFields: [
            {
              columnName: testCase.columnName,
              isDescending: false,
            },
          ],
        };

        await service.getAlarms(params);

        const edpParams = edpServiceStub.getAlarms.lastCall.args[2];
        expect(edpParams).to.exist;
        expect(edpParams!.sortBy).to.equal(testCase.expected);
        expect(edpParams!.orderBy).to.equal('ASC');
      }
    });

    it('should handle EDP service errors', async () => {
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.getAlarms.rejects(error);

      try {
        await service.getAlarms(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to get alarms')).to.be.true;
      }
    });

    it('should transform EDP alarms without optional fields', async () => {
      const minimalEDPAlarm: EDPAlarm = {
        sectionArea: 'Area A',
        sectionName: 'Section 1',
        equipmentName: 'Equipment 1',
        faultId: 'FAULT-002',
        faultDescription: 'Minimal fault',
        faultTag: 'TAG-002',
        faultStartTimeUtc: '2023-01-15T10:30:00Z',
        faultEndTimeUtc: '', // Empty string
        faultDuration: 1800000,
        removalStatus: 'ACTIVE',
        removalReason: '',
        updatedStartTimeUtc: '', // Empty string
        updatedEndTimeUtc: '',
        updatedDuration: 0,
      };

      const minimalResponse: EDPAlarmsResponse = {
        data: [minimalEDPAlarm],
        metadata: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(minimalResponse);

      const result = await service.getAlarms(mockQueryParams);

      expect(result.data[0].timing.endTime).to.be.undefined;
      expect(result.data[0].timing.updatedStartTime).to.be.undefined;
      expect(result.data[0].timing.updatedEndTime).to.be.undefined;
      expect(result.data[0].timing.updatedDuration).to.equal('0');
    });
  });

  describe('createAlarm', () => {
    beforeEach(() => {
      edpServiceStub.createAlarm.reset();
    });
    it('should successfully create a new alarm', async () => {
      const validAlarmCreateRequest: AlarmCreateDto = {
        displayName: 'Test Alarm',
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: true,
        section: 'TestSection',
        equipment: 'TestEquipment',
        description: 'Test alarm description',
        comments: 'No additional comments',
      };
      edpServiceStub.createAlarm.resolves();
      await service.createAlarm(validAlarmCreateRequest);
      expect(edpServiceStub.createAlarm.calledOnce).to.be.true;
      expect(edpServiceStub.createAlarm.firstCall.args).to.deep.equal([
        mockTenantId,
        mockFacilityId,
        validAlarmCreateRequest,
      ]);
    });

    it('should throw error if edp service fails to create alarm', async () => {
      const validAlarmCreateRequest: AlarmCreateDto = {
        displayName: 'Test Alarm',
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: true,
        section: 'TestSection',
        equipment: 'TestEquipment',
        description: 'Test alarm description',
        comments: 'No additional comments',
      };
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.createAlarm.rejects(error);

      try {
        await service.createAlarm(validAlarmCreateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to create alarm')).to.be
          .true;
      }
    });
  });

  describe('context methods', () => {
    describe('getTenantId', () => {
      it('should extract tenant ID from context datasetId', async () => {
        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(getTenantIdStub.calledOnce).to.be.true;
        expect(
          edpServiceStub.getAlarms.calledWith(
            mockTenantId,
            sinon.match.any,
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when tenant ID not found', async () => {
        getTenantIdStub.throws(new Error('Tenant ID not found in context'));
        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(Error);
          expect((error as Error).message).to.equal(
            'Tenant ID not found in context',
          );
        }
      });
    });

    describe('getFacilityId', () => {
      it('should extract facility ID from context datasetId split', async () => {
        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(getFacilityIdStub.calledOnce).to.be.true;
        expect(
          edpServiceStub.getAlarms.calledWith(
            sinon.match.any,
            mockFacilityId,
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when facility ID not found', async () => {
        getFacilityIdStub.throws(new Error('Facility ID not found in context'));
        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(Error);
          expect((error as Error).message).to.equal(
            'Facility ID not found in context',
          );
        }
      });
    });
  });

  describe('data transformation', () => {
    it('should correctly transform multiple EDP alarms', async () => {
      const edpAlarms: EDPAlarm[] = [
        {
          sectionArea: 'Area A',
          sectionName: 'Section 1',
          equipmentName: 'Equipment 1',
          faultId: 'FAULT-001',
          faultDescription: 'First fault',
          faultTag: 'TAG-001',
          faultStartTimeUtc: '2023-01-15T10:30:00Z',
          faultEndTimeUtc: '2023-01-15T11:30:00Z',
          faultDuration: 3600000,
          removalStatus: 'ACTIVE',
          removalReason: 'Investigating',
          updatedStartTimeUtc: '2023-01-15T10:30:00Z',
          updatedEndTimeUtc: '2023-01-15T11:30:00Z',
          updatedDuration: 3600000,
        },
        {
          sectionArea: 'Area B',
          sectionName: 'Section 2',
          equipmentName: 'Equipment 2',
          faultId: 'FAULT-002',
          faultDescription: 'Second fault',
          faultTag: 'TAG-002',
          faultStartTimeUtc: '2023-01-16T14:15:00Z',
          faultEndTimeUtc: '',
          faultDuration: 9000000,
          removalStatus: 'RESOLVED',
          removalReason: 'Fixed',
          updatedStartTimeUtc: '',
          updatedEndTimeUtc: '',
          updatedDuration: 0,
        },
      ];

      const response: EDPAlarmsResponse = {
        data: edpAlarms,
        metadata: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(response);

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.data).to.have.length(2);
      expect(result.data[0].id).to.equal('FAULT-001');
      expect(result.data[1].id).to.equal('FAULT-002');
      expect(result.data[0].location.area).to.equal('Area A');
      expect(result.data[1].location.area).to.equal('Area B');
    });
  });

  describe('pagination calculation', () => {
    it('should correctly calculate page from offset', async () => {
      const testCases = [
        {offset: 0, limit: 10, expectedPage: 1},
        {offset: 10, limit: 10, expectedPage: 2},
        {offset: 25, limit: 10, expectedPage: 3},
        {offset: 50, limit: 25, expectedPage: 3},
      ];

      edpServiceStub.getAlarms.resolves({
        data: [],
        metadata: {
          page: 1,
          limit: 10,
          total: 100,
          totalPages: 10,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      for (const testCase of testCases) {
        const params: AlarmQueryParams = {
          offset: testCase.offset,
          limit: testCase.limit,
          start_date: new Date(),
          end_date: new Date(),
        };

        const result = await service.getAlarms(params);

        expect(result.metadata.page).to.equal(testCase.expectedPage);
        expect(result.metadata.limit).to.equal(testCase.limit);
      }
    });

    it('should default to page 1 when offset is undefined', async () => {
      edpServiceStub.getAlarms.resolves({
        data: [],
        metadata: {
          page: 1,
          limit: 50,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.metadata.page).to.equal(1);
      expect(result.metadata.limit).to.equal(50);
    });
  });

  describe('getAlarmById', () => {
    const mockAlarmId = 'b307f935';
    const mockEDPAlarm: EDPAlarm = {
      sectionArea: 'DMSInboundMergeSorter',
      sectionName: 'ENET32',
      equipmentName: 'ENET32_COMM',
      faultId: 'b307f935',
      faultDescription: 'ENET32 COMMUNICATION ERROR',
      faultTag: 'ENET32_COMM_ERR',
      faultStartTimeUtc: '2025-07-10T21:45:06.000Z',
      faultEndTimeUtc: '2025-07-10T21:46:02.000Z',
      faultDuration: 56000,
      removalStatus: 'KEPT',
      removalReason: 'KEPT',
      updatedStartTimeUtc: '2025-07-10T21:45:06.000Z',
      updatedEndTimeUtc: '2025-07-10T21:46:02.000Z',
      updatedDuration: 56,
    };

    const expectedAlarm: Alarm = {
      id: 'b307f935',
      title: 'ENET32 COMMUNICATION ERROR',
      description: 'ENET32 COMMUNICATION ERROR',
      tag: 'ENET32_COMM_ERR',
      location: {
        area: 'DMSInboundMergeSorter',
        section: 'ENET32',
        equipment: 'ENET32_COMM',
      },
      timing: {
        startTime: new Date('2025-07-10T21:45:06.000Z'),
        endTime: new Date('2025-07-10T21:46:02.000Z'),
        duration: '56000',
        updatedStartTime: new Date('2025-07-10T21:45:06.000Z'),
        updatedEndTime: new Date('2025-07-10T21:46:02.000Z'),
        updatedDuration: '56',
      },
      status: 'KEPT',
      reason: 'KEPT',
    };

    beforeEach(() => {
      edpServiceStub.getAlarmById.resolves(mockEDPAlarm);
    });

    it('should successfully retrieve and transform a single alarm', async () => {
      const result = await service.getAlarmById(mockAlarmId);

      expect(result).to.deep.equal(expectedAlarm);

      // Verify EDP service was called with correct parameters
      expect(edpServiceStub.getAlarmById.calledOnce).to.be.true;
      expect(edpServiceStub.getAlarmById.firstCall.args[0]).to.equal(
        mockTenantId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[1]).to.equal(
        mockFacilityId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[2]).to.equal(
        mockAlarmId,
      );

      // Verify logging
      expect(loggerStub.info.calledWith('Getting alarm by ID')).to.be.true;
    });

    it('should handle undefined/null fields in EDP response', async () => {
      const incompleteEDPAlarm: EDPAlarm = {
        ...mockEDPAlarm,
        faultDescription: '',
        faultTag: '',
        faultEndTimeUtc: '',
        updatedStartTimeUtc: '',
        updatedEndTimeUtc: '',
        updatedDuration: 0,
      };

      edpServiceStub.getAlarmById.resolves(incompleteEDPAlarm);

      const result = await service.getAlarmById(mockAlarmId);

      expect(result.title).to.equal('');
      expect(result.description).to.equal('');
      expect(result.tag).to.equal('');
      expect(result.timing.endTime).to.be.undefined;
      expect(result.timing.updatedStartTime).to.be.undefined;
      expect(result.timing.updatedEndTime).to.be.undefined;
      expect(result.timing.updatedDuration).to.equal('0');
    });

    it('should propagate EDP service errors', async () => {
      const serviceError = IctError.notFound('Alarm not found');
      edpServiceStub.getAlarmById.rejects(serviceError);

      try {
        await service.getAlarmById(mockAlarmId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.equal(serviceError);
      }

      expect(loggerStub.error.calledWith('Failed to get alarm by ID')).to.be
        .true;
    });

    it('should extract tenant and facility IDs from context', async () => {
      await service.getAlarmById(mockAlarmId);

      // Verify the correct tenant and facility IDs were extracted and used
      expect(edpServiceStub.getAlarmById.firstCall.args[0]).to.equal(
        mockTenantId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[1]).to.equal(
        mockFacilityId,
      );
    });

    it('should handle different alarm ID formats', async () => {
      const testIds = ['abc123', 'test-alarm-001', 'b307f935', '12345678'];

      for (const testId of testIds) {
        await service.getAlarmById(testId);
        expect(
          edpServiceStub.getAlarmById.calledWith(
            mockTenantId,
            mockFacilityId,
            testId,
          ),
        ).to.be.true;
      }
    });

    it('should handle missing context data gracefully', async () => {
      getTenantIdStub.throws(new Error('Tenant ID not found in context'));

      try {
        await service.getAlarmById(mockAlarmId);
        expect.fail('Should have thrown an error for missing tenant ID');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });

    it('should log detailed information for debugging', async () => {
      await service.getAlarmById(mockAlarmId);

      const logCall = loggerStub.info.firstCall;
      expect(logCall.args[0]).to.equal('Getting alarm by ID');
      expect(logCall.args[1]).to.have.property('tenantId', mockTenantId);
      expect(logCall.args[1]).to.have.property('facilityId', mockFacilityId);
      expect(logCall.args[1]).to.have.property('alarmId', mockAlarmId);
    });
  });
});
