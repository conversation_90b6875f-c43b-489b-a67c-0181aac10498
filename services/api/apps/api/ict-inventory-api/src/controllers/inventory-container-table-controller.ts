import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
  Produces,
} from 'tsoa';
import {
  BaseFilterType,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  type PaginatedRequestNoDates,
  ProtectedRouteMiddleware,
  ExportFormatter,
} from 'ict-api-foundations';
import {Readable} from 'stream';
import {
  FlattenedInventoryContainerData,
  PostInventoryContainersListResponse,
} from '../defs/inventory-container-def.ts';
import {InventoryService} from '../services/inventory-service.ts';
import {IncludedColumns} from '../defs/inventory-export-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryContainerTableController extends Controller {
  public static readonly exampleData: FlattenedInventoryContainerData = {
    container_id: 'AN0884',
    location_id: 'LOC987',
    zone: 'Zone A',
    sku: 'TRL_4536',
    quantity: 150,
    last_activity_date: '2024-11-03 09:15:10 AM',
    last_cycle_count: '2024-10-30 11:45:20 AM',
    data_updated: '2024-11-05 08:00:00 AM',
    free_cycle_count: null,
  };

  public static readonly exampleConfig = {
    limit: 10,
    page: 1,
    totalResults: 2,
  };

  public static readonly exampleResponse: PostInventoryContainersListResponse =
    {
      data: [InventoryContainerTableController.exampleData],
      metadata: InventoryContainerTableController.exampleConfig,
    };

  /**
   * Post inventory container data with filters
   *
   * @param {PaginatedRequestNoDates & {byPassConfigSetting?: boolean}} paginatedRequest - Request body with filters, sorting, and pagination
   * @param {boolean} [paginatedRequest.byPassConfigSetting] - If true, bypasses configuration settings and retrieves all data without applying work area filters.
   * @returns {Promise<PostInventoryContainersListResponse>} Paginated response with container data
   * @throws IctError
   */
  @Example(InventoryContainerTableController.exampleResponse)
  @SuccessResponse('200')
  @Post('/containers/list')
  @OperationId('PostInventoryContainersList')
  @Tags('inventory')
  public async postInventoryContainersList(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {byPassConfigSetting?: boolean},
  ): Promise<PostInventoryContainersListResponse> {
    const inventoryService = Container.get(InventoryService);
    const {
      filters,
      sortFields,
      page,
      limit,
      byPassConfigSetting,
      searchString,
    } = paginatedRequest;

    // Pass filters and params to the service
    const containerListContract =
      await inventoryService.getInventoryContainerDataAsync({
        reqFilter: filters as BaseFilterType,
        sortFields,
        page,
        limit,
        byPassConfigSetting,
        searchString,
      });
    return containerListContract;
  }

  /**
   *
   * @returns {Promise<Readable>} Excel file as a readable stream
   * @throws IctError
   */

  @SuccessResponse('200')
  @Post('/containers/list/export')
  @OperationId('PostInventoryContainersListExport')
  @Tags('inventory')
  @Produces('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  public async postInventoryContainersListExcelFile(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      byPassConfigSetting?: boolean;
      columns: IncludedColumns;
    },
  ): Promise<Readable> {
    const inventoryService = Container.get(InventoryService);

    // What's a reasonable limit when we want it all but need to provide a limit
    // or repeat the existing query? Keep the filters, set the rest for export.
    const containersListContract =
      await inventoryService.getInventoryContainerDataAsync({
        reqFilter: paginatedRequest.filters as BaseFilterType,
        sortFields: paginatedRequest.sortFields || [],
        page: 0,
        limit: 100000,
        byPassConfigSetting: paginatedRequest.byPassConfigSetting,
        searchString: paginatedRequest.searchString,
      });

    const updatedReponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      containersListContract.data,
    );

    const buffer: Buffer = ExportFormatter.generateExcelFile(updatedReponse);

    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `Containers_Forward-Pick_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
