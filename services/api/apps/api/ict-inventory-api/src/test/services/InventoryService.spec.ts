import {
  BigQueryDate,
  BigQueryDatetime,
  BigQueryTimestamp,
} from '@google-cloud/bigquery';
import {expect} from 'chai';
import {
  ChartSeriesData,
  ChartSeriesPercentageData,
  PaginatedResults,
} from 'ict-api-foundations';
import sinon from 'sinon';
import {Readable} from 'stream';
import * as XLSX from 'xlsx';
import {InventoryAreaFilterDefinition} from '../../defs/inventory-area-filter-def.ts';
import {InventoryHighImpactSKU} from '../../defs/inventory-high-impact-sku-def.ts';
import {
  InventoryPerformanceSeriesData,
  InventoryPerformanceSeriesQueryResponse,
} from '../../defs/inventory-performance-series-def.ts';
import {InventoryContainerEventsData} from '../../defs/inventory-container-events-list-def.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryStore} from '../../stores/inventory-store.ts';
import {InventoryAdvicesInProgressQueryResponse} from '../../defs/inventory-advices-in-progress-def.ts';
import {AdvicesOutstandingQueryResponse} from '../../defs/inventory-advices-outstanding-def.ts';
import {AdvicesFinishedQueryResponse} from '../../defs/inventory-finished-advices-def.ts';
import {AdvicesCycleTimeQueryResponse} from '../../defs/inventory-advices-cycle-time-def.ts';
import {
  AdvicesListData,
  AdvicesListQueryResponse,
} from '../../defs/inventory-advices-list-def.ts';
import {InventoryStockDistributionOverData} from '../../defs/inventory-stock-distribution-over-def.ts';
import {InventoryStockDistributionUnderData} from '../../defs/inventory-stock-distribution-under-def.ts';
import {InventoryStockDistributionNoData} from '../../defs/inventory-stock-distribution-no-def.ts';
import {InventoryStockDistributionAtData} from '../../defs/inventory-distribution-stat-history-def.ts';
import {AdvicesDetailsQueryResponse} from '../../defs/inventory-advices-details-drawer-def.ts';
import {
  InventoryForecastListing,
  InventoryForecastListingConfig,
  InventoryForecastListingData,
  InventoryForecastListingParams,
  InventoryForecastListingQueryResponse,
  InventoryForecastSkuLocationAreas,
  InventoryForecastSkuOrderDetails,
  InventoryForecastSkuOrders,
  InventorySkuForecastDetails,
  InventorySkuForecastLocationData,
  InventorySkuLocationResponse,
} from '../../defs/inventory-forecast-def.ts';
import {InventoryContainerData} from '../../defs/inventory-container-def.ts';
import {DataFlowManager} from '../../utils/data-flow-manager.ts';
import {
  InventoryContainerEventsKpiContract,
  InventoryContainerEventsKpiResponse,
} from '../../defs/inventory-container-events-kpi-def.ts';
import {
  InventoryReplenishmentDetails,
  InventoryReplenishmentQueryResponse,
  TaskTypeData,
} from '../../defs/inventory-replenishment-details-def.ts';
import {
  InventoryUploadRecentActivity,
  InventoryUploadRecentActivityQueryResponse,
} from '../../defs/inventory-upload-def.ts';
import {
  InventoryBinLocation,
  InventoryBinLocationQueryResponse,
} from '../../defs/inventory-bin-locations-def.ts';

describe('InventoryService', () => {
  let inventoryService: InventoryService;
  let inventoryStoreStub: sinon.SinonStubbedInstance<InventoryStore>;

  beforeEach(() => {
    // Setup inventory store stub
    inventoryStoreStub = sinon.createStubInstance(InventoryStore);
    inventoryService = new InventoryService(inventoryStoreStub);
  });

  describe('getInventoryAreaAsync', () => {
    it('should call getInventoryAreaFilterAsync method of InventoryStore', async () => {
      const areaFilterData: InventoryAreaFilterDefinition = {
        areaFilterTable: ['Filter 1', 'Filter 2'],
      };

      inventoryStoreStub.getInventoryAreaFilterAsync.resolves(areaFilterData);

      const result = await inventoryService.getInventoryAreaAsync();

      expect(result).to.deep.equal(areaFilterData);
    });
  });

  describe('getAdvicesFinished', () => {
    let mockQueryResponse: AdvicesFinishedQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {finished_advices: 2};
      inventoryStoreStub.getAdvicesFinished.resolves(mockQueryResponse);
    });

    it('should get outstanding advices', async () => {
      const actual = await inventoryService.getAdvicesFinished();
      const expected = {finishedAdvices: 2};
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getAdvicesList', () => {
    let mockQueryResponse: AdvicesListQueryResponse[];

    beforeEach(() => {
      mockQueryResponse = [
        {
          advice_id: 'ADV01',
          advice_status: 'In progress',
          owner_id: 'OWN03',
          advice_type: 'SUPPLIER',
          supplier_id: 'SUP01',
          created_time: new BigQueryTimestamp('2024-01-01T00:00:00.000Z'),
          start_time: new BigQueryTimestamp('2024-01-02T00:00:00.000Z'),
          finished_time: null,
          advice_lines: 7,
          delivered_lines: 3,
          overdelivered_lines: 2,
          underdelivered_lines: 1,
        },
      ];
      inventoryStoreStub.getAdvicesList.resolves(mockQueryResponse);
    });

    it('should get advices list', async () => {
      const actual = await inventoryService.getAdvicesList();
      const expected: AdvicesListData[] = [
        {
          adviceId: 'ADV01',
          adviceStatus: 'In progress',
          ownerId: 'OWN03',
          adviceType: 'SUPPLIER',
          supplierId: 'SUP01',
          createdTime: '2024-01-01T00:00:00.000Z',
          startTime: '2024-01-02T00:00:00.000Z',
          finishedTime: null,
          adviceLines: 7,
          deliveredLines: 3,
          overdeliveredLines: 2,
          underdeliveredLines: 1,
        },
      ];
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getAdviceDetails', () => {
    const adviceId = 'ADV01';
    const mockQueryResponse: AdvicesDetailsQueryResponse[] = [
      {
        supplier_id: '001',
        handling_unit_id: 'HU-00001',
        handling_unit_type: 'PALLET',
        advice_line: 'ADV-00003#1',
        sku: 'SKU-00001',
        quantity: 10,
        packaging_level: 'CASE',
      },
    ];

    beforeEach(() => {
      inventoryStoreStub.getAdviceDetails.resolves(mockQueryResponse);
    });

    it('should get advice details', async () => {
      const actual = await inventoryService.getAdviceDetails(adviceId);
      expect(actual).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getAdvicesCycleTime', () => {
    let mockQueryResponse: AdvicesCycleTimeQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {cycle_time: 2};
      inventoryStoreStub.getAdvicesCycleTime.resolves(mockQueryResponse);
    });

    it('should get cycle time of advices', async () => {
      const actual = await inventoryService.getAdvicesCycleTime();
      const expected = {cycleTime: 2};
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getInventoryHighImpactSkuListData', () => {
    it('should get high impact sku list', async () => {
      const expected: PaginatedResults<InventoryHighImpactSKU[]> = {
        list: [
          {
            sku: '123456',
            accuracy: 0.9,
            storageArea: 'A',
            quantity: 100,
            cubeUtilization: 0.5,
            daysOnHand: 10,
          },
        ],
        totalResults: 1,
      };
      inventoryStoreStub.getInventoryHighImpactSkuListAsync.resolves(expected);
      const actual = await inventoryService.getInventoryHighImpactSkuListAsync({
        reqFilter: undefined,
        sortFields: undefined,
        page: 1,
        limit: 10,
        startDate: '2021-01-01',
        endDate: '2021-01-31',
      });
      expect(actual).to.deep.equal({
        data: expected.list,
        metadata: {page: 1, limit: 10, totalResults: expected.totalResults},
      });
    });
  });

  describe('getAdvicesOutstanding', () => {
    let mockQueryResponse: AdvicesOutstandingQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {outstanding_advices: 2};
      inventoryStoreStub.getAdvicesOutstanding.resolves(mockQueryResponse);
    });

    it('should get outstanding advices', async () => {
      const actual = await inventoryService.getAdvicesOutstanding();
      const expected = {outstandingAdvices: 2};
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getInventoryHandlingUnitsTrayedAsync', () => {
    it('should get handling units trayed between start_date and end_date', async () => {
      const startDate = '2021-11-03T05:29:05.5576Z';
      const endDate = '2021-11-05T05:29:05.5576Z';
      const expected = {
        timePeriodInHours: 48,
        data: {
          handlingUnitsTrayed: 480,
        },
      };

      const actual =
        await inventoryService.getInventoryHandlingUnitsTrayedAsync(
          startDate,
          endDate,
        );
      expect(actual).to.deep.equal(expected);
    });
  });
  describe('getInventoryAccuracyMonthlyAggregatedSeries', () => {
    const mockQueryResponse: InventoryPerformanceSeriesQueryResponse[] = [
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 80,
      },
    ];
    it('should return the correct data', async () => {
      inventoryStoreStub.getInventoryAccuracyMonthlyAggregatedSeries.resolves(
        mockQueryResponse,
      );

      const result =
        await inventoryService.getInventoryAccuracyMonthlyAggregatedSeries({
          startDate: '2023-10-01',
          endDate: '2023-11-01',
          departmentFilter: 'All',
        });
      const expected: InventoryPerformanceSeriesData = {
        name: 'Inventory Accuracy',
        series: [
          {
            name: '2023-10-01',
            value: 90,
          },
          {
            name: '2023-11-01',
            value: 80,
          },
        ],
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getOrderFulfillmentRateMonthlyAggregatedSeries', () => {
    const mockQueryResponse: InventoryPerformanceSeriesQueryResponse[] = [
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 80,
      },
    ];
    it('should return the correct data', async () => {
      inventoryStoreStub.getOrderFulfillmentRateMonthlyAggregatedSeries.resolves(
        mockQueryResponse,
      );

      const result =
        await inventoryService.getOrderFulfillmentRateMonthlyAggregatedSeries({
          startDate: '2023-10-01',
          endDate: '2023-11-01',
          departmentFilter: 'All',
        });
      const expected: InventoryPerformanceSeriesData = {
        name: 'Order Fulfillment',
        series: [
          {
            name: '2023-10-01',
            value: 90,
          },
          {
            name: '2023-11-01',
            value: 80,
          },
        ],
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getStorageUtilizationMonthlyAggregatedSeries', () => {
    const mockQueryResponse: InventoryPerformanceSeriesQueryResponse[] = [
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 80,
      },
    ];
    it('should return the correct data', async () => {
      inventoryStoreStub.getStorageUtilizationMonthlyAggregatedSeries.resolves(
        mockQueryResponse,
      );

      const result =
        await inventoryService.getStorageUtilizationMonthlyAggregatedSeries({
          startDate: '2023-10-01',
          endDate: '2023-11-01',
          departmentFilter: 'All',
        });
      const expected: InventoryPerformanceSeriesData = {
        name: 'Storage Utilization',
        series: [
          {
            name: '2023-10-01',
            value: 90,
          },
          {
            name: '2023-11-01',
            value: 80,
          },
        ],
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getInventoryAdvicesInProgress', () => {
    let mockQueryResponse: InventoryAdvicesInProgressQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {
        in_progress_advices: 2,
      };
      inventoryStoreStub.getInventoryAdvicesInProgress.resolves(
        mockQueryResponse,
      );
    });

    it('should get advices in progress', async () => {
      const actual = await inventoryService.getInventoryAdvicesInProgress();
      const expected = {
        inProgressAdvices: 2,
      };
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getStockOutMonthlyAggregatedSeries', () => {
    const mockQueryResponse: InventoryPerformanceSeriesQueryResponse[] = [
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 80,
      },
    ];
    it('should return the correct data', async () => {
      inventoryStoreStub.getStockOutMonthlyAggregatedSeries.resolves(
        mockQueryResponse,
      );

      const result = await inventoryService.getStockOutMonthlyAggregatedSeries({
        startDate: '2023-10-01',
        endDate: '2023-11-01',
        departmentFilter: 'All',
      });
      const expected: InventoryPerformanceSeriesData = {
        name: 'Stock Out',
        series: [
          {
            name: '2023-10-01',
            value: 90,
          },
          {
            name: '2023-11-01',
            value: 80,
          },
        ],
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getInventoryContainerDataAsync', () => {
    let mockQueryResponse: PaginatedResults<InventoryContainerData[]>;

    beforeEach(() => {
      mockQueryResponse = {
        list: [
          {
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: {value: '2024-11-03 09:15:10 AM'},
            last_cycle_count: {value: '2024-10-30 11:45:20 AM'},
            data_updated: {value: '2024-11-05 08:00:00 AM'},
            free_cycle_count: null,
          },
        ],
        totalResults: 1,
      };

      inventoryStoreStub.getInventoryContainersList.resolves(mockQueryResponse);
    });

    it('should get inventory container data list', async () => {
      const actual = await inventoryService.getInventoryContainerDataAsync({
        page: 1,
        limit: 50,
      });

      const expected = {
        data: [
          {
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: '2024-11-03 09:15:10 AM',
            last_cycle_count: '2024-10-30 11:45:20 AM',
            data_updated: '2024-11-05 08:00:00 AM',
            free_cycle_count: null,
          },
        ],
        metadata: {
          page: 1,
          limit: 50,
          totalResults: mockQueryResponse.totalResults,
        },
      };

      expect(actual).to.deep.equal(expected);
    });

    it('should handle empty response gracefully', async () => {
      mockQueryResponse = {
        list: [],
        totalResults: 0,
      };

      inventoryStoreStub.getInventoryContainersList.resolves(mockQueryResponse);

      const actual = await inventoryService.getInventoryContainerDataAsync({
        page: 1,
        limit: 50,
      });

      const expected = {
        data: [],
        metadata: {
          page: 1,
          limit: 50,
          totalResults: 0,
        },
      };

      expect(actual).to.deep.equal(expected);
    });

    it('should pass undefined skuId when not provided', async () => {
      inventoryStoreStub.getInventoryContainersList.resolves({
        list: [],
        totalResults: 0,
      });

      const actual = await inventoryService.getInventoryContainerDataAsync({
        page: 1,
        limit: 50,
      });

      sinon.assert.calledWithExactly(
        inventoryStoreStub.getInventoryContainersList,
        sinon.match({
          filters: undefined,
          filterParams: undefined,
          sortFields: [],
          limit: 50,
          page: 1,
        }),
      );

      expect(actual).to.deep.equal({
        data: [],
        metadata: {
          page: 1,
          limit: 50,
          totalResults: 0,
        },
      });
    });
  });

  describe('getInventoryContainerEventsList', () => {
    let skuId: string;
    let mockQueryResponse: PaginatedResults<InventoryContainerEventsData[]>;

    beforeEach(() => {
      skuId = 'TRL1_STACK';
      mockQueryResponse = {
        list: [
          {
            event_date: new BigQueryDatetime('2023-06-19T00:00:00.000Z'),
            event_type: 'FREECYCLECOUNT',
            destination_container: '000000',
            operator_name: 'EUD202284',
            quantity: 0,
            workstation_code: 'M11-GTP-03',
            sku_code: 'TRL1_STACK',
          },
        ],
        totalResults: 1,
      };

      inventoryStoreStub.getInventoryContainerEventsList.resolves(
        mockQueryResponse,
      );
    });

    it('should get inventory container events list', async () => {
      const actual =
        await inventoryService.getInventoryContainerEventsListAsync(skuId, {
          page: 1,
          limit: 50,
        });

      const expected = [
        {
          timestamp: '2023-06-19 00:00:00.000',
          event: 'FREECYCLECOUNT',
          workstationCode: 'M11-GTP-03',
          destinationContainer: '000000',
          operator: 'EUD202284',
          sku: 'TRL1_STACK',
          quantity: 0,
        },
      ];
      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getInventoryWMSContainerEventsKpiAsync', () => {
    const containerId = 'CONTAINER01';

    // Mock response from the store (snake_case)
    const mockStoreResponse: InventoryContainerEventsKpiResponse[] = [
      {
        container_id: 'CONTAINER01',
        location_id: 'LOC123',
        zone: 'Zone A',
        sku: 'SKU123',
        quantity: 100,
        last_activity_date: {value: '2024-01-01T10:00:00'},
        last_cycle_count: {value: '2024-01-02T12:00:00'},
        data_updated: {value: '2024-01-03T08:00:00'},
        cycle_count_events_today: 3,
        average_daily_cycle_count: 10,
        pick_events_today: 5,
        average_daily_pick_events: 20,
      },
    ];

    // Transform store response to camelCase for service response
    const mockServiceResponse: InventoryContainerEventsKpiContract = {
      events: mockStoreResponse.map(item => ({
        containerId: item.container_id,
        locationId: item.location_id,
        zone: item.zone,
        sku: item.sku,
        quantity: item.quantity,
        lastActivityDate: item.last_activity_date.value,
        lastCycleCount: item.last_cycle_count.value,
        dataUpdated: item.data_updated.value,
        cycleCountEventsToday: item.cycle_count_events_today,
        averageDailyCycleCount: item.average_daily_cycle_count,
        pickEventsToday: item.pick_events_today,
        averageDailyPickEvents: item.average_daily_pick_events,
      })),
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryWMSContainerEventsKpiAsync.resolves(
        mockStoreResponse,
      );
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should fetch container events KPI data from the store and return the correct response', async () => {
      const actual =
        await inventoryService.getInventoryWMSContainerEventsKpiAsync(
          containerId,
        );

      expect(actual).to.deep.equal(mockServiceResponse);

      sinon.assert.calledOnceWithExactly(
        inventoryStoreStub.getInventoryWMSContainerEventsKpiAsync,
        containerId,
      );
    });

    it('should throw an error if the store method fails', async () => {
      inventoryStoreStub.getInventoryWMSContainerEventsKpiAsync.rejects(
        new Error('Something went wrong'),
      );

      await expect(
        inventoryService.getInventoryWMSContainerEventsKpiAsync(containerId),
      ).to.be.rejectedWith('Something went wrong');

      sinon.assert.calledOnceWithExactly(
        inventoryStoreStub.getInventoryWMSContainerEventsKpiAsync,
        containerId,
      );
    });
  });

  describe('getInventoryDistributionOver', () => {
    let startDate: Date;
    let endDate: Date;
    let mockStoreResponse: ChartSeriesPercentageData[];

    beforeEach(() => {
      startDate = new Date('2023-10-01');
      endDate = new Date('2023-10-03');
      mockStoreResponse = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];

      inventoryStoreStub.getOverInventoryPercentageHistoricalSeriesData.resolves(
        mockStoreResponse,
      );
    });

    it('should get inventory distribution over data', async () => {
      const expected: InventoryStockDistributionOverData = {
        overInventory: mockStoreResponse,
      };

      const actual =
        await inventoryService.getOverInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        );

      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getInventoryDistributionUnder', () => {
    let startDate: Date;
    let endDate: Date;
    let mockStoreResponse: ChartSeriesPercentageData[];

    beforeEach(() => {
      startDate = new Date('2023-10-01');
      endDate = new Date('2023-10-03');
      mockStoreResponse = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];

      inventoryStoreStub.getUnderInventoryPercentageHistoricalSeriesData.resolves(
        mockStoreResponse,
      );
    });

    it('should get inventory distribution under data', async () => {
      const expected: InventoryStockDistributionUnderData = {
        underInventory: mockStoreResponse,
      };

      const actual =
        await inventoryService.getUnderInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        );

      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getInventoryDistributionNo', () => {
    let startDate: Date;
    let endDate: Date;
    let mockStoreResponse: ChartSeriesPercentageData[];

    beforeEach(() => {
      startDate = new Date('2023-10-01');
      endDate = new Date('2023-10-03');
      mockStoreResponse = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];

      inventoryStoreStub.getNoInventoryPercentageHistoricalSeriesData.resolves(
        mockStoreResponse,
      );
    });

    it('should get inventory distribution no data', async () => {
      const expected: InventoryStockDistributionNoData = {
        noInventory: mockStoreResponse,
      };

      const actual =
        await inventoryService.getNoInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        );

      expect(actual).to.deep.equal(expected);
    });
  });

  describe('getAtInventoryPercentageHistoricalDataAsync', () => {
    let startDate: Date;
    let endDate: Date;
    let mockStoreResponse: ChartSeriesPercentageData[];

    beforeEach(() => {
      startDate = new Date('2023-10-01');
      endDate = new Date('2023-10-03');
      mockStoreResponse = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];

      inventoryStoreStub.getAtInventoryPercentageHistoricalDataAsync.resolves(
        mockStoreResponse,
      );
    });

    it('should get inventory distribution at data', async () => {
      const expected: InventoryStockDistributionAtData = {
        atInventory: mockStoreResponse,
      };

      const actual =
        await inventoryService.getAtInventoryPercentageHistoricalDataAsync(
          startDate,
          endDate,
        );

      expect(actual).to.deep.equal(expected);
    });
  });

  describe('InventoryForecastSkuLocationAreas', () => {
    const skuId = 'SKU01';
    const mockReserve: InventorySkuForecastLocationData[] = [
      {
        location_id: 'CNVPAC01',
        location_type: 'STAGE',
        container_id: 'APP834',
        container_count: 1,
        quantity: 60,
        container_quantity: 60,
        uom: 'EACHES',
        sku_size: 'L',
        length: 26,
        width: 34,
        condition_code: 'UNRESTRICTED',
        zone: 'CNVPAC01Z',
        last_activity_date_local: {value: '2024-09-03T02:31:58'},
      },
    ];
    const mockForward: InventorySkuForecastLocationData[] = [
      {
        location_id: 'MS011107200211',
        location_type: 'FAST',
        container_id: null,
        container_count: 2,
        quantity: 120,
        uom: 'EACHES',
        sku_size: 'L',
        length: 26,
        width: 34,
        condition_code: 'UNRESTRICTED',
        zone: 'ASRS',
        last_activity_date_local: {value: '2024-07-11T02:39:14'},
      },
    ];
    const mockQueryResponse: InventorySkuLocationResponse[] = [];

    mockQueryResponse.push({
      reserve: mockReserve,
      forward: mockForward,
    });

    const mockServiceResponse: InventoryForecastSkuLocationAreas = {
      data: [
        {
          area: 'Reserve Storage',
          details: [
            {
              locationId: 'CNVPAC01',
              locationType: 'STAGE',
              containerId: 'APP834',
              containerCount: 1,
              containerQuantity: 60,
              quantity: 60,
              uom: 'EACHES',
              skuSize: 'L',
              containerDimensions: '26 x 34',
              conditionCode: 'UNRESTRICTED',
              zone: 'CNVPAC01Z',
              lastActivityDate: '2024-09-03T02:31:58',
            },
          ],
        },
        {
          area: 'Forward Pick',
          details: [
            {
              locationId: 'MS011107200211',
              locationType: 'FAST',
              containerId: null,
              containerCount: 2,
              quantity: 120,
              containerQuantity: undefined,
              uom: 'EACHES',
              skuSize: 'L',
              containerDimensions: '26 x 34',
              conditionCode: 'UNRESTRICTED',
              zone: 'ASRS',
              lastActivityDate: '2024-07-11T02:39:14',
            },
          ],
        },
      ],
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryForecastDrawerLocations.resolves(
        mockQueryResponse,
      );
    });

    it('should get sku location details', async () => {
      const actual =
        await inventoryService.getInventoryForecastSkuLocations(skuId);
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('InventoryForecastSkuLOrders', () => {
    const skuId = 'SKU01';

    const mockQueryResponse = [
      {
        skuId: 'SKU01',
        order_id: 'ORDER 1',
        priority: '1',
        allocation_date: {value: '2024-06-28 13:45:57 UTC'},
        ship_date: {value: '2024-06-28'},
        order_lines: 12,
        qty_allocated: 2,
      },
      {
        skuId: 'SKU01',
        order_id: 'ORDER 2',
        priority: '1',
        allocation_date: {value: '2024-06-28 05:12:22 UTC'},
        ship_date: {value: '2024-06-28'},
        order_lines: 5,
        qty_allocated: 10,
      },
    ];

    const mockOrderOne: InventoryForecastSkuOrderDetails = {
      skuId: 'SKU01',
      orderId: 'ORDER 1',
      priority: '1',
      allocationDate: '2024-06-28 13:45:57 UTC',
      shipDate: '2024-06-28',
      orderLines: 12,
      allocatedQty: 2,
    };

    const mockOrderTwo: InventoryForecastSkuOrderDetails = {
      skuId: 'SKU01',
      orderId: 'ORDER 2',
      priority: '1',
      allocationDate: '2024-06-28 05:12:22 UTC',
      shipDate: '2024-06-28',
      orderLines: 5,
      allocatedQty: 10,
    };

    const mockServiceResponse: InventoryForecastSkuOrders = {
      skuId: 'SKU01',
      openOrderCount: 2,
      data: [mockOrderOne, mockOrderTwo],
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryForecastDrawerOrders.resolves(
        mockQueryResponse,
      );
    });

    it('should get sku open order details', async () => {
      const actual =
        await inventoryService.getInventoryForecastSkuOrders(skuId);
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('InventorySkuForecastDetails', () => {
    const skuId = 'SKU01';

    const predictedDemandSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 18,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 0,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 30,
      },
    ];

    const aactualDemandSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 12,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 0,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 26,
      },
    ];

    const confidenceLowSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 0,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 8,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 0,
      },
    ];

    const confidenceHighSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 27,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 6,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 1,
      },
    ];

    const mockQueryResponse = {
      actual_demand_series: aactualDemandSeriesData,
      forecasted_demand_series: predictedDemandSeriesData,
      demand_lower_range_series: confidenceLowSeriesData,
      demand_upper_range_series: confidenceHighSeriesData,
      confidence_quantity: 10,
      known_demand_percentage: 45,
      known_demand_qty: 85,
      short_term_daily_demand_quantity: 180,
      short_term_daily_days: 88,
      long_term_daily_demand_quantity: 178,
      long_term_daily_days: 360,
      non_zero_demand_percentage: 60,
      zero_demand_intermittency_days: 3,
    };

    const mockServiceResponse: InventorySkuForecastDetails = {
      confidence: 10,
      knownDemand: {
        quantity: 85,
        percentage: 45,
      },
      shortTermDaily: 180,
      shortTermDailyDays: 88,
      longTermDaily: 178,
      longTermDailyDays: 360,
      nonZeroDemand: 60,
      zeroDemandIntermittency: 3,
      predictedDemandSeries: predictedDemandSeriesData,
      actualDemandSeries: aactualDemandSeriesData,
      confidenceLowSeries: confidenceLowSeriesData,
      confidenceHighSeries: confidenceHighSeriesData,
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryForecastSkuDetails.resolves(
        mockQueryResponse,
      );
    });

    it('should get sku forecast details', async () => {
      const actual =
        await inventoryService.getInventoryForecastSkuForecastDetails(skuId);
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('InventoryForecastListing', () => {
    const mockQueryResponse: PaginatedResults<
      InventoryForecastListingQueryResponse[]
    > = {
      list: [
        {
          sku: 'SKU01',
          reserveStorage: 4,
          forwardPick: 6,
          pendingReplenishment: 0,
          pendingPicks: 1,
          allocatedOrders: 2,
          projectedForwardPick: 4,
          averageReplenishment: 0.03333333333333333,
          averageDemand: 1,
          demandTomorrow: null,
          knownDemand: null,
          forwardPickTomorrow: null,
          twoDayDemand: null,
          twoDayForwardPick: null,
        },
        {
          sku: 'SKU02',
          reserveStorage: 0,
          forwardPick: 37,
          pendingReplenishment: 0,
          pendingPicks: 0,
          allocatedOrders: 0,
          projectedForwardPick: 37,
          averageReplenishment: 1.0333333333333334,
          averageDemand: 1,
          demandTomorrow: 0,
          knownDemand: 0,
          forwardPickTomorrow: 37,
          twoDayDemand: 0,
          twoDayForwardPick: 37,
        },
      ],
      totalResults: 2,
    };

    const mockServiceResponse: InventoryForecastListing[] = [
      {
        sku: 'SKU01',
        current: {
          reserveStorage: 4,
          forwardPick: 6,
        },
        projected: {
          pendingReplenishment: 0,
          pendingPicks: 1,
          allocatedOrders: 2,
          projectedForwardPick: 4,
        },
        forecast: {
          averageReplenishment: 0.03333333333333333,
          averageDemand: 1,
          demandTomorrow: null,
          knownDemand: null,
          forwardPickTomorrow: null,
          twoDayDemand: null,
          twoDayForwardPick: null,
        },
      },
      {
        sku: 'SKU02',
        current: {
          reserveStorage: 0,
          forwardPick: 37,
        },
        projected: {
          pendingReplenishment: 0,
          pendingPicks: 0,
          allocatedOrders: 0,
          projectedForwardPick: 37,
        },
        forecast: {
          averageReplenishment: 1.0333333333333334,
          averageDemand: 1,
          demandTomorrow: 0,
          knownDemand: 0,
          forwardPickTomorrow: 37,
          twoDayDemand: 0,
          twoDayForwardPick: 37,
        },
      },
    ];

    const mockParams: InventoryForecastListingParams = {
      page: 1,
      limit: 50,
      sortField: [],
      reqFilter: undefined,
    };

    const mockConfig: InventoryForecastListingConfig = {
      page: 1,
      limit: 50,
      totalResults: 2,
    };

    const mockListingData: InventoryForecastListingData = {
      data: mockServiceResponse,
      metadata: mockConfig,
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryForecastListing.resolves(
        mockQueryResponse,
      );
    });

    it('should get sku location details', async () => {
      const actual =
        await inventoryService.getInventoryForecastListingAsync(mockParams);
      expect(actual).to.deep.equal(mockListingData);
    });
  });

  describe('InventoryReplenishmentDetails', () => {
    const startDate = new Date(new Date().setDate(new Date().getDate() - 7));
    const endDate = new Date();

    const mockQueryResponse: InventoryReplenishmentQueryResponse = {
      average_replenishments: 419,
      average_pending_orders: 570,
      average_cycle_times: 604,
      daily_replenishments: [
        {value: 312, name: {value: '2025-01-21'}},
        {value: 689, name: {value: '2025-01-22'}},
        {value: 452, name: {value: '2025-01-23'}},
        {value: 501, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 583, name: {value: '2025-01-27'}},
      ],
      daily_pending_orders: [
        {value: 496, name: {value: '2025-01-21'}},
        {value: 602, name: {value: '2025-01-22'}},
        {value: 568, name: {value: '2025-01-23'}},
        {value: 603, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 583, name: {value: '2025-01-27'}},
      ],
      daily_cycle_times: [
        {value: 296, name: {value: '2025-01-21'}},
        {value: 500, name: {value: '2025-01-22'}},
        {value: 568, name: {value: '2025-01-23'}},
        {value: 887, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 840, name: {value: '2025-01-27'}},
      ],
      first_shift: [
        {value: 183, name: {value: '2025-01-21'}},
        {value: 294, name: {value: '2025-01-22'}},
        {value: 307, name: {value: '2025-01-23'}},
        {value: 277, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 248, name: {value: '2025-01-27'}},
      ],
      second_shift: [
        {value: 125, name: {value: '2025-01-21'}},
        {value: 302, name: {value: '2025-01-22'}},
        {value: 127, name: {value: '2025-01-23'}},
        {value: 207, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 329, name: {value: '2025-01-27'}},
      ],
      third_shift: [
        {value: 25, name: {value: '2025-01-21'}},
        {value: 102, name: {value: '2025-01-22'}},
        {value: 107, name: {value: '2025-01-23'}},
        {value: 137, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 219, name: {value: '2025-01-27'}},
      ],
      shift_times: {
        first_endTime: '15:00',
        second_endTime: '00:00',
        third_endTime: '00:00',
        first_startTime: '6:00',
        second_startTime: '15:30',
        third_startTime: '00:00',
      },
    };

    const mockServiceResponse: InventoryReplenishmentDetails = {
      // averageDailyReplenishments: 419,
      // averagePendingOrders: 570,
      // averageCycleTimes: 604,
      dailyReplenishments: [
        {name: '2025-01-21', value: 312},
        {name: '2025-01-22', value: 689},
        {name: '2025-01-23', value: 452},
        {name: '2025-01-24', value: 501},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 583},
      ],
      dailyPendingOrders: [
        {name: '2025-01-21', value: 496},
        {name: '2025-01-22', value: 602},
        {name: '2025-01-23', value: 568},
        {name: '2025-01-24', value: 603},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 583},
      ],
      dailyCycleTimes: [
        {name: '2025-01-21', value: 296},
        {name: '2025-01-22', value: 500},
        {name: '2025-01-23', value: 568},
        {name: '2025-01-24', value: 887},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 840},
      ],
      shiftData: [
        {
          firstShift: [
            {name: '2025-01-21', value: 183},
            {name: '2025-01-22', value: 294},
            {name: '2025-01-23', value: 307},
            {name: '2025-01-24', value: 277},
            {name: '2025-01-25', value: 0},
            {name: '2025-01-26', value: 0},
            {name: '2025-01-27', value: 248},
          ],
          secondShift: [
            {name: '2025-01-21', value: 125},
            {name: '2025-01-22', value: 302},
            {name: '2025-01-23', value: 127},
            {name: '2025-01-24', value: 207},
            {name: '2025-01-25', value: 0},
            {name: '2025-01-26', value: 0},
            {name: '2025-01-27', value: 329},
          ],
          thirdShift: [
            {name: '2025-01-21', value: 25},
            {name: '2025-01-22', value: 102},
            {name: '2025-01-23', value: 107},
            {name: '2025-01-24', value: 137},
            {name: '2025-01-25', value: 0},
            {name: '2025-01-26', value: 0},
            {name: '2025-01-27', value: 219},
          ],
        },
      ],
      shiftTimes: {
        first_endTime: '15:00',
        second_endTime: '00:00',
        third_endTime: '00:00',
        first_startTime: '6:00',
        second_startTime: '15:30',
        third_startTime: '00:00',
      },
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryReplenishmentDetails.resolves(
        mockQueryResponse,
      );
    });

    it('should get sku location details', async () => {
      const actual = await inventoryService.getInventoryReplenishmentDetails(
        startDate,
        endDate,
      );
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('getReplenishmentTaskTypeSeriesData', () => {
    const startDate = new Date('2025-01-01');
    const endDate = new Date('2025-01-07');

    const mockQueryResponse: TaskTypeData = {
      demand: [
        {name: '2025-01-01', value: 15},
        {name: '2025-01-02', value: 12},
        {name: '2025-01-03', value: 18},
        {name: '2025-01-04', value: 10},
        {name: '2025-01-05', value: 22},
        {name: '2025-01-06', value: 8},
        {name: '2025-01-07', value: 14},
      ],
      topOff: [
        {name: '2025-01-01', value: 8},
        {name: '2025-01-02', value: 6},
        {name: '2025-01-03', value: 10},
        {name: '2025-01-04', value: 7},
        {name: '2025-01-05', value: 12},
        {name: '2025-01-06', value: 5},
        {name: '2025-01-07', value: 9},
      ],
      relocation: [
        {name: '2025-01-01', value: 5},
        {name: '2025-01-02', value: 3},
        {name: '2025-01-03', value: 8},
        {name: '2025-01-04', value: 4},
        {name: '2025-01-05', value: 7},
        {name: '2025-01-06', value: 2},
        {name: '2025-01-07', value: 6},
      ],
    };

    beforeEach(() => {
      inventoryStoreStub.getInventoryReplenishmentTaskTypeData.resolves(
        mockQueryResponse,
      );
    });

    it('should get replenishment task type data for the date range', async () => {
      const actual = await inventoryService.getReplenishmentTaskTypeSeriesData(
        startDate,
        endDate,
      );
      expect(actual).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        inventoryStoreStub.getInventoryReplenishmentTaskTypeData,
        startDate,
        endDate,
      );
    });

    it('should throw an error if the store method fails', async () => {
      inventoryStoreStub.getInventoryReplenishmentTaskTypeData.rejects(
        new Error('Database error'),
      );

      await expect(
        inventoryService.getReplenishmentTaskTypeSeriesData(startDate, endDate),
      ).to.be.rejectedWith('Database error');

      sinon.assert.calledOnceWithExactly(
        inventoryStoreStub.getInventoryReplenishmentTaskTypeData,
        startDate,
        endDate,
      );
    });
  });
  describe('InventoryUploadRecentActivity', () => {
    const mockQueryResponse: InventoryUploadRecentActivityQueryResponse[] = [
      {
        created_date_time_local: {
          value: '2025-02-18T13:48:59',
        },
        known_order_count: 2454,
        known_order_line_count: 31490,
      },
      {
        created_date_time_local: {
          value: '2025-02-18T11:33:59',
        },
        known_order_count: 2454,
        known_order_line_count: 31490,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T15:41:59',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T12:23:00',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T07:13:00',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-16T13:41:00',
        },
        known_order_count: 2936,
        known_order_line_count: 33736,
      },
      {
        created_date_time_local: {
          value: '2025-02-16T12:34:59',
        },
        known_order_count: 2936,
        known_order_line_count: 33736,
      },
      {
        created_date_time_local: {
          value: '2025-02-15T14:42:00',
        },
        known_order_count: 1730,
        known_order_line_count: 16854,
      },
      {
        created_date_time_local: {
          value: '2025-02-15T12:12:59',
        },
        known_order_count: 1730,
        known_order_line_count: 16854,
      },
      {
        created_date_time_local: {
          value: '2025-02-14T11:27:00',
        },
        known_order_count: 2197,
        known_order_line_count: 23452,
      },
    ];

    const mockServiceResponse: InventoryUploadRecentActivity[] = [
      {
        date: '2025-02-18T13:48:59',
        knownOrderCount: 2454,
        knownOrderLineCount: 31490,
      },
      {
        date: '2025-02-18T11:33:59',
        knownOrderCount: 2454,
        knownOrderLineCount: 31490,
      },
      {
        date: '2025-02-17T15:41:59',
        knownOrderCount: 2268,
        knownOrderLineCount: 30987,
      },
      {
        date: '2025-02-17T12:23:00',
        knownOrderCount: 2268,
        knownOrderLineCount: 30987,
      },
      {
        date: '2025-02-17T07:13:00',
        knownOrderCount: 2268,
        knownOrderLineCount: 30987,
      },
      {
        date: '2025-02-16T13:41:00',
        knownOrderCount: 2936,
        knownOrderLineCount: 33736,
      },
      {
        date: '2025-02-16T12:34:59',
        knownOrderCount: 2936,
        knownOrderLineCount: 33736,
      },
      {
        date: '2025-02-15T14:42:00',
        knownOrderCount: 1730,
        knownOrderLineCount: 16854,
      },
      {
        date: '2025-02-15T12:12:59',
        knownOrderCount: 1730,
        knownOrderLineCount: 16854,
      },
      {
        date: '2025-02-14T11:27:00',
        knownOrderCount: 2197,
        knownOrderLineCount: 23452,
      },
    ];

    beforeEach(() => {
      inventoryStoreStub.getInventoryUploadRecentActivity.resolves(
        mockQueryResponse,
      );
    });

    it('should get upload activity in the past 7 days', async () => {
      const actual = await inventoryService.getInventoryUploadRecentActivity();
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('getInventoryBinLocations', () => {
    const aisle = '01';
    const level = '01';

    const mockQueryResponse: InventoryBinLocationQueryResponse[] = [
      {
        binLocation: 'MS000000000',
        locationSide: 'Left',
        skuCode: 'Test sku',
        quantity: 5,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS000000000',
        locationSide: 'Left',
        skuCode: 'Test sku 2',
        quantity: 10,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS111111111',
        locationSide: 'Left',
        skuCode: 'Test sku',
        quantity: 5,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS222222222',
        locationSide: 'Left',
        skuCode: 'Test sku 2',
        quantity: 0,
        containerType: 'Inventory Tote',
      },
    ];

    const mockServiceResponse: InventoryBinLocation[] = [
      {
        binLocation: 'MS000000000',
        locationSide: 'Left',
        containerType: 'Inventory Tote',
        status: 'Occupied',
        skus: [
          {sku: 'Test sku', quantity: 5},
          {sku: 'Test sku 2', quantity: 10},
        ],
      },
      {
        binLocation: 'MS111111111',
        locationSide: 'Left',
        containerType: 'Inventory Tote',
        status: 'Occupied',
        skus: [{sku: 'Test sku', quantity: 5}],
      },
      {
        binLocation: 'MS222222222',
        locationSide: 'Left',
        containerType: 'Inventory Tote',
        status: 'Empty',
        skus: [],
      },
    ];

    beforeEach(() => {
      inventoryStoreStub.getInventoryBinLocations.resolves(mockQueryResponse);
    });

    it('should correctly group bin location data by binLocation', async () => {
      const actual = await inventoryService.getInventoryBinLocations(
        aisle,
        level,
      );
      expect(actual).to.deep.equal(mockServiceResponse);
    });
  });

  describe('handleInventoryUploadKnownDemand', () => {
    let extractDateFromManagerStub: sinon.SinonStub;
    let sendToPubSubStub: sinon.SinonStub;
    /* eslint-disable @typescript-eslint/no-explicit-any */
    let parseXlsToJsonSpy: sinon.SinonSpy<[Buffer], any[]>;

    beforeEach(() => {
      extractDateFromManagerStub = sinon.stub(
        DataFlowManager,
        'extractDateFromManager',
      );
      sendToPubSubStub = sinon.stub(DataFlowManager, 'sendToPubSub');
      parseXlsToJsonSpy = sinon.spy(DataFlowManager, 'parseXlsToJson');
      process.env.TZ = 'UTC';
    });

    afterEach(() => {
      extractDateFromManagerStub.restore();
      sendToPubSubStub.restore();
      parseXlsToJsonSpy.restore();
    });

    // Sample data based on actual files provided as examples
    const managerData: Data = [
      ['12/17/2024', 'Dynamic List Display', '1'],
      [],
      [],
      ['Date & Time :', '12/17/24 12:37'],
      ['Orders Selected:', '2,453'],
      [],
      [
        'Ship Point',
        'Deliv.Date',
        'Ship-to',
        'Sold-to',
        'Name (Sold-to)',
        'Carrier',
        'Altr. Nb.',
        'Code',
        'Alloc.',
        'Lns',
        'ALC %',
        'Order Numb',
        'Order Type',
        'Ship date',
        'Del. Pri.',
        'Nb. Del.',
        'Sched. Str',
        'E Alloc Dt',
        'Season(s)',
        'Delvry Num',
        'Date Qlf.',
        'Description',
        'Division',
        'Expedite',
      ],
      [
        '03',
        '1/6/25',
        '50225396',
        '11027661',
        'LCC Lifecare Dssi Faxed Order Only',
        'UP7P',
        '0',
        '',
        '1',
        '1',
        '25',
        '33882552',
        'OR',
        '5/13/24',
        '3',
        '1',
        'Z10',
        '12/9/24',
        '',
        '91',
        'P/Ship 50/100/12',
        '001',
        '',
      ],
      [
        '03',
        '1/17/25',
        '50160216',
        '11004543',
        'Davita Inc',
        'UP7P',
        '1',
        'LG',
        '1',
        '1',
        '33.33',
        '34026430',
        'OR',
        '6/28/24',
        '3',
        '1',
        'Z00',
        '12/9/24',
        '',
        '91',
        'P/Ship 50/80/6',
        '001',
      ],
    ];

    const details: Data = [
      ['12/17/24', 'details'],
      [],
      ['Allocation Inquiry'],
      [],
      [
        'Requirement',
        'RqTy',
        'SL #',
        'Sea',
        'Stock type',
        'Batch #',
        'Status',
        'Material',
        'Size',
        'Quantity',
        'BUn',
        'Item',
        'Req.dlv.dt',
        'Route',
        'DlBl',
        'Mat.Av.Dt.',
        'Purchase order no.',
        'Sold-to pt',
        'CGrp',
        'Sales Division',
      ],
      [
        '0034552278',
        '05',
        '0000',
        '',
        'C',
        '0000878735',
        'F',
        'UPS2019WH',
        '14/',
        '1',
        '',
        '000020',
        '12/20/24',
        'UP73',
        '',
        '12/20/24',
        'UPS Emp Allowance Tr',
        '11056656',
        '',
        '',
      ],
      [
        '*0034552278',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
      ],
      [
        '0034551922',
        '05',
        '0000',
        '',
        'C',
        '0000227529',
        'F',
        '73411',
        'NS',
        '10',
        '',
        '000080',
        '12/19/24',
        'HLPC',
        '06',
        '12/19/24',
        '7318186W',
        '11000533',
        '',
        '',
      ],
      [
        '*0034551922',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
      ],
    ];

    // Create XLS files
    const managerXLS = createXLS(managerData);
    const detailsXLS = createXLS(details);

    // Simulate Multer files
    const managerFile = simulateMulterFile(managerXLS, 'manager.xls');
    const detailsFile = simulateMulterFile(detailsXLS, 'details.xls');

    const recTime = Date.now(); // Unix timestamp in milliseconds
    const expected = {
      ordersProcessed: 2,
      orderLinesProcessed: 4,
    };

    const expectedManagerJson = [
      {
        'ship point': '3',
        'deliv.date': '2025-01-06T00:00:00.000Z',
        'ship-to': '50225396',
        'sold-to': '11027661',
        'name (sold-to)': 'LCC Lifecare Dssi Faxed Order Only',
        carrier: 'UP7P',
        'altr. nb.': '0',
        code: '',
        'alloc.': '1',
        lns: '1',
        'alc %': '25',
        'order numb': '33882552',
        'order type': 'OR',
        'ship date': '2024-05-13T00:00:00.000Z',
        'del. pri.': '3',
        'nb. del.': '1',
        'sched. str': 'Z10',
        'e alloc dt': '2024-12-09T00:00:00.000Z',
        'season(s)': '',
        'delvry num': '91',
        'date qlf.': 'P/Ship 50/100/12',
        description: '1',
        division: '',
        expedite: undefined,
      },
      {
        'ship point': '3',
        'deliv.date': '2025-01-17T00:00:00.000Z',
        'ship-to': '50160216',
        'sold-to': '11004543',
        'name (sold-to)': 'Davita Inc',
        carrier: 'UP7P',
        'altr. nb.': '1',
        code: 'LG',
        'alloc.': '1',
        lns: '1',
        'alc %': '33.33',
        'order numb': '34026430',
        'order type': 'OR',
        'ship date': '2024-06-28T00:00:00.000Z',
        'del. pri.': '3',
        'nb. del.': '1',
        'sched. str': 'Z00',
        'e alloc dt': '2024-12-09T00:00:00.000Z',
        'season(s)': '',
        'delvry num': '91',
        'date qlf.': 'P/Ship 50/80/6',
        description: '1',
        division: undefined,
        expedite: undefined,
      },
    ];

    const expectedDetailsJson = [
      {
        requirement: '34552278',
        rqty: 5,
        'sl #': '',
        sea: '',
        'stock type': 'C',
        'batch #': '878735',
        status: 'F',
        material: 'UPS2019WH',
        size: '14/',
        quantity: 1,
        bun: '',
        item: '20',
        'req.dlv.dt': '2024-12-20T00:00:00.000Z',
        route: 'UP73',
        dlbl: '',
        'mat.av.dt.': '2024-12-20T00:00:00.000Z',
        'purchase order no.': 'UPS Emp Allowance Tr',
        'sold-to pt': '11056656',
        cgrp: '',
        'sales division': '',
      },
      {
        requirement: '*0034552278',
        rqty: 0,
        'sl #': '',
        sea: '',
        'stock type': '',
        'batch #': '',
        status: '',
        material: '',
        size: '',
        quantity: 0,
        bun: '',
        item: '',
        'req.dlv.dt': '',
        route: '',
        dlbl: '',
        'mat.av.dt.': '',
        'purchase order no.': '',
        'sold-to pt': '',
        cgrp: '',
        'sales division': '',
      },
      {
        requirement: '34551922',
        rqty: 5,
        'sl #': '',
        sea: '',
        'stock type': 'C',
        'batch #': '227529',
        status: 'F',
        material: '73411',
        size: 'NS',
        quantity: 10,
        bun: '',
        item: '80',
        'req.dlv.dt': '2024-12-19T00:00:00.000Z',
        route: 'HLPC',
        dlbl: '6',
        'mat.av.dt.': '2024-12-19T00:00:00.000Z',
        'purchase order no.': '7318186W',
        'sold-to pt': '11000533',
        cgrp: '',
        'sales division': '',
      },
      {
        requirement: '*0034551922',
        rqty: 0,
        'sl #': '',
        sea: '',
        'stock type': '',
        'batch #': '',
        status: '',
        material: '',
        size: '',
        quantity: 0,
        bun: '',
        item: '',
        'req.dlv.dt': '',
        route: '',
        dlbl: '',
        'mat.av.dt.': '',
        'purchase order no.': '',
        'sold-to pt': '',
        cgrp: '',
        'sales division': '',
      },
    ];

    it('should get parse the file and return the count of orders and order lines processed', async () => {
      const today = new Date().toISOString();
      extractDateFromManagerStub.returns(today); // Stub the method
      sendToPubSubStub.resolves(); // Stub the method

      const actual = await inventoryService.handleInventoryUploadKnownDemand(
        managerFile,
        detailsFile,
        recTime,
      );
      expect(actual).to.deep.equal(expected);
      expect(parseXlsToJsonSpy.calledTwice).to.be.true; // Ensure the spy was called twice

      const managerJsonCall = parseXlsToJsonSpy.getCall(0).returnValue;
      const detailsJsonCall = parseXlsToJsonSpy.getCall(1).returnValue;

      expect(managerJsonCall).to.deep.equal(expectedManagerJson);
      expect(detailsJsonCall).to.deep.equal(expectedDetailsJson);
    });
  });
});

type Data = (string | number)[][];

function createXLS(data: Data): Buffer {
  const ws = XLSX.utils.aoa_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  return XLSX.write(wb, {type: 'buffer', bookType: 'xls'});
}

function simulateMulterFile(
  buffer: Buffer,
  fileName: string,
): Express.Multer.File {
  const file: Express.Multer.File = {
    fieldname: 'file',
    originalname: fileName,
    encoding: '7bit',
    mimetype: 'application/vnd.ms-excel',
    size: buffer.length,
    stream: bufferToStream(buffer),
    destination: '',
    filename: fileName,
    path: '',
    buffer,
  };
  return file;
}

function bufferToStream(buffer: Buffer): Readable {
  const stream = new Readable();
  stream.push(buffer);
  stream.push(null);
  return stream;
}
