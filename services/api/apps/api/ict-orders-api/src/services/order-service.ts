import {
  ChartSeriesData,
  DiService,
  WMSCustomerOrderDetailStatus,
  WMSCustomerOrderStatus,
} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import {EstimatedOrderCompletionTimes} from '../defs/estimated-completion-def.ts';
import {CycletimeFacilityArea} from '../defs/order-areas-cycle-time-def.ts';
import {ThroughputFacilityArea} from '../defs/order-areas-throughput-def.ts';
import {OrderCycleTimeChartData} from '../defs/order-cycle-time-chart-def.ts';
import {
  OrderLineProgressChartQueryData,
  OrderLineProgressSeriesData,
} from '../defs/order-line-progress-chart-def.ts';
import {OrderPerformanceFulfillmentData} from '../defs/order-performance-fulfillment-def.ts';
import {UnitsRemainingData} from '../defs/order-remaining-def.ts';
import {OrderThroughputData} from '../defs/order-throughput-def.ts';
import {OrdersOutstandingData} from '../defs/orders-outstanding-def.ts';
import {ProjectedOrderFulfillment} from '../defs/orders-projected-fulfillment-def.ts';
import {OrderStore} from '../stores/order-store.ts';
import {
  OrderProgressSeriesData,
  OrderCustomerLineProgressSeriesData,
} from '../defs/order-progress-chart-def.ts';
import {CustomerOrderThroughputData} from '../defs/customer-order-throughput-def.ts';
import {CustomerOrderEstimatedCompletionData} from '../defs/order-estimated-completion-def.ts';
import {CustomerOrderLineThroughputSeriesData} from '../defs/order-customer-line-throughput-series-def.ts';
import {FacilityOrderLineThroughputSeriesResponse} from '../defs/facility-order-line-throughput-series-def.ts';
import {OrderLineThroughputData} from '../defs/order-line-throughput-def.ts';
import {PickOrderArea} from '../defs/pick-order-def.ts';
import {OrderCustomerLineThroughputData} from '../defs/order-customer-line-throughput-def.ts';
import {OrdersPickCycleCountData} from '../defs/orders-pick-cycle-count-def.ts';

@DiService()
export class OrderService {
  constructor(private orderStore: OrderStore) {}

  async getOrderLineProgress(startDate: string, endDate: string) {
    const lineProgress = await this.orderStore.getOrderLineProgress(
      startDate,
      endDate,
    );
    return lineProgress;
  }

  async getCustomerOrderLineProgress(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderDetailStatus,
  ) {
    const lineProgress = await this.orderStore.getCustomerOrderLineProgress(
      startDate,
      endDate,
      status,
    );
    return lineProgress;
  }

  async getFacilityOrderLineProgress(startDate: string, endDate: string) {
    const lineProgress = await this.orderStore.getFacilityOrderLineProgress(
      startDate,
      endDate,
    );
    return lineProgress;
  }

  async getOrderProgress(startDate: string, endDate: string) {
    return this.orderStore.getOrderProgress(startDate, endDate);
  }

  async getOrdersFacilityProgress(startDate: string, endDate: string) {
    return this.orderStore.getOrdersFacilityProgress(startDate, endDate);
  }

  async getPickOrdersShipped(startDate: string, endDate: string) {
    return await this.orderStore.getPickOrdersShipped(startDate, endDate);
  }

  async getOrdersFacilityShipped(startDate: string, endDate: string) {
    return await this.orderStore.getOrdersFacilityShipped(startDate, endDate);
  }

  async getOrdersShipped(startDate: string, endDate: string) {
    return await this.orderStore.getOrdersShipped(startDate, endDate);
  }

  async getCustomerOrderProgress(startDate: string, endDate: string) {
    return this.orderStore.getCustomerOrderProgress(startDate, endDate);
  }

  async getOrderProgressSeries(
    startDate: Date,
    endDate: Date,
  ): Promise<OrderProgressSeriesData> {
    const orderProgressSeriesQueryData =
      await this.orderStore.getOrderProgressSeries(startDate, endDate);
    return {
      progress: orderProgressSeriesQueryData.map(datum => ({
        name: datum.hour,
        value:
          datum.totalOrders > 0
            ? (datum.runningCompletedTotal / datum.totalOrders) * 100
            : 0,
      })),
    };
  }

  async getCustomerOrderProgressSeries(
    startDate: Date,
    endDate: Date,
    status?: WMSCustomerOrderStatus,
  ): Promise<OrderProgressSeriesData> {
    const orderProgressSeriesQueryData =
      await this.orderStore.getCustomerOrderProgressSeries(
        startDate,
        endDate,
        status,
      );
    return {
      progress: orderProgressSeriesQueryData.map(datum => ({
        name: datum.hour,
        value:
          datum.totalOrders > 0
            ? (datum.runningCompletedTotal / datum.totalOrders) * 100
            : 0,
      })),
    };
  }

  async getOrderCycleTime(startDate: string, endDate: string) {
    return await this.orderStore.getOrderCycleTime(startDate, endDate);
  }

  async getCustomerOrderLineThroughputSeries(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderLineThroughputSeriesData> {
    const throughputSeries =
      await this.orderStore.getCustomerOrderLineThroughputSeries(
        startDate,
        endDate,
      );
    return {
      throughput: throughputSeries.map(datum => ({
        name: datum.event_interval.value,
        value: datum.completed_order_lines_hourly_rate,
      })),
    };
  }

  async getCustomerOrderCycleTime(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderStatus,
  ) {
    return await this.orderStore.getCustomerOrderCycleTime(
      startDate,
      endDate,
      status,
    );
  }

  async getFacilityOrderCycleTime(startDate: string, endDate: string) {
    return await this.orderStore.getFacilityOrderCycleTime(startDate, endDate);
  }

  async getCustomerOrdersEstimatedCompletion(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderEstimatedCompletionData> {
    return {
      estimatedCompletionMinutes:
        await this.orderStore.getCustomerOrderEstimatedCompletion(
          startDate,
          endDate,
        ),
    };
  }

  async getFacilityOrdersEstimatedCompletion(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderEstimatedCompletionData> {
    return {
      estimatedCompletionMinutes:
        await this.orderStore.getFacilityOrdersEstimatedCompletion(
          startDate,
          endDate,
        ),
    };
  }

  async getOrdersEstimatedCompletion(
    startDate: string,
    endDate: string,
  ): Promise<EstimatedOrderCompletionTimes> {
    const estimatedCompletionQueryResult =
      await this.orderStore.getOrdersEstimatedCompletion(startDate, endDate);

    const now = new Date();
    const completionTime = new Date(
      now.getTime() +
        estimatedCompletionQueryResult.estimatedHoursRemaining * 3600 * 1000,
    );

    return {
      completionTime: completionTime.toISOString(),
    };
  }

  async getAreasCycleTime(
    startDate: string,
    endDate: string,
  ): Promise<CycletimeFacilityArea[]> {
    return await this.orderStore.getAreasCycleTime(startDate, endDate);
  }

  async getOrderCycleTimeChartData(
    startDate: string,
    endDate: string,
  ): Promise<OrderCycleTimeChartData> {
    const orderCycleTimeQueryData =
      await this.orderStore.getOrderCycleTimeChartData(startDate, endDate);
    return {
      orderCycleTimeChart: orderCycleTimeQueryData.map(datum => ({
        name: datum.event_hour.value,
        value: datum.average_cycletime_seconds,
      })),
    };
  }

  async getOrderLineProgressAreas(startDate: string, endDate: string) {
    return await this.orderStore.getOrderLineProgressAreas(startDate, endDate);
  }

  /**
   * Attempt to query the database for order throughput rate.
   * @returns Data Contract that contains the OrderThroughputLinesData type.
   */
  async getOrderThroughputRateAsync(
    startDate: string,
    endDate: string,
    area?: PickOrderArea,
  ): Promise<OrderThroughputData | undefined> {
    return this.orderStore.getOrderThroughputRate(startDate, endDate, area);
  }

  /**
   * Attempt to query the database for order throughput rate.
   * @returns Data Contract that contains the OrderThroughputLinesData type.
   */
  async getOrderLineThroughput(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineThroughputData | undefined> {
    return this.orderStore.getOrderLineThroughput(startDate, endDate);
  }

  /**
   * Calls the order store to query the customer order table for customer order throughput rate.
   * @returns Data Contract that contains the OrderThroughputData type.
   */
  async getCustomerOrderThroughputRate(
    startDate: Date,
    endDate: Date,
    status?: WMSCustomerOrderStatus,
  ): Promise<CustomerOrderThroughputData> {
    // If the passed in end date is in the future, use the current datetime. We do this because the orders per hour calculation is
    // done based on the number of hours between startdate and enddate, and no orders can be completed in the future
    const endDateCalc = new Date(
      Math.min(new Date().getTime(), endDate.getTime()),
    );

    return this.orderStore.getCustomerOrderThroughputRate(
      startDate.toISOString(),
      endDateCalc.toISOString(),
      status,
    );
  }

  async getFacilityOrderThroughputRate(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderThroughputData> {
    // If the passed in end date is in the future, use the current datetime. We do this because the orders per hour calculation is
    // done based on the number of hours between startdate and enddate, and no orders can be completed in the future
    const endDateCalc = new Date(
      Math.min(new Date().getTime(), endDate.getTime()),
    );

    return this.orderStore.getFacilityOrderThroughputRate(
      startDate.toISOString(),
      endDateCalc.toISOString(),
    );
  }

  /**
   * Attempt to query the database for order throughput chart data.
   * @returns a {@link ChartSeriesData}[] of order throughput
   */
  async getOrderCustomerThroughputChartData(
    startDate: string,
    endDate: string,
  ): Promise<ChartSeriesData[]> {
    const CustomerOrderThroughputChartQueryData =
      await this.orderStore.getOrderCustomerThroughputChartData(
        startDate,
        endDate,
      );
    return CustomerOrderThroughputChartQueryData.map(datum => ({
      name: datum.event_interval.value,
      value: datum.completed_orders,
    }));
  }

  /**
   * Attempt to query the database for order throughput chart data.
   * @returns a {@link ChartSeriesData}[] of order throughput
   */
  async getOrderThroughputChartData(
    startDate: string,
    endDate: string,
  ): Promise<ChartSeriesData[]> {
    const orderThroughputChartQueryData =
      await this.orderStore.getOrderThroughputChartData(startDate, endDate);
    return orderThroughputChartQueryData.map(datum => ({
      name: datum.event_hour.value,
      value: datum.completed_orders,
    }));
  }

  /**
   * Attempt to query the database for order line throughput series data.
   * @returns a {@link ChartSeriesData}[] of order line throughput
   */
  async getOrderPickLineThroughputSeries(
    startDate: string,
    endDate: string,
  ): Promise<ChartSeriesData[]> {
    const orderThroughputChartQueryData =
      await this.orderStore.getOrderPickLineThroughputSeries(
        startDate,
        endDate,
      );
    return orderThroughputChartQueryData.map(datum => ({
      name: datum.event_interval.value,
      value: datum.hourly_throughput_rate,
    }));
  }

  /**
   * Attempt to query the database for facility order line throughput series data.
   * @returns a {@link ChartSeriesData}[] of facility order line throughput
   */
  async getFacilityOrderLineThroughputSeries(
    startDate: Date,
    endDate: Date,
  ): Promise<FacilityOrderLineThroughputSeriesResponse> {
    const facilityOrderThroughputChartQueryData =
      await this.orderStore.getFacilityOrderLineThroughputSeries(
        startDate.toISOString(),
        endDate.toISOString(),
      );
    return {
      throughput: facilityOrderThroughputChartQueryData.map(datum => ({
        name: datum.event_interval.value,
        value: datum.hourly_throughput_rate,
      })),
    };
  }

  async getAreasThroughput(
    startDate: string,
    endDate: string,
    area?: string,
  ): Promise<ThroughputFacilityArea[]> {
    return this.orderStore.getAreasThroughput(startDate, endDate, area);
  }

  /**
   * Attempt to query the database for order line progress chart data.
   * @returns Data Contract that contains the OrderLineProgressChartData type.
   */
  async getOrderLineProgressChartData(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineProgressSeriesData> {
    const orderLineProgressChartQueryData =
      await this.orderStore.getOrderLineProgressChartData(startDate, endDate);
    return {
      progress: orderLineProgressChartQueryData.map(datum => ({
        name: datum.hour,
        value: datum.runningTotal,
      })),
      trend: this.getOrderLineTrendData(
        orderLineProgressChartQueryData,
        endDate,
      ),
    };
  }

  async getCustomerOrderLineProgressSeries(
    startDate: Date,
    endDate: Date,
    status?: WMSCustomerOrderDetailStatus,
  ): Promise<OrderCustomerLineProgressSeriesData> {
    const orderLineProgressSeriesQueryData =
      await this.orderStore.getCustomerOrderLineProgressSeries(
        startDate,
        endDate,
        status,
      );
    return {
      progress: orderLineProgressSeriesQueryData.map(datum => ({
        name: datum.hour,
        value:
          datum.totalOrders > 0
            ? (datum.runningCompletedTotal / datum.totalOrders) * 100
            : 0,
      })),
    };
  }

  /**
   * Gets trend data for missing data in requested time span.
   * Uses known line progress data to generate a trend for missing event data.
   * @param knownLineProgress Known line progress event data
   * @param endDate last requested date for line progress data
   * @returns Missing event data in the form of a trend
   */
  private getOrderLineTrendData(
    knownLineProgress: OrderLineProgressChartQueryData[],
    endDate: string,
  ): ChartSeriesData[] {
    let trend: ChartSeriesData[] = [];
    const firstEvent = knownLineProgress.at(0);
    const lastEvent = knownLineProgress.at(-1);
    // Data from the date range does not include data up to end date
    if (firstEvent && lastEvent && lastEvent.hour !== endDate) {
      const firstEventHour = DateTime.fromISO(firstEvent.hour).toUTC();
      const lastEventHour = DateTime.fromISO(lastEvent.hour).toUTC();

      // Calculate Average line progress
      // Total line progress for the available data
      const totalLineProgress = lastEvent.runningTotal;
      // How many hours of available data there is
      const timespanHours =
        lastEventHour.diff(firstEventHour, 'hours').hours + 1;
      // Average line progress of the data
      const averageLineProgress = totalLineProgress / timespanHours;

      // Generate trend data
      // How many hours the available data is unable to provide for
      const missingHours =
        DateTime.fromISO(endDate).diff(lastEventHour, 'hours').hours + 1;
      const lastEventValue = lastEvent.runningTotal;

      if (lastEventHour.isValid) {
        // Create trend chart series data
        trend = [...Array(Math.round(missingHours)).keys()].map((_, i) => ({
          name: lastEventHour.plus({hours: i}).toISO(),
          value: Math.floor(lastEventValue + averageLineProgress * i),
        }));
      }
    }
    return trend;
  }
  async getProjectedOrderFulfillmentPercentage(
    startDate: string,
    endDate: string,
  ): Promise<ProjectedOrderFulfillment> {
    return this.orderStore.getProjectedOrderFulfillment(startDate, endDate);
  }

  async getOrdersFulfillmentOutstanding(
    startDate: string,
    endDate: string,
  ): Promise<OrdersOutstandingData> {
    return this.orderStore.getOrderFulfillmentOutstanding(startDate, endDate);
  }

  async getOrdersFacilityOutstanding(
    startDate: string,
    endDate: string,
  ): Promise<OrdersOutstandingData> {
    return this.orderStore.getOrderFacilityOutstanding(startDate, endDate);
  }

  async getOrderPerformanceFulfillment(
    startDate: string,
    endDate: string,
    departmentFilter: string,
  ): Promise<OrderPerformanceFulfillmentData> {
    return await this.orderStore.getOrderPerformanceFulfillment(
      startDate,
      endDate,
      departmentFilter,
    );
  }

  async getUnitsRemaining(
    startDate: string,
    endDate: string,
  ): Promise<UnitsRemainingData> {
    return this.orderStore.getUnitsRemaining(startDate, endDate);
  }

  async getOrderCustomerLineThroughput(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderDetailStatus,
  ): Promise<OrderCustomerLineThroughputData> {
    return this.orderStore.getOrderCustomerLineThroughput(
      startDate,
      endDate,
      status,
    );
  }

  async getOrdersPickCycleCount(
    startDate: Date,
    endDate: Date,
  ): Promise<OrdersPickCycleCountData> {
    return this.orderStore.getOrdersPickCycleCount(startDate, endDate);
  }
}
