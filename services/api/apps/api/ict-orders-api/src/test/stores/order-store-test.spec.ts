import {
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  HealthStatus,
  IctError,
  WinstonLogger,
  WMSCustomerOrderDetailStatus,
} from 'ict-api-foundations';
import chaiAsPromised from 'chai-as-promised';
import {expect, use} from 'chai';

import sinon, {SinonStub} from 'sinon';
import {BigQuery, BigQueryTimestamp} from '@google-cloud/bigquery';
import {AppConfigSetting} from '@ict/sdk-foundations/types/index.ts';
import {OrderStore} from '../../stores/order-store.ts';
import {
  CustomerOrderProgress,
  OrderProgress,
} from '../../defs/order-progress-def.ts';
import {OrderShipped} from '../../defs/order-lines-shipped-def.ts';
import {OrderCycleTime} from '../../defs/order-cycle-time-def.ts';
import {OrderCycleTimeChartQueryResponse} from '../../defs/order-cycle-time-chart-def.ts';
import {LineProgressFacilityArea} from '../../defs/order-areas-lineprogress-def.ts';
import {CycletimeFacilityArea} from '../../defs/order-areas-cycle-time-def.ts';
import {OrderLineProgressChartQueryData} from '../../defs/order-line-progress-chart-def.ts';
import {UnitsRemainingQueryResponse} from '../../defs/order-remaining-def.ts';
import {OrderPerformanceFulfillmentQueryResponse} from '../../defs/order-performance-fulfillment-def.ts';
import {OrdersShipped} from '../../defs/order-shipped-def.ts';
import {OrderProgressSeriesQueryData} from '../../defs/order-progress-chart-def.ts';
import {CustomerOrderLineThroughputSeriesQueryResponse} from '../../defs/order-customer-line-throughput-series-def.ts';
import {FacilityOrderLineThroughputSeriesQueryResponse} from '../../defs/facility-order-line-throughput-series-def.ts';
import {OrderLineThroughputData} from '../../defs/order-line-throughput-def.ts';
import {OrderPickLineThroughputSeriesQueryData} from '../../defs/order-line-throughput-series-def.ts';
import {PickOrderArea} from '../../defs/pick-order-def.ts';
import {OrderCustomerThroughputChartQueryData} from '../../defs/order-throughput-chart-def.ts';
import {OrdersPickCycleCountData} from '../../defs/orders-pick-cycle-count-def.ts';
import {ProjectedOrderFulfillment} from '../../defs/orders-projected-fulfillment-def.ts';
import {DefaultConfigSettings} from 'ict-api-schema';

describe('OrderStore', () => {
  use(chaiAsPromised);
  let orderStore: OrderStore;
  let clientStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let configStore: ConfigStore;
  let executeMonitoredJobStub: SinonStub;

  beforeEach(() => {
    clientStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(clientStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );

    // stub the "info" method of mockLogger
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();
    configStore = new ConfigStore(contextService);

    orderStore = new OrderStore(contextService, mockLogger, configStore);
  });

  describe('getOrderProgress', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';

    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get order progress percentage', async () => {
      const mockQueryResponse: OrderProgress = {
        orderProgressPercentage: 60,
      };
      executeMonitoredJobStub.resolves([
        [
          {
            orderProgressPercentage: 60,
          },
        ],
      ]);

      const result = await orderStore.getOrderProgress(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderProgress(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrdersFacilityProgress', () => {
    const startDate = '2025-06-10T00:00:00.000Z';
    const endDate = '2025-06-11T00:00:00.000Z';

    it('should get order progress percentage', async () => {
      const mockQueryResponse: CustomerOrderProgress = {
        orderProgressPercentage: 98.1873,
      };
      executeMonitoredJobStub.resolves([
        [
          {
            orderProgressPercentage: 98.1873,
          },
        ],
      ]);

      const result = await orderStore.getOrdersFacilityProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrdersFacilityProgress(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getCustomerOrderLineThroughputSeries', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
    });

    it('should get order cycle time', async () => {
      const mockQueryResponse: CustomerOrderLineThroughputSeriesQueryResponse[] =
        [
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
            completed_order_lines_hourly_rate: 15,
          },
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
            completed_order_lines_hourly_rate: 15,
          },
        ];
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getCustomerOrderLineThroughputSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getFacilityOrderLineThroughputSeries', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should get facility order line throughput series', async () => {
      const mockQueryResponse: FacilityOrderLineThroughputSeriesQueryResponse[] =
        [
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
            hourly_throughput_rate: 15,
          },
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
            hourly_throughput_rate: 15,
          },
        ];
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getFacilityOrderLineThroughputSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getCustomerOrderProgress', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';

    it('should get order progress percentage', async () => {
      const mockQueryResponse: CustomerOrderProgress = {
        orderProgressPercentage: 60.2345,
      };
      executeMonitoredJobStub.resolves([
        [
          {
            orderProgressPercentage: 60.2345,
          },
        ],
      ]);

      const result = await orderStore.getCustomerOrderProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderProgress(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getEstimatedCompletion', () => {
    let startDate: string;
    let endDate: string;
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      startDate = '2021-11-27T00:23:50';
      endDate = '2021-11-28T18:22:53.';

      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get estimated completion time', async () => {
      const mockQueryResponse = [
        {
          completionTime: '2021-11-30T23:59:59',
          status: 'ok',
        },
      ];
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrdersEstimatedCompletion(
        startDate,
        endDate,
      );

      // Map the response to match the expected structure
      const expectedResponse = mockQueryResponse.map(item => ({
        completionTime: item.completionTime,
        status: item.status,
      }))[0];

      expect(result).to.deep.equal(expectedResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([]);
      try {
        await orderStore.getOrdersEstimatedCompletion(startDate, endDate);
      } catch (e) {
        expect(e).to.have.property('statusCode', 204);
      }
    });
  });

  describe('getOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;
    let settingStub: sinon.SinonStub;
    const configSetting: AppConfigSetting = {
      id: 'test',
      name: 'test',
      group: null,
      dataType: 'json',
      value: {},
    };

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';

      // mock data required to be returned by config stubs
      configSetting.value = {
        startStates: ['test'],
        endStates: ['test'],
      };

      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      // Get start and end states from config
      settingStub.onFirstCall().resolves(configSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should get order cycle time', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 203,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrderCycleTime(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should get the tenant config for start and stop states', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 203,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      await orderStore.getOrderCycleTime(startDate, endDate);

      expect(settingStub.called).to.be.true;
    });

    it('should throw an error when the setting value cannot be found', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 203,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
      settingStub.onFirstCall().resolves(undefined);

      const result = await expect(
        orderStore.getOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);
      expect(result.statusCode).to.equal(
        IctError.internalServerError().statusCode,
      );
    });

    it('should ensure there is a default config setting defined', async () => {
      const settingNames: string[] = [];
      DefaultConfigSettings.forEach(setting => {
        settingNames.push(setting.name);
      });
      expect(DefaultConfigSettings).to.be.an('array');
      expect(settingNames).to.contain('cycle-time-state-range');
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });

    it('should be no content if zero', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 0,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await expect(
        orderStore.getOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);
      expect(result.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getCustomerOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should get order cycle time', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 203,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getCustomerOrderCycleTime(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);
      const outcome = await expect(
        orderStore.getCustomerOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });

    it('should be no content if zero', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 0,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await expect(
        orderStore.getCustomerOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);
      expect(result.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFacilityOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2025-07-17T00:00:01';
      endDate = '2025-07-17T23:59:59';
    });

    it('should get order cycle time', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 203,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getFacilityOrderCycleTime(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);
      const outcome = await expect(
        orderStore.getFacilityOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });

    it('should be no content if zero', async () => {
      const mockQueryResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 0,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await expect(
        orderStore.getFacilityOrderCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);
      expect(result.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getCustomerEstimatedCompletion', () => {
    let startDate: Date;
    let endDate: Date;
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should get customer estimated completion', async () => {
      const mockQueryResponse = {estimatedMinutesRemaining: 203};
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getCustomerOrderEstimatedCompletion(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse.estimatedMinutesRemaining);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderEstimatedCompletion(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getPickOrderShipped', () => {
    let startDate: string;
    let endDate: string;
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get order shipped', async () => {
      const mockQueryResponse: OrderShipped = {
        total: 2800,
        current: 1700,
        past: 1750,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getPickOrdersShipped(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getPickOrdersShipped(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getProjectedOrderFulfillment', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get a percentage indicating projected order fulfillment', async () => {
      const mockQueryResponse: ProjectedOrderFulfillment = {
        projectedOrderFulfillmentPercentage: 0.23,
      };

      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getProjectedOrderFulfillment(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getProjectedOrderFulfillment(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrdersShipped', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get orders shipped', async () => {
      const mockQueryResponse: OrdersShipped = {
        total: 2800,
        shipped: 1700,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrdersShipped(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrdersShipped(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrdersFacilityShipped', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2025-06-03T00:00:00.000Z';
      endDate = '2025-06-03T23:59:00.000Z';
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should get facility orders shipped', async () => {
      const mockQueryResponse: OrdersShipped = {
        total: 2800,
        shipped: 1700,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrdersFacilityShipped(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrdersFacilityShipped(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAreasCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should return area cycle time data', async () => {
      const mockQueryResponse = [
        {
          areaId: 'testArea2',
          completionTime: '2021-11-08T10:15:27.000',
          time_difference: -46856,
          activeOperators: 24,
          lowRecOperators: 19,
          highRecOperators: 37,
          maxOperators: 100,
          identifier: 'Workstation 2',
          message: 'Attention Required',
          averageCycleTime: 46976,
          minimumCycleTime: 7648,
          maximumCycleTime: 164889,
          areaName: 'Put Wall',
          status: 'caution',
        },
      ];

      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const expectedResponse: CycletimeFacilityArea[] = [
        {
          id: 'testArea2',
          name: 'Put Wall',
          alertStatus: {
            status: HealthStatus.Caution,
            identifier: 'Workstation 2',
          },
          operators: {
            activeOperators: 24,
            lowRecOperators: 19,
            highRecOperators: 37,
          },
          cycleTime: {
            averageCycleTimeSeconds: 46976,
            fastestCycleTimeSeconds: 7648,
            slowestCycleTimeSeconds: 164889,
            completionTime: '2021-11-08T10:15:27.000',
            slowestRecCycleTimeSeconds: 130000,
            targetCycleTimeSeconds: 2000,
          },
        },
      ];

      const result = await orderStore.getAreasCycleTime(startDate, endDate);

      expect(result).to.deep.equal(expectedResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getAreasCycleTime(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderCycleTimeChartData', () => {
    let mockQueryResponse: OrderCycleTimeChartQueryResponse[];
    let startDate: string;
    let endDate: string;
    // let settingStub: sinon.SinonStub;

    beforeEach(() => {
      mockQueryResponse = [
        {
          event_hour: new BigQueryTimestamp('2023-03-03T00:00:00'),
          average_cycletime_seconds: 400,
        },
        {
          event_hour: new BigQueryTimestamp('2023-03-03T01:00:00'),
          average_cycletime_seconds: 465,
        },
      ];
      startDate = '2023-03-03T00:00:00';
      endDate = '2023-03-03T23:59:59';
    });

    it('should get order cycletime chart data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getOrderCycleTimeChartData(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderCycleTimeChartData(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderThroughputRate', () => {
    const startDate = '2023-07-01T00:00:01';
    const endDate = '2023-07-10T23:59:59';
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    const mockOrdersThroughputRateResponse = [
      {
        throughputRateLinesPerHour: 700,
      },
    ];

    it('should get order throughput rate data', async () => {
      executeMonitoredJobStub.resolves([mockOrdersThroughputRateResponse]);

      const result = await orderStore.getOrderThroughputRate(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrdersThroughputRateResponse[0]);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should get order throughput rate when area is given', async () => {
      const area = PickOrderArea.Picking;
      executeMonitoredJobStub.resolves([mockOrdersThroughputRateResponse]);

      const result = await orderStore.getOrderThroughputRate(
        startDate,
        endDate,
        area,
      );

      expect(result).to.deep.equal(mockOrdersThroughputRateResponse[0]);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should get order throughput rate when area is not given', async () => {
      executeMonitoredJobStub.resolves([mockOrdersThroughputRateResponse]);

      const result = await orderStore.getOrderThroughputRate(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrdersThroughputRateResponse[0]);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderThroughputRate(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getCustomerOrderThroughputRate', () => {
    const startDate = '2023-07-01T00:00:01';
    const endDate = '2023-07-10T23:59:59';

    it('should get customer order throughput rate data', async () => {
      const mockOrdersThroughputRateResponse = [
        {
          throughputRateOrdersPerHour: 100,
        },
      ];

      executeMonitoredJobStub.resolves([mockOrdersThroughputRateResponse]);

      const result = await orderStore.getCustomerOrderThroughputRate(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrdersThroughputRateResponse[0]);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderThroughputRate(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFacilityOrderThroughputRate', () => {
    const startDate = '2023-07-01T00:00:01';
    const endDate = '2023-07-10T23:59:59';

    it('should get customer order throughput rate data', async () => {
      const mockOrdersThroughputRateResponse = [
        {
          throughputRateOrdersPerHour: 100,
        },
      ];

      executeMonitoredJobStub.resolves([mockOrdersThroughputRateResponse]);

      const result = await orderStore.getFacilityOrderThroughputRate(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrdersThroughputRateResponse[0]);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getFacilityOrderThroughputRate(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderLineProgressAreas', () => {
    const startDate = '2023-05-23T01:26:54.813Z';
    const endDate = '2023-05-26T06:35:07.293Z';

    it('should get all areas and their respective order line progress data', async () => {
      const mockQueryResponse = [
        {
          areaId: 'testArea1',
          areaName: 'Picking',
          status: HealthStatus.Ok,
          identifier: 'Workstation 5',
          message: 'Alert message',
          activeOperators: 13,
          lowRecOperators: 19,
          highRecOperators: 37,
          orderLineProgress: 99.5,
          orderLinesCompleted: 426,
          totalOrderLines: 428,
        },
      ];
      const expectedResponse: LineProgressFacilityArea[] = [
        {
          id: 'testArea1',
          name: 'Picking',
          alertStatus: {
            identifier: 'Workstation 5',
            status: HealthStatus.Ok,
          },
          operators: {
            activeOperators: 13,
            lowRecOperators: 19,
            highRecOperators: 37,
          },
          orderLines: {
            orderLineProgress: 99.5,
            orderLinesCompleted: 426,
            totalOrderLines: 428,
          },
        },
      ];

      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrderLineProgressAreas(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(expectedResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderLineProgressAreas(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderLineProgressChartData', () => {
    let startDate: string;
    let endDate: string;

    let mockOrderLineProgressChartResponse: OrderLineProgressChartQueryData[];
    const mockQueryResponse = [
      {
        hour: {value: '2023-05-24T04:00:00.000Z'},
        total_quantityPicked: 4427,
        running_total: 148287,
        total_quantityTarget: 157023,
      },

      {
        hour: {value: '2023-05-24T05:00:00.000Z'},
        total_quantityPicked: 5282,
        running_total: 153569,
        total_quantityTarget: 157023,
      },
    ];

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      mockOrderLineProgressChartResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          quantityPicked: 4427,
          runningTotal: 148287,
          quantityTarget: 157023,
        },
        {
          hour: '2023-05-24T05:00:00.000Z',
          quantityPicked: 5282,
          runningTotal: 153569,
          quantityTarget: 157023,
        },
      ];
    });

    it('should get order line progress chart data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getOrderLineProgressChartData(
        startDate,
        endDate,
      );

      const expected: OrderLineProgressChartQueryData[] =
        mockOrderLineProgressChartResponse.map(datum => ({
          hour: datum.hour,
          quantityPicked: datum.quantityPicked,
          runningTotal: datum.runningTotal,
          quantityTarget: datum.quantityTarget,
        }));
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderLineProgressChartData(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getUnitsRemaining', () => {
    let startDate: string;
    let endDate: string;
    let fakeQueryResponse: UnitsRemainingQueryResponse;
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeQueryResponse = {
        unitsRemaining: 1337,
      };
      executeMonitoredJobStub.resolves([[fakeQueryResponse]]);
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should get units remaining data', async () => {
      const result = await orderStore.getUnitsRemaining(startDate, endDate);

      const expected: UnitsRemainingQueryResponse = fakeQueryResponse;
      expect(result).to.deep.equal(expected);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getUnitsRemaining(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.called).to.be.true;
    });
  });

  describe('getOrderPerformanceFulfillment', () => {
    let startDate: string;
    let endDate: string;
    let departmentFilter: string;
    let mockQueryResponse: OrderPerformanceFulfillmentQueryResponse;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      departmentFilter = 'All';
      mockQueryResponse = {
        orderFulfillment: 24,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    // TODO: Remove skip once query is rewritten
    it.skip('should get units remaining data', async () => {
      const result = await orderStore.getOrderPerformanceFulfillment(
        startDate,
        endDate,
        departmentFilter,
      );

      const expected: OrderPerformanceFulfillmentQueryResponse = {
        orderFulfillment: 24,
      };
      expect(result).to.deep.equal(expected);
    });

    // TODO: Remove skip once query is rewritten
    it.skip('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderPerformanceFulfillment(
          startDate,
          endDate,
          departmentFilter,
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderFulfillmentOutstanding', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    it('should get order outstanding', async () => {
      const mockQueryResponse = {
        incompleted_orders: 423000,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrderFulfillmentOutstanding(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal({
        incompletedTotal: 423000,
      });
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderFulfillmentOutstanding(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderFacilityOutstanding', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    it('should get facility orders outstanding', async () => {
      const mockQueryResponse = {
        incompleted_orders: 42,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrderFacilityOutstanding(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal({
        incompletedTotal: 42,
      });
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderFacilityOutstanding(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderLineThroughput', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    it('should get order line throughput', async () => {
      const mockQueryResponse: OrderLineThroughputData = {
        totalCompletedOrderLines: 1000,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrderLineThroughput(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderLineThroughput(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let settingStub: sinon.SinonStub;
    let mockOrderProgressSeriesResponse: OrderProgressSeriesQueryData[];
    const mockQueryResponse = [
      {
        hour: {value: '2023-05-24T04:00:00.000Z'},
        running_completed_total: 42,
        total_orders: 2107,
      },

      {
        hour: {value: '2023-05-24T05:00:00.000Z'},
        running_completed_total: 84,
        total_orders: 4214,
      },
    ];

    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
      mockOrderProgressSeriesResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should get order progress series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getOrderProgressSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrderProgressSeriesResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderProgressSeries(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getCustomerOrderProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;

    afterEach(() => {
      sinon.restore();
    });

    let mockOrderProgressSeriesResponse: OrderProgressSeriesQueryData[];
    const mockQueryResponse = [
      {
        hour: {value: '2023-05-24T04:00:00.000Z'},
        running_completed_total: 42,
        total_orders: 2107,
      },

      {
        hour: {value: '2023-05-24T05:00:00.000Z'},
        running_completed_total: 84,
        total_orders: 4214,
      },
    ];
    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      mockOrderProgressSeriesResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
    });

    it('should get customer order progress series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getCustomerOrderProgressSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrderProgressSeriesResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderProgressSeries(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });
  describe('getOrderThroughputChartData', () => {
    let startDate: string;
    let endDate: string;

    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
      startDate = '2023-05-24T05:00:00.000Z';
      endDate = '2023-05-25T05:00:00.000Z';
    });

    afterEach(() => {
      settingStub.restore();
    });

    const mockQueryResponse = [
      {
        event_hour: '2023-05-24T05:00:00.000Z',
        completed_orders: 20,
      },
      {
        event_hour: '2023-05-24T05:15:00.000Z',
        completed_orders: 20,
      },
    ];

    it('should get order throughput chart series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getOrderThroughputChartData(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderThroughputChartData(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getCustomerOrderLineProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let mockOrderLineProgressSeriesResponse: OrderProgressSeriesQueryData[];
    const mockQueryResponse = [
      {
        hour: {value: '2023-05-24T04:00:00.000Z'},
        running_completed_total: 42,
        total_orders: 2107,
      },

      {
        hour: {value: '2023-05-24T05:00:00.000Z'},
        running_completed_total: 84,
        total_orders: 4214,
      },
    ];
    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      mockOrderLineProgressSeriesResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
    });

    it('should get customer order line progress series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await orderStore.getCustomerOrderLineProgressSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrderLineProgressSeriesResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderLineProgressSeries(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getCustomerOrderLineProgress', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';
    const mockQueryResponse = [
      [
        {
          lineProgressPercent: 60.2345,
        },
      ],
    ];
    const mockStoreResponse = {
      lineProgressPercent: 60.2345,
    };

    afterEach(() => {
      sinon.restore();
    });

    it('should get customer order line progress percentage when area is given', async () => {
      const status = WMSCustomerOrderDetailStatus.Shipped;
      executeMonitoredJobStub.resolves(mockQueryResponse);

      const result = await orderStore.getCustomerOrderLineProgress(
        startDate,
        endDate,
        status,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should get customer order line progress percentage when area is not given', async () => {
      executeMonitoredJobStub.resolves(mockQueryResponse);

      const result = await orderStore.getCustomerOrderLineProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getCustomerOrderLineProgress(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFacilityOrderLineProgress', () => {
    const startDate = '2025-07-17T00:00:01';
    const endDate = '2025-07-17T23:59:59';
    const mockQueryResponse = [
      [
        {
          lineProgressPercent: 60.2345,
        },
      ],
    ];
    const mockStoreResponse = {
      lineProgressPercent: 60.2345,
    };

    afterEach(() => {
      sinon.restore();
    });

    it('should get facility order line progress percentage', async () => {
      executeMonitoredJobStub.resolves(mockQueryResponse);

      const result = await orderStore.getFacilityOrderLineProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getFacilityOrderLineProgress(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderCustomerLineThroughput', () => {
    let startDate: string;
    let endDate: string;
    const mockQueryResponse = [
      {
        throughputRateOrderLinesPerHour: 1000,
      },
    ];

    beforeEach(() => {
      startDate = '2021-11-25T10:26:54.813Z';
      endDate = '2021-11-25T10:26:54.813Z';
    });

    it('should get order customer Line throughput rate when area is given', async () => {
      const area = WMSCustomerOrderDetailStatus.Picked;
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrderCustomerLineThroughput(
        startDate,
        endDate,
        area,
      );

      expect(result).to.deep.equal(mockQueryResponse[0]);
    });

    it('should get order customer line throughput rate when area is not given', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrderCustomerLineThroughput(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse[0]);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderCustomerLineThroughput(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderPickLineThroughputSeries', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';

    const mockQueryResponse: OrderPickLineThroughputSeriesQueryData[] = [
      {
        event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
        hourly_throughput_rate: 15,
      },
      {
        event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
        hourly_throughput_rate: 15,
      },
    ];

    it('should get order pick line throughput series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrderPickLineThroughputSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        orderStore.getOrderPickLineThroughputSeries(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getOrderCustomerThroughputSeries', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should get customer throughput series chart data', async () => {
      const mockQueryResponse: OrderCustomerThroughputChartQueryData[] = [
        {
          event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
          completed_orders: 15,
        },
        {
          event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
          completed_orders: 15,
        },
      ];
      executeMonitoredJobStub.resolves([mockQueryResponse]);

      const result = await orderStore.getOrderCustomerThroughputChartData(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getOrdersPickCycleCount', () => {
    const startDate = new Date('2021-11-30T00:00:01');
    const endDate = new Date('2021-11-30T23:59:59');

    afterEach(() => {
      sinon.restore();
    });

    it('should get pick order cycle count data', async () => {
      const mockQueryResponse: OrdersPickCycleCountData = {
        cycleCount: 200,
      };

      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      const result = await orderStore.getOrdersPickCycleCount(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getOrderCompletedCodes', async () => {
    let settingStub: sinon.SinonStub;
    it('should always return default value even if setting doesnt exist', async () => {
      // make stub return undefined
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(undefined);

      // invoke method
      const result = await orderStore.getOrderCompletedCodes();

      // expect return value to have length of at least 1
      expect(result.length).to.be.greaterThanOrEqual(1);
      expect(settingStub.calledOnce).to.be.true;
    });
  });
});
