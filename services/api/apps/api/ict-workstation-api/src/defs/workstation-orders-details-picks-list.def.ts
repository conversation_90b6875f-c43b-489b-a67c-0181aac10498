import {
  SortField,
  FilterObjectParameters,
  BaseFilterType,
} from 'ict-api-foundations';

export interface WorkstationOrdersDetailsPicksListQueryParams {
  startDate: Date;
  endDate: Date;
  orderId: string;
  page: number;
  limit: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  searchString?: string;
}

export interface WorkstationOrdersDetailsPicksListParams {
  startDate: Date;
  endDate: Date;
  orderId: string;
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}

export type WorkstationOrdersDetailsPicksListQueryResponse = {
  container: string;
  status: string;
  order_line: string;
  style: string;
  size: string;
  qty_ordered: number;
  qty_picked: number;
  containers: number;
  delivery_number: string;
};

export type WorkstationOrdersDetailsPicksListData = {
  data: {
    deliveryNumber: string;
    tableData: {
      container: string;
      status: string;
      orderLine: string;
      style: string;
      size: string;
      qtyOrdered: number;
      qtyPicked: number;
      containers: number;
    }[];
  };
  metadata: {
    orderId: string;
    page: number;
    limit: number;
    totalResults: number;
  };
};
