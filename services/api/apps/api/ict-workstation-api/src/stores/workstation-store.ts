import {Query} from '@google-cloud/bigquery';
import {
  addStartEndDateParamValues,
  BigQueryGenerator,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
  PaginatedResults,
  SortingSelectQueryParams,
  SQLQueryGenerator,
} from 'ict-api-foundations';
import {WorkstationListResponse} from '../defs/workstation-list.ts';
import {WorkstationDailyPerformanceListQueryData} from '../defs/workstation-daily-performance-list-def.ts';
import {getExcludedWorkstationList} from '../utils/excluded-workstation-helper.ts';
import {
  WorkstationOrdersDetailsPicksListParams,
  WorkstationOrdersDetailsPicksListQueryResponse,
} from '../defs/workstation-orders-details-picks-list.def.ts';
import {
  WorkstationOrdersListParams,
  WorkstationOrdersListQueryResponse,
  WorkstationOrdersStatusQueryResponse,
} from '../defs/workstation-orders-status-def.ts';
import {
  WorkstationContainersListParams,
  WorkstationContainersListQueryResponse,
} from '../defs/workstation-containers-list-def.ts';

@DiService()
export class WorkstationStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  public async getWorkstations(
    startDate: Date,
    endDate: Date,
  ): Promise<{workstation: string}[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const pick_table =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        SELECT DISTINCT workstation_code as workstation
        FROM ${pick_table}
        WHERE event_timestamp_utc BETWEEN @startDate and @endDate
          AND workstation_code IS NOT NULL
          AND workstation_code NOT LIKE '%PICK'
        ORDER BY workstation_code
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows || !rows.length) {
      throw IctError.noContent();
    }

    return rows as {workstation: string}[];
  }

  /**
   * Retrieve individual data for the group of workstations.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns workstation list stats as available.
   */
  async getWorkstationList(): Promise<WorkstationListResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const excludedStations = (
      await getExcludedWorkstationList(this.configStore, this.context)
    ).workstations;

    const platinumWorkstationListing = bigQueryDb.getEdpFullTablePath(
      'platinum_workstation_listing',
    );

    const sql = `
        WITH workstationStats AS (
          SELECT *, 
          ROW_NUMBER() OVER (PARTITION BY workstation ORDER BY record_timestamp DESC) AS row_num 
          FROM \`${platinumWorkstationListing}\`
          WHERE record_timestamp > TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY)
            AND workstation NOT IN UNNEST(@excludedStations)
        )
        SELECT 
          workstation, 
          status, 
          work_mode, 
          workflow_status, 
          operator_id,  
          active_time, 
          starved_time, 
          idle_time, 
          blocked_time, 
          --- Agg_workstation_listing processes quarter hour increments of data so rates s/b x4 ---
          picks_per_hour * 4 AS picks_per_hour,
          lines_per_hour * 4 AS lines_per_hour,
          COALESCE(donorTotesPerHour * 4, 0) AS donor_totes_per_hour,
          COALESCE(orderTotesPerHour * 4, 0) AS order_totes_per_hour,
          weighted_picks_per_hour * 4 AS weighted_picks_per_hour,
          weighted_lines_per_hour * 4 AS weighted_lines_per_hour,
          weighted_donor_totes_per_hour * 4 AS weighted_donor_totes_per_hour,
          weighted_order_totes_per_hour * 4 AS weighted_order_totes_per_hour  
        FROM workstationStats 
        WHERE row_num = 1 
        ORDER BY workstation;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        excludedStations,
      },
      types: {
        excludedStations: ['string'],
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows;
  }

  public static readonly type: string = 'workstation-store';

  public getType(): string {
    return WorkstationStore.type;
  }

  async getWorkstationContainersList(
    params: WorkstationContainersListParams,
  ): Promise<PaginatedResults<WorkstationContainersListQueryResponse[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsInventoryTable =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');
    const movementConnectionTable = bigQueryDb.getEdpFullTablePath(
      'gold_movement_connection',
    );

    const templateSql = `
      WITH mostRecent AS (
        SELECT
          *,
          CASE
            WHEN STARTS_WITH(location_id, "MS0") THEN "Forward Pick"
            WHEN STARTS_WITH(location_id, "ML0") THEN "Forward Pick"
            WHEN STARTS_WITH(location_id, "VNA") THEN "Reserve Storage"
            WHEN STARTS_WITH(location_id, "CNV") THEN "Reserve Storage"
            ELSE "In Transit"
          END AS status,
        FROM (
          SELECT
            *,
            ROW_NUMBER() OVER(
              PARTITION BY
                sku_style,
                sku_size,
                container_id
              ORDER BY query_timestamp_utc DESC
            ) AS row_number,
          FROM \`${wmsInventoryTable}\`
          WHERE query_timestamp_utc BETWEEN @startDate AND @endDate
            AND sku_style = @style
            AND sku_size = @size
            AND zone = @zone
        )
        WHERE row_number = 1
      ),
      transportData AS (
        SELECT
          destination_location_code,
          transport_request_code,
        FROM (
          SELECT
            destination_location_code,
            transport_request_code,
            ROW_NUMBER() OVER(
              PARTITION BY destination_location_code
              ORDER BY movement_end_timestamp_utc DESC
            ) AS row_number,
          FROM \`${movementConnectionTable}\`
          WHERE movement_end_timestamp_utc BETWEEN @startDate AND @endDate
        )
        WHERE row_number = 1
      ),
      containerData AS (
        SELECT
          mr.container_id AS container,
          mr.status AS status,
          CASE
            WHEN mr.status = "In Transit" THEN td.transport_request_code
            ELSE "--"
          END AS transport,
          mr.quantity AS quantity,
          mr.location_id AS last_location,
          mr.query_datetime_local AS event_time,
        FROM mostRecent mr
        JOIN transportData td
          ON mr.location_id = td.destination_location_code
      )
    `;
    const allowedColumnNames = [
      'container',
      'status',
      'transport',
      'quantity',
      'last_location',
      'event_time',
    ];

    const searchableColumns = [
      'container',
      'status',
      'transport',
      'last_location',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'containerData',
      allowedColumnNames,
      searchableColumns,
      searchString: params.searchString,
      cteSubquery: templateSql,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate,
      params.endDate,
      params.filterParams,
    );
    params.filterParams.style = params.style;
    params.filterParams.size = params.size;
    params.filterParams.zone = params.zone;

    const bqQueryGenerator = new BigQueryGenerator();
    const results =
      await SQLQueryGenerator.paginatedQuery<WorkstationContainersListQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (results.list.length === 0) {
      throw IctError.noContent();
    }

    return results;
  }

  async getWorkstationDailyPerformanceList(
    workstations: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<WorkstationDailyPerformanceListQueryData[]> {
    const bigQuery = this.dbProvider.bigQuery;
    const dailyPerformanceTable = bigQuery.getFullTablePath(
      'platinum_workstation_daily_performance',
    );

    const sql = `
      SELECT 
        day AS date,
        TRIM(CAST(SUM(total_logged_in_minutes) / 60 AS STRING FORMAT '9999.9')) AS total_logged_in_hours,
        TRIM(CAST(SUM(idle_time_in_minutes) / SUM(total_logged_in_minutes) * 100 AS STRING FORMAT '990.9')) || '%' AS idle_percent,
        TRIM(CAST(SUM(starved_time_in_minutes) / SUM(total_logged_in_minutes) * 100 AS STRING FORMAT '990.9')) || '%' AS starved_percent,
        TRIM(CAST(SUM(starved_time_in_minutes) / 60 AS STRING FORMAT '990.9')) AS starved_hours,
        SUM(donor_containers) AS donor_containers,
        SUM(gtp_containers) AS gtp_containers,
        SUM(lines) AS lines_picked,
        PARSE_NUMERIC(CAST((SUM(pick_line_qty)/SUM(lines)) AS STRING FORMAT '999999.9')) AS qty_per_line,
        SUM(pick_line_qty) AS pick_line_qty,
        TRIM(cast(sum(lines)/(sum(total_logged_in_minutes)/60) AS STRING FORMAT '999999.9')) as lines_per_hour,
        TRIM(cast(sum(lines_1st_shift)/sum(hours_1st_shift)/110*100 AS STRING FORMAT '9990.9')) || '%' AS avg_lines_1st_shift,
        TRIM(cast(sum(lines_2nd_shift)/sum(hours_2nd_shift)/110*100 AS STRING FORMAT '9990.9')) || '%' AS avg_lines_2nd_shift,
        MAX(retrieval_from_dms) AS retrieval_from_dms,
        MAX(storage_to_dms) AS storage_to_dms,
        MAX(retrieval_from_asrs) AS retrieval_from_asrs,
        MAX(storage_to_asrs) AS storage_to_asrs
      FROM \`${dailyPerformanceTable}\` p
      WHERE TIMESTAMP(day) BETWEEN @startDate AND @endDate
      ${workstations.length > 0 ? 'AND p.workstation_code IN UNNEST(@workstations)' : ''}
      GROUP BY day
      ORDER BY day DESC;
    `;

    const sqlOptions: Query = {
      query: sql,
      types: {
        // type needed when array is empty
        workstations: ['string'],
      },
      params: {
        workstations,
        startDate,
        endDate,
      },
    };

    const [rows] =
      await this.dbProvider.bigQuery.executeMonitoredJob(sqlOptions);
    if (!rows || rows.length === 0) {
      throw IctError.noContent();
    }

    return rows;
  }

  async getWorkstationOrdersDetailsPicksList(
    params: WorkstationOrdersDetailsPicksListParams,
  ): Promise<
    PaginatedResults<WorkstationOrdersDetailsPicksListQueryResponse[]>
  > {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsCustomerOrderTable = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order',
    );
    const pickTable = bigQueryDb.getEdpFullTablePath('gold_pick');
    const wmsCustomerOrderDetailTable = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order_detail',
    );
    const wmsInventoryTable =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');

    const templateSql = `
      WITH customerOrder AS (
        SELECT * 
        FROM \`${wmsCustomerOrderTable}\`
        WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
          AND order_code = @orderId
        LIMIT 1
      ),
      orderPicks AS (
        SELECT *
        FROM \`${pickTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
          AND facility_order_code = (SELECT external_code FROM customerOrder)
      ),
      orderDetails AS (
        SELECT *
        FROM \`${wmsCustomerOrderDetailTable}\`
        WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
          AND order_key = (SELECT order_code FROM customerOrder)
      ),
      -- For container counts
      mostRecentSkuSnapShots AS (
        SELECT 
          quantity,
          sku,
        FROM (
          SELECT
            *,
            ROW_NUMBER() OVER(
              PARTITION BY sku, sku_style, sku_size
              ORDER BY last_activity_date_utc DESC
            ) AS row_number,
          FROM \`${wmsInventoryTable}\`
          WHERE query_timestamp_utc BETWEEN @startDate AND @endDate
        )
        WHERE row_number = 1
      ),
      dataWithAlmostDupes AS (
        SELECT
          source_handling_unit_code AS container,
          CASE
            -- sts = open and state != allocated
            WHEN d.sts = "0" AND d.state != "2" THEN "Open"
            -- sts = open and state = allocated
            WHEN d.sts = "0" AND d.state = "2" THEN "Allocated"
            -- sts = picked
            WHEN d.sts = "5" THEN "Picked"
            -- sts = shipped
            WHEN d.sts = "9" THEN "Shipped"
            ELSE "Other"
          END AS status,
          d.order_line AS order_line,
          d.sku AS style,
          d.storer_id AS size,
          d.qty_ordered AS qty_ordered,
          d.qty_picked AS qty_picked,
          (
            SELECT quantity
            FROM mostRecentSkuSnapShots
            WHERE sku = p.item_sku
          ) AS containers,
          p.facility_order_code AS delivery_number,
        FROM orderPicks p
        INNER JOIN customerOrder o
          ON p.facility_order_code = o.external_code
        INNER JOIN orderDetails d
          ON o.order_code = d.order_key
          AND p.item_sku = d.sku_code
        GROUP BY
          source_handling_unit_code,
          d.order_line,
          d.sku,
          d.storer_id,
          d.qty_ordered,
          d.qty_picked,
          p.item_sku,
          d.sts,
          d.state,
          p.facility_order_code
      ),
      orderStatusDetailData AS (
        -- Data has almost duplicates (same container, order_line, style, size) with only difference being how much has been picked for those particular entries
        -- We only want the most recent qty_picked out of the entries
        -- Only issue is event_date_timestamp_utc is the same for all almost dupe entries
        -- So I am using the one with the most qty_picked and assuming it is the most recent
        SELECT
          * EXCEPT(row_number)
        FROM (
          SELECT
            *,
            ROW_NUMBER() OVER(
              PARTITION BY container, order_line, style, size, qty_ordered
              ORDER BY qty_picked DESC
            ) AS row_number,
          FROM dataWithAlmostDupes
        )
        WHERE row_number = 1
      )
    `;

    const allowedColumnNames = [
      'container',
      'status',
      'order_line',
      'style',
      'size',
      'qty_ordered',
      'qty_picked',
      'containers',
      'delivery_number',
    ];

    const searchableColumns = [
      'container',
      'status',
      'order_line',
      'style',
      'size',
      'qty_ordered',
      'qty_picked',
      'containers',
      'delivery_number',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'orderStatusDetailData',
      allowedColumnNames,
      searchableColumns,
      searchString: params.searchString,
      cteSubquery: templateSql,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate,
      params.endDate,
      params.filterParams,
    );
    params.filterParams.orderId = params.orderId;

    const bqQueryGenerator = new BigQueryGenerator();
    const results =
      await SQLQueryGenerator.paginatedQuery<WorkstationOrdersDetailsPicksListQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (!results.list.length) {
      throw IctError.noContent();
    }

    return results;
  }

  async getWorkstationOrdersList(
    params: WorkstationOrdersListParams,
  ): Promise<PaginatedResults<WorkstationOrdersListQueryResponse[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const destinationLocationsSuffixes = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'workstation-destination-locations-suffixes',
      )
      .then(setting => (setting?.value as {suffixes: string[]}).suffixes);
    const orderDelayedTime = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'workstation-order-delayed-time',
      )
      .then(setting => setting?.value ?? 45);

    const wmsCustomerOrderTable = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order',
    );
    const pickTable = bigQueryDb.getEdpFullTablePath('gold_pick');
    const pickTaskTable = bigQueryDb.getEdpFullTablePath('gold_pick_task');
    const pickActivityTable =
      bigQueryDb.getEdpFullTablePath('gold_pick_activity');

    const templateSql = `
      WITH pickActivities AS (
        SELECT *
        FROM \`${pickActivityTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
          -- Only keep activites from locations with specific suffixes
          AND EXISTS (
            SELECT 1
            FROM UNNEST(@destinationLocationsSuffixes) AS suffix
            WHERE ENDS_WITH(induction_zone_code, suffix)
          )
      ),
      maybeActiveActivities AS (
        SELECT *
        FROM pickActivities
        WHERE UPPER(event_code) = 'ARRIVAL'
      ),
      releasedActivities AS (
        SELECT *
        FROM pickActivities
        WHERE UPPER(event_code) = 'RELEASE'
      ),
      -- Active activities are those that have an 'Arrival' event but no 'Release' event.
      -- This means that the container is still at the workstation.
      activeActivities AS (
        SELECT *
        FROM maybeActiveActivities
        WHERE handling_unit_code NOT IN (SELECT handling_unit_code FROM releasedActivities)
      ),
      pickTasks AS (
        SELECT *
        FROM \`${pickTaskTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
      ),
      -- Count of allocated pick task events per order
      allocTaskEvents AS (
        SELECT
          facility_order_code,
          COUNT(*) AS count_allocated_events,
        FROM pickTasks
        WHERE UPPER(event_code) = 'ALLOC_COMPLETE'
        GROUP BY facility_order_code
      ),
      -- Count of completed pick task events per order
      completedTaskEvents AS (
        SELECT 
          facility_order_code,
          COUNT(*) AS count_completed_events,
        FROM pickTasks
        WHERE UPPER(event_code) = 'COMPLETED'
        GROUP BY facility_order_code
      ),
      orders AS (
        SELECT *
        FROM \`${wmsCustomerOrderTable}\`
        WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
      ),
      picks AS (
        SELECT *
        FROM \`${pickTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
      ),
      rawOrderStatusData AS (
        SELECT 
          aa.workstation_code,
          aa.induction_zone_code,
          aa.handling_unit_code,
          p.pick_task_code,
          o.order_code,
          (SELECT cte.count_completed_events FROM completedTaskEvents cte WHERE cte.facility_order_code = p.facility_order_code) AS completed_picks,
          (SELECT ate.count_allocated_events FROM allocTaskEvents ate WHERE ate.facility_order_code = p.facility_order_code) AS total_picks,
          MIN(aa.event_timestamp_utc) AS arrival_time,
          TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), MIN(aa.event_timestamp_utc), MINUTE) AS dwell_time,
        FROM activeActivities aa
        LEFT JOIN picks p
          ON aa.handling_unit_code = p.destination_handling_unit_code
        LEFT JOIN orders o
          ON p.facility_order_code = o.external_code
        GROUP BY
          aa.workstation_code,
          aa.induction_zone_code,
          aa.handling_unit_code,
          p.pick_task_code,
          p.facility_order_code,
          o.order_code
      ),
      orderStatusData AS (
        SELECT
          osd.workstation_code AS station,
          osd.induction_zone_code AS position,
          osd.dwell_time AS dwell_time,
          CASE
            WHEN osd.dwell_time > @orderDelayedTime THEN "Delayed"
            WHEN osd.pick_task_code IS NULL THEN "Assigned"
            ELSE "Active"
          END AS order_status,
          IFNULL(osd.pick_task_code, '--') AS pick_task,
          IFNULL(osd.order_code, '--') AS order_id,
          osd.handling_unit_code AS container,
          IFNULL(osd.completed_picks, 0) AS completed_picks,
          IFNULL(osd.total_picks, 0) AS total_picks,
          osd.arrival_time AS arrival_time,
        FROM rawOrderStatusData osd
      )
    `;

    const allowedColumnNames = [
      'station',
      'position',
      'dwell_time',
      'order_status',
      'pick_task',
      'order_id',
      'container',
      'completed_picks',
      'total_picks',
      'arrival_time',
    ];

    const searchableColumns = [
      'station',
      'position',
      'order_status',
      'pick_task',
      'order_id',
      'container',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'orderStatusData',
      allowedColumnNames,
      searchableColumns,
      searchString: params.searchString,
      cteSubquery: templateSql,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
    };

    params.filterParams = {
      ...addStartEndDateParamValues(
        params.startDate,
        params.endDate,
        params.filterParams,
      ),
      destinationLocationsSuffixes,
      orderDelayedTime,
      types: {
        // type needed when array is empty
        destinationLocationsSuffixes: ['string'],
      },
    };

    const bqQueryGenerator = new BigQueryGenerator();
    const results =
      await SQLQueryGenerator.paginatedQuery<WorkstationOrdersListQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (!results.list.length) {
      throw IctError.noContent();
    }

    return results;
  }

  async getWorkstationOrdersStatus(
    startDate: Date,
    endDate: Date,
  ): Promise<WorkstationOrdersStatusQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const destinationLocationsSuffixes =
      (
        (
          await this.configStore.findMostRelevantSettingForUser(
            this.context.userId,
            undefined,
            'workstation-destination-locations-suffixes',
          )
        )?.value as {suffixes: string[]}
      ).suffixes ?? [];
    const orderDelayedTime =
      (
        await this.configStore.findMostRelevantSettingForUser(
          this.context.userId,
          undefined,
          'workstation-order-delayed-time',
        )
      )?.value ?? 45;

    const pickActivityTable =
      bigQueryDb.getEdpFullTablePath('gold_pick_activity');

    const sql = `
      WITH pickActivities AS (
        SELECT *
        FROM \`${pickActivityTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
          -- Only keep activities from locations with specific suffixes
          AND EXISTS (
            SELECT 1
            FROM UNNEST(@destinationLocationsSuffixes) AS suffix
            WHERE ENDS_WITH(induction_zone_code, suffix)
          )
      ),
      maybeActiveActivities AS (
        SELECT *
        FROM pickActivities
        WHERE event_code = 'Arrival'
      ),
      releasedActivities AS (
        SELECT *
        FROM pickActivities
        WHERE event_code = 'Release'
      ),
      -- Active activities are those that have an 'Arrival' event but no 'Release' event.
      -- This means that the container is still at the workstation.
      activeActivities AS (
        SELECT *
        FROM maybeActiveActivities
        WHERE handling_unit_code NOT IN (SELECT handling_unit_code FROM releasedActivities)
      ),
      delayedActivities AS (
        SELECT *
        FROM activeActivities
        WHERE TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), event_timestamp_utc, MINUTE) > @orderDelayedTime
      )
      SELECT
        (SELECT COUNT(*) FROM activeActivities) AS activeOrders,
        (SELECT COUNT(*) FROM delayedActivities) AS delayedOrders,
      ;
    `;

    const sqlOptions = {
      query: sql,
      params: {
        destinationLocationsSuffixes,
        orderDelayedTime,
        startDate,
        endDate,
      },
      types: {
        // type needed when array is empty
        destinationLocationsSuffixes: ['string'],
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows[0] as WorkstationOrdersStatusQueryResponse;
  }
}
