import sinon, {SinonStub} from 'sinon';
import {
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import {expect, use} from 'chai';
import chaiAsPromised from 'chai-as-promised';
import {BigQuery} from '@google-cloud/bigquery';
import {AppConfigSetting} from '@ict/sdk-foundations/types';
import {WorkstationStore} from '../../stores/workstation-store.ts';
import {WorkstationsController} from '../../controllers/workstation-workstations-controller.ts';
import {WorkstationController} from '../../controllers/workstation-controller.ts';

describe('WorkstationStore', () => {
  use(chaiAsPromised);
  let workstationStore: WorkstationStore;
  let clientStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let configStore: ConfigStore;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let executeMonitoredJobStub: SinonStub;

  beforeEach(() => {
    clientStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(clientStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );
    configStore = new ConfigStore(contextService);

    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();

    workstationStore = new WorkstationStore(contextService, configStore);
  });

  describe('getWorkstations', () => {
    const startDate = new Date('2021-11-30T00:00:01');
    const endDate = new Date('2021-11-30T23:59:59');

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationStore.getWorkstations(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });

    it('should return the expected rows of workstations', async () => {
      executeMonitoredJobStub.resolves([
        WorkstationsController.exampleWorkstations,
      ]);

      const outcome = await workstationStore.getWorkstations(
        startDate,
        endDate,
      );
      expect(outcome).to.deep.eq(WorkstationsController.exampleWorkstations);
    });
  });

  describe('getWorkstationList', () => {
    let settingStub: sinon.SinonStub;
    const setting: AppConfigSetting = {
      id: 'test',
      name: 'excluded-workstation-list',
      dataType: 'json',
      group: null,
      value: {
        workstations: [
          '10000000002',
          '10000000003',
          '10000000004',
          '10000000006',
          '10000000007',
        ],
      },
    };

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(setting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationStore.getWorkstationList(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should return the expected workstation list', async () => {
      const exampleWorkstationList =
        WorkstationController.exampleWorkstationList.workstationList;
      executeMonitoredJobStub.resolves([exampleWorkstationList]);

      const outcome = await workstationStore.getWorkstationList();
      expect(outcome).to.deep.eq(exampleWorkstationList);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getWorkstationDailyPerformanceList', () => {
    const startDate = new Date('2021-11-30T00:00:01');
    const endDate = new Date('2021-11-30T23:59:59');
    const workstations: string[] = [];

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationStore.getWorkstationDailyPerformanceList(
          workstations,
          startDate,
          endDate,
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getWorkstationContainersList', () => {
    const startDate = new Date('2021-11-30T00:00:01');
    const endDate = new Date('2021-11-30T23:59:59');

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      await expect(
        workstationStore.getWorkstationContainersList({
          startDate,
          endDate,
          size: 'L',
          style: 'style',
          zone: 'ASRS',
          page: 0,
          limit: 50,
          sortFields: [],
          filters: undefined,
          searchString: undefined,
        }),
      ).to.be.rejectedWith(IctError);
    });

    it('should return a list of containers', async () => {
      const mockData = [
        {
          container: 'container',
          status: 'status',
          transport: '--',
          quantity: 5,
          last_location: 'lastLocation',
          event_time: {value: '2021-11-30T00:00:01'},
          totalResults: 1,
        },
      ];
      executeMonitoredJobStub.resolves([mockData]);

      const result = await workstationStore.getWorkstationContainersList({
        startDate,
        endDate,
        size: 'L',
        style: 'style',
        zone: 'ASRS',
        page: 0,
        limit: 50,
        sortFields: [],
        filters: undefined,
        searchString: undefined,
      });
      const expected = {
        list: mockData.map(({totalResults, ...rest}) => rest),
        totalResults: 1,
      };
      expect(result).to.deep.eq(expected);
    });
  });

  describe('getWorkstationStatusOrdersDetailsPicksList', () => {
    it('should return a list of picks status for the order', async () => {
      const mockData = [
        {
          container: 'test container',
          status: 'Open',
          order_line: '0004',
          style: 'ASC',
          size: 'S',
          qty_ordered: 3,
          qty_picked: 1,
          containers: 4,
          delivery_number: 'test delivery',
          totalResults: 1,
        },
      ];
      executeMonitoredJobStub.resolves([mockData]);

      const result =
        await workstationStore.getWorkstationOrdersDetailsPicksList({
          startDate: new Date('2021-11-30T00:00:01'),
          endDate: new Date('2021-11-30T23:59:59'),
          orderId: 'test id',
          page: 0,
          limit: 50,
          sortFields: [],
        });

      const expected = {
        list: mockData.map(({totalResults, ...rest}) => rest),
        totalResults: 1,
      };
      expect(result).to.deep.eq(expected);
    });
  });

  describe('getWorkstationStatusOrdersDetailsPicksList', () => {
    it('should return a list of picks status for the order', async () => {
      const mockData = [
        {
          container: 'test container',
          status: 'Open',
          order_line: '0004',
          style: 'ASC',
          size: 'S',
          qty_ordered: 3,
          qty_picked: 1,
          containers: 4,
          delivery_number: 'test delivery',
          totalResults: 1,
        },
      ];
      executeMonitoredJobStub.resolves([mockData]);

      const result =
        await workstationStore.getWorkstationOrdersDetailsPicksList({
          startDate: new Date('2021-11-30T00:00:01'),
          endDate: new Date('2021-11-30T23:59:59'),
          orderId: 'test id',
          page: 0,
          limit: 50,
          sortFields: [],
        });

      const expected = {
        list: mockData.map(({totalResults, ...rest}) => rest),
        totalResults: 1,
      };
      expect(result).to.deep.eq(expected);
    });
  });

  describe('getWorkstationOrderStatus', () => {
    const startDate = new Date('2021-11-30T00:00:01');
    const endDate = new Date('2021-11-30T23:59:59');

    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.onFirstCall().resolves({value: {suffixes: ['B1']}});
      settingStub.onSecondCall().resolves({value: 40});
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should return active and delayed orders', async () => {
      executeMonitoredJobStub.resolves([
        [{activeOrders: 10, delayedOrders: 5}],
      ]);
      const result = await workstationStore.getWorkstationOrdersStatus(
        startDate,
        endDate,
      );
      expect(result).to.deep.eq({activeOrders: 10, delayedOrders: 5});
      expect(settingStub.calledTwice).to.be.true;
    });
  });
});
