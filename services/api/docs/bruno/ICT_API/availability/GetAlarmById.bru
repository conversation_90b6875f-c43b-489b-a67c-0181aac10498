meta {
  name: GetAlarmById
  type: http
  seq: 4
}

get {
  url: {{api_host}}/availability/alarms/fe3156ed
  body: none
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should be a single alarm object", function() {
    const alarm = res.getBody();
    expect(alarm).to.be.an('object');
    expect(alarm).to.have.property('id');
    expect(alarm).to.have.property('title');
    expect(alarm).to.have.property('description');
    expect(alarm).to.have.property('tag');
    expect(alarm).to.have.property('location');
    expect(alarm).to.have.property('timing');
    expect(alarm).to.have.property('status');
    expect(alarm).to.have.property('reason');
  });
  
  test("Location should have required fields", function() {
    const location = res.getBody().location;
    expect(location).to.have.property('area');
    expect(location).to.have.property('section');
    expect(location).to.have.property('equipment');
  });
  
  test("Timing should have required fields", function() {
    const timing = res.getBody().timing;
    expect(timing).to.have.property('startTime');
    expect(timing).to.have.property('duration');
    expect(timing.duration).to.be.a('string');
  });
  
  test("Should match expected alarm ID", function() {
    expect(res.getBody().id).to.equal('fe3156ed');
  });
}