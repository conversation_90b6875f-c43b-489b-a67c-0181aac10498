meta {
  name: PostAlarmsListWithUpdatedSort
  type: http
  seq: 3
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2024-01-01T00:00:00.000Z",
    "end_date": "2024-01-31T23:59:59.999Z",
    "filters": {
      "areas": ["Decant", "Receiving"],
      "sections": ["CC001", "CC002"],
      "equipment": ["CA002DPS1"],
      "statuses": ["KEPT", "Active"],
      "faultIds": ["fe3156ed"],
      "reasons": ["Equipment Maintenance"]
    },
    "sortFields": [
      {
        "columnName": "updatedEndTime",
        "isDescending": true
      }
    ],
    "groupByFields": [],
    "limit": 20,
    "offset": 0,
    "searchString": "E-STOP RELAY"
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Metadata should have correct structure", function() {
    const metadata = res.getBody().metadata;
    expect(metadata).to.have.property('page');
    expect(metadata).to.have.property('limit');
    expect(metadata).to.have.property('totalResults');
  });
  
  test("Data should be array of alarms", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    if (data.length > 0) {
      const alarm = data[0];
      expect(alarm).to.have.property('id');
      expect(alarm).to.have.property('title');
      expect(alarm).to.have.property('location');
      expect(alarm).to.have.property('timing');
      expect(alarm.location).to.have.property('area');
      expect(alarm.location).to.have.property('section');
      expect(alarm.location).to.have.property('equipment');
      expect(alarm.timing).to.have.property('startTime');
      expect(alarm.timing).to.have.property('duration');
    }
  });
}