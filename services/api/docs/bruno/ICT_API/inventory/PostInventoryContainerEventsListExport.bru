meta {
  name: PostInventoryContainerEventsListExport
  type: http
  seq: 39
}

post {
  url: {{api_host}}/inventory/container-events/list/export/:containerId
  body: json
  auth: inherit
}

params:path {
  containerId: BJS249
}

body:json {
  {
    "limit": 50,
    "page": 1,
    "sortFields": [
      {
        "columnName": "event_type",
        "isDescending": false
      }
    ],
    "months": 12,
    "columns": {
      "timestamp": true,
      "event": true,
      "workstationCode": true,
      "destinationContainer": true,
      "operator": true,
      "sku": true,
      "quantity": true
    }
  }
}
