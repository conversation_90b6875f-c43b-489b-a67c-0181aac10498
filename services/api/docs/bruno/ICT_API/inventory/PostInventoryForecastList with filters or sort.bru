meta {
  name: PostInventoryForecastList with filters or sort
  type: http
  seq: 25
}

post {
  url: {{api_host}}/inventory/forecast/list
  body: json
  auth: inherit
}

body:json {
  {
      "limit": 50,
      "page": 1,
      "filters": {
              "type": "single",
              "name": "sku",
              "comparison": "Like",
              "value": "%TBN%"
      },
      "sortFields": [
          {
              "columnName": "outstandingDemand",
              "isDescending": true
          }
      ]
  }
}
