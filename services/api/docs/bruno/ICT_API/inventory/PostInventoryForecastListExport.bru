meta {
  name: PostInventoryForecastListExport
  type: http
  seq: 2
}

post {
  url: {{api_host}}/inventory/forecast/list/export
  body: json
  auth: inherit
}

body:json {
  {
    "page": 1,
    "limit": 50,
    "filters": null,
    "sortFields": [],
    "columns": {
      "sku": true,
      "current.reserveStorage": true,
      "current.forwardPick": true,
      "projected.pendingReplenishment": true,
      "projected.pendingPicks": true,
      "projected.allocatedOrders": true,
      "projected.projectedForwardPick": true,
      "forecast.averageReplenishment": true,
      "forecast.averageDemand": true,
      "forecast.demandTomorrow": true,
      "forecast.knownDemand": true,
      "forecast.forwardPickTomorrow": true,
      "forecast.twoDayDemand": true,
      "forecast.twoDayForwardPick": true
    }
  }
}
