meta {
  name: UpdateInventoryProcessFlowEdge
  type: http
  seq: 23
}

put {
  url: {{api_host}}/inventory/process-flow/edges/:edgeId
  body: json
  auth: inherit
}

params:path {
  edgeId: 5:fef3831c-7abc-492d-8eb6-d8460db18983:1152922604118474762
}

body:json {
  {
    "id": "5:fef3831c-7abc-492d-8eb6-d8460db18983:1152922604118474762",
    "source": "4:fef3831c-7abc-492d-8eb6-d8460db18983:10",
    "metrics": [
      {
        "id": "dematic:grandrapids_mi:multishuttle:stations:units_per_hour",
        "type": "hourly",
        "pastHour": 0,
        "past15Minutes": 0,
        "change": "unchanged"
      }
    ],
    "direction": "upstream",
    "target": "4:fef3831c-7abc-492d-8eb6-d8460db18983:11"
  }
}
