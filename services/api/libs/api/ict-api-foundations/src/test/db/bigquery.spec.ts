import {assert} from 'chai';
import sinon from 'sinon';

import {BigQuery, Query} from '@google-cloud/bigquery';
import {BigQueryDatabase} from '../../db/bigquery.ts';
import {DatabaseTypes} from '../../db/db-types.ts';
import {ContextService} from '../../context/context-service.ts';

describe('BigQueryDatabase', () => {
  let bigQueryStub: sinon.SinonStubbedInstance<BigQuery>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let testDatabase: BigQueryDatabase;

  afterEach(() => {
    sinon.restore();
  });

  describe('getFullTablePath', () => {
    const testDatasetName = 'test-dataset';
    const testProjectId = 'test-project';
    const testTableName = 'test-table';

    beforeEach(() => {
      bigQueryStub = sinon.createStubInstance(BigQuery);
      bigQueryStub.projectId = testProjectId;

      contextServiceStub = sinon.createStubInstance(ContextService);
      testDatabase = new BigQueryDatabase(bigQueryStub, testDatasetName);
      // eslint-disable-next-line dot-notation
      testDatabase['contextService'] = contextServiceStub; // Inject the stubbed contextService
    });

    it('should add table name to context and return the correct full table path', () => {
      const expectedPath = `${testProjectId}.${testDatasetName}_oa_curated.${testTableName}`;

      const result = testDatabase.getFullTablePath(testTableName);

      assert.equal(result, expectedPath);
      sinon.assert.calledOnceWithExactly(
        contextServiceStub.addTableName,
        testTableName
      );
    });
  });

  describe('getFunctionPath', () => {
    const testDatasetName = 'test-dataset';
    const testProjectId = 'test-project';
    const testFunctionName = 'test-function';

    beforeEach(() => {
      bigQueryStub = sinon.createStubInstance(BigQuery);
      bigQueryStub.projectId = testProjectId;
      testDatabase = new BigQueryDatabase(bigQueryStub, testDatasetName);
    });

    it('result should match expected', () => {
      assert.equal(
        testDatabase.getFunctionPath(testFunctionName),
        `${testDatasetName}_oa_curated.${testFunctionName}`
      );
    });
  });

  describe('getType', () => {
    const testDatasetName = 'test-dataset';
    const testProjectId = 'test-project';

    beforeEach(() => {
      bigQueryStub = sinon.createStubInstance(BigQuery);
      bigQueryStub.projectId = testProjectId;
      testDatabase = new BigQueryDatabase(bigQueryStub, testDatasetName);
    });

    it('result should match expected', () => {
      assert.equal(testDatabase.getType(), DatabaseTypes.BigQuery);
    });
  });

  describe('executeMonitoredJob', async () => {
    const testDatasetName = 'test-dataset';
    const testProjectId = 'test-project';
    const mockQueryResults = [{result: 'test'}];
    const mockJob = {
      getQueryResults: sinon.fake.resolves([mockQueryResults]),
      getMetadata: sinon.fake.resolves([
        {
          statistics: {
            query: {
              totalBytesBilled: '12345',
            },
          },
          configuration: {
            query: {
              query: 'SELECT * FROM `test-project.test-dataset.test-table`',
            },
          },
        },
      ]),
    };

    beforeEach(() => {
      bigQueryStub = sinon.createStubInstance(BigQuery);
      bigQueryStub.projectId = testProjectId;

      sinon.stub(contextServiceStub, 'organization').get(() => {
        return {metadata: {dataset: testDatasetName, tableauSite: 'test'}};
      });

      sinon.stub(contextServiceStub, 'datasetId').get(() => testDatasetName);

      testDatabase = new BigQueryDatabase(bigQueryStub, testDatasetName);
      // eslint-disable-next-line dot-notation
      testDatabase['contextService'] = contextServiceStub;
      (bigQueryStub.createQueryJob as sinon.SinonStub).resolves([mockJob]);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('a bigquery job is created, executed, and metadata is retrieved', async () => {
      const sqlOptions: Query = {
        query: 'SELECT 1 from 2',
      };

      const results = await testDatabase.executeMonitoredJob(sqlOptions);

      assert.isTrue(bigQueryStub.createQueryJob.calledOnce);
      assert.isTrue(mockJob.getMetadata.calledOnce);
      assert.isTrue(mockJob.getQueryResults.calledOnce);
      assert.deepEqual(results, [mockQueryResults]);
    });

    it('application label is added to job labels', async () => {
      const sqlOptions: Query = {
        query: 'SELECT 1 from 2',
        labels: {
          myCustomLabel: 'cool',
        },
      };

      await testDatabase.executeMonitoredJob(sqlOptions);

      assert.isTrue(
        bigQueryStub.createQueryJob.calledWith({
          ...sqlOptions,
          labels: {
            ...sqlOptions.labels,
            application: 'control-tower-intelligent',
            tenant: testDatasetName,
          },
        })
      );
    });
  });
});
