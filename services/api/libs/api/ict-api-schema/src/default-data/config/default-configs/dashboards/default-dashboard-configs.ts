import {DefaultSettingDefinition} from '../default-setting-definition.ts';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const dashboardConfigSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  required: ['title', 'content'],
  properties: {
    title: {
      type: 'string',
      description: 'The title of the dashboard',
    },
    content: {
      type: 'array',
      items: {
        type: 'object',
        required: ['h', 'w', 'x', 'y', 'content'],
        properties: {
          h: {
            type: 'integer',
            description: 'Height of the widget in grid units',
          },
          w: {
            type: 'integer',
            description: 'Width of the widget in grid units',
          },
          x: {
            type: 'integer',
            description: 'X coordinate of the widget position',
          },
          y: {
            type: 'integer',
            description: 'Y coordinate of the widget position',
          },
          content: {
            type: 'object',
            required: ['id', 'type'],
            properties: {
              id: {
                type: 'string',
                description: 'Unique identifier for the widget',
              },
              type: {
                type: 'string',
                description: 'Type of the widget',
                enum: [
                  'total-stations',
                  'station-performance',
                  'logged-in-operators',
                  'station-health',
                  'workstation-list',
                  'kpi',
                  'advice-list',
                  'outbound-overview-areas',
                  'kpi-chart',
                  'bar-chart',
                  'pie-chart',
                  'combo-chart',
                ],
              },
              name: {
                type: 'string',
                description: 'Display name of the widget',
              },
              options: {
                type: 'object',
                description: 'Widget-specific configuration options',
                properties: {
                  type: {
                    type: 'string',
                  },
                  title: {
                    type: 'string',
                  },
                  bgColor: {
                    type: 'string',
                  },
                  description: {
                    type: 'string',
                  },
                  unit: {
                    type: 'boolean',
                  },
                  precision: {
                    type: 'number',
                  },
                  valueSuffix: {
                    type: 'string',
                  },
                },
              },
              filters: {
                type: 'object',
                properties: {
                  datePeriodRange: {
                    type: 'string',
                  },
                  groupBy: {
                    type: 'string',
                  },
                },
              },
            },
          },
        },
      },
    },
    showDateRange: {
      type: 'boolean',
      description: 'Whether to show date range selector',
    },
    defaultDatePeriodRange: {
      type: 'string',
      description: 'Default date range to display',
      enum: ['today', 'thisWeek', 'lastWeek'],
    },
  },
};

const workstationOverviewDashboardConfig: DefaultSettingDefinition = {
  name: 'workstation-overview-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Workstation Overview page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Workstation Overview',
    autoRefreshEnabled: true,
    autoRefreshInterval: 60,
    content: [
      {
        h: 3,
        w: 4,
        x: 0,
        y: 0,
        content: {
          id: 'total-stations',
          type: 'total-stations',
          options: {},
        },
      },
      {
        h: 3,
        w: 4,
        x: 4,
        y: 0,
        content: {
          id: 'logged-in-operators',
          type: 'logged-in-operators',
          options: {},
        },
      },
      {
        h: 3,
        w: 4,
        x: 8,
        y: 0,
        content: {
          id: 'workstation-order-status',
          type: 'workstation-order-status',
          options: {},
        },
      },
      {
        h: 2,
        w: 4,
        x: 0,
        y: 4,
        content: {
          id: 'station-performance',
          type: 'station-performance',
          options: {},
        },
      },
      {
        h: 2,
        w: 4,
        x: 4,
        y: 3,
        content: {
          id: 'station-health',
          type: 'station-health',
          options: {},
        },
      },
      {
        h: 6,
        w: 12,
        x: 0,
        y: 5,
        content: {
          id: 'workstation-list',
          type: 'workstation-list',
          options: {},
        },
      },
    ],
  },
};

const inboundOverviewDashboardConfig: DefaultSettingDefinition = {
  name: 'inbound-overview-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Inbound Overview page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Inbound Overview',
    content: [
      {
        h: 2,
        w: 3,
        x: 3,
        y: 3,
        content: {
          id: 'inventory-advices-outstanding',
          type: 'kpi',
          options: {
            type: 'inventory-advices-outstanding',
            title: 'Outstanding Advices',
            bgColor: '#ffffff',
            description: 'Number of outstanding advices',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 0,
        y: 3,
        content: {
          id: 'inventory-advices-in-progress',
          type: 'kpi',
          options: {
            type: 'inventory-advices-in-progress',
            title: 'In Progress Advices',
            bgColor: '#ffffff',
            description: 'Number of advices currently in progress',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 6,
        y: 3,
        content: {
          id: 'inventory-advices-finished',
          type: 'kpi',
          options: {
            type: 'inventory-advices-finished',
            title: 'Finished Advices',
            bgColor: '#ffffff',
            description: 'Number of finished advices',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 9,
        y: 3,
        content: {
          id: 'inventory-advices-cycle-time',
          type: 'kpi',
          options: {
            type: 'inventory-advices-cycle-time',
            title: 'Advice Cycle Time',
            bgColor: '#ffffff',
            description: 'Average cycle time for advices',
          },
        },
      },
      {
        h: 5,
        w: 12,
        x: 0,
        y: 5,
        content: {
          id: 'advice-list',
          name: 'Advice List',
          type: 'advice-list',
          options: {},
        },
      },
    ],
  },
};

const outboundOverviewDashboardConfig: DefaultSettingDefinition = {
  name: 'outbound-overview-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Outbound Overview page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Outbound Overview',
    content: [
      {
        h: 2,
        w: 3,
        x: 0,
        y: 0,
        content: {
          id: 'facility-orders-shipped',
          type: 'kpi',
          options: {
            type: 'facility-orders-shipped',
            title: 'Facility Orders Shipped',
            bgColor: '#ffffff',
            description: 'Number of orders shipped vs total',
            unit: true,
            precision: 0,
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 3,
        y: 0,
        content: {
          id: 'facility-orders-progress',
          type: 'kpi',
          options: {
            type: 'facility-orders-progress',
            unit: true,
            title: 'Facility Order Progress',
            bgColor: '#ffffff',
            precision: 1,
            description: 'Percentage of facility order progress',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 6,
        y: 0,
        content: {
          id: 'facility-orders-throughput-rate',
          type: 'kpi',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'facility-orders-throughput-rate',
            title: 'Facility Throughput Rate',
            precision: 1,
            unit: true,
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 9,
        y: 0,
        content: {
          id: 'facility-orders-cycle-time',
          type: 'kpi',
          options: {
            type: 'facility-orders-cycle-time',
            unit: true,
            title: 'Facility Order Cycle Time',
            bgColor: '#ffffff',
            description: 'Average facility order cycle time',
            valueSuffix: 'minutes',
            precision: 0,
          },
        },
      },
      {
        h: 2,
        w: 4,
        x: 0,
        y: 2,
        content: {
          id: 'orders-facility-estimated-completion',
          type: 'kpi',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'orders-facility-estimated-completion',
            title: 'Facility Estimated Completion',
            unit: true,
            precision: 0,
          },
        },
      },
      {
        h: 2,
        w: 4,
        x: 4,
        y: 2,
        content: {
          id: 'operators-active',
          type: 'kpi',
          options: {
            type: 'operators-active',
            title: 'Active Operators',
            bgColor: '#ffffff',
            showDelta: true,
            showTarget: false,
            description: 'Number of active operators',
            targetValue: 5,
            targetDirection: 'above',
            targetDisplayStyle: 'arrow',
            precision: 0,
          },
        },
      },
      {
        h: 2,
        w: 4,
        x: 8,
        y: 2,
        content: {
          id: 'facility-orders-lines-progress',
          type: 'kpi',
          options: {
            type: 'facility-orders-lines-progress',
            unit: true,
            title: 'Facility Order Line Progress',
            bgColor: '#ffffff',
            description: 'Percentage of facility order line progress',
            precision: 1,
          },
        },
      },
      {
        h: 6,
        w: 12,
        x: 0,
        y: 4,
        content: {
          id: 'e43bb72c-a619-45c8-acb3-d01c418829fd',
          type: 'outbound-overview-areas',
          filters: {
            datePeriodRange: 'thisWeek',
          },
          options: {
            areas: [
              {
                key: 'shipping',
                label: 'Shipping Area',
              },
              {
                key: 'picking',
                label: 'Picking Area',
              },
            ],
          },
        },
      },
    ],
    showDateRange: true,
    defaultDatePeriodRange: 'today',
  },
};

const inventoryOverviewDashboardConfig: DefaultSettingDefinition = {
  name: 'inventory-overview-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Inventory Overview page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Inventory Overview',
    content: [
      {
        h: 2,
        w: 3,
        x: 6,
        y: 0,
        content: {
          id: 'orders-outstanding',
          name: 'Orders Outstanding',
          type: 'kpi',
          options: {
            type: 'orders-pick-cycle-counts',
            title: 'Cycle Counts',
            bgColor: '#ffffff',
            metricType: 'orders-outstanding',
            description: 'Number of outstanding orders',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 0,
        y: 0,
        content: {
          id: 'orders-fulfillment',
          name: 'Projected Order Fulfillment',
          type: 'kpi',
          options: {
            type: 'orders-outstanding',
            title: 'Orders Outstanding',
            bgColor: '#ffffff',
            metricType: 'orders-projected-fulfillment',
            description: 'Projected order fulfillment rate',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 3,
        y: 0,
        content: {
          id: 'orders-cycletime',
          name: 'Order Cycle Time',
          type: 'kpi',
          options: {
            type: 'orders-projected-fulfillment',
            unit: true,
            title: 'Projected Order Fulfillment',
            bgColor: '#ffffff',
            metricType: 'orders-cycle-time',
            description: 'Average order cycle time',
            valueSuffix: 'minutes',
          },
        },
      },
      {
        h: 2,
        w: 3,
        x: 9,
        y: 0,
        content: {
          id: 'orders-storage-utilization',
          name: 'Storage Utilization',
          type: 'kpi',
          options: {
            type: 'inventory-storage-utilization',
            unit: true,
            title: 'Storage Utilization',
            bgColor: '#ffffff',
            precision: 1,
            metricType: 'inventory-storage-utilization',
            description: 'Storage utilization percentage',
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 0,
        y: 2,
        content: {
          id: 'at-inventory',
          name: 'At Inventory',
          type: 'kpi-chart',
          options: {
            type: 'inventory-stock-distribution-at-percentage',
            title: 'At Inventory',
            colorId: null,
            showUnit: true,
            precision: 1,
            dateFormat: 'hh:mm a',
            showMaxKpi: false,
            showMinKpi: false,
            valueLabel: '',
            targetValue: 55,
            valueFormat: '%',
            showTotalKpi: false,
            showAverageKpi: false,
            showCurrentKpi: true,
            showTargetLine: false,
            showAverageLine: false,
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 0,
        y: 5,
        content: {
          id: 'under-inventory',
          name: 'Under Inventory',
          type: 'kpi-chart',
          options: {
            type: 'inventory-stock-distribution-over-percentage',
            title: 'Over Inventory',
            colorId: 'teal',
            showUnit: true,
            lineColor: '#007d79',
            precision: 1,
            dateFormat: 'hh:mm a',
            valueLabel: '',
            targetValue: 55,
            valueFormat: '%',
            showCurrentKpi: true,
            showTargetLine: false,
            showAverageLine: false,
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 6,
        y: 2,
        content: {
          id: 'over-inventory',
          name: 'Over Inventory',
          type: 'kpi-chart',
          options: {
            type: 'inventory-stock-distribution-no-percentage',
            title: 'No Inventory',
            colorId: 'cyan',
            showUnit: true,
            lineColor: '#33b1ff',
            precision: 1,
            dateFormat: 'hh:mm a',
            valueLabel: '',
            targetValue: 55,
            valueFormat: '%',
            showCurrentKpi: true,
            showTargetLine: false,
            showAverageLine: false,
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 6,
        y: 5,
        content: {
          id: 'no-inventory',
          name: 'No Inventory',
          type: 'kpi-chart',
          options: {
            type: 'inventory-stock-distribution-under-percentage',
            title: 'Under Inventory',
            colorId: 'magenta',
            showUnit: true,
            lineColor: '#ff7eb6',
            precision: 1,
            dateFormat: 'hh:mm a',
            valueLabel: '',
            targetValue: 55,
            valueFormat: '%',
            showCurrentKpi: true,
            showTargetLine: false,
            showAverageLine: false,
          },
        },
      },
    ],
    showDateRange: true,
    defaultDatePeriodRange: 'today',
    availableDateRanges: ['today', 'last7days', 'last14days', 'last30days'],
  },
};

const pickingBufferAreaDetailsDashboardConfig: DefaultSettingDefinition = {
  name: 'picking-buffer-area-details-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Picking Buffer Area Details page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Picking Buffer Area Details',
    content: [
      {
        h: 4,
        w: 6,
        x: 0,
        y: 3,
        content: {
          id: '99104560-45ad-4cd1-9e58-8e9f6b539571',
          type: 'bar-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Aisle',
            sortBy: 'ascending',
            filters: {
              groupBy: 'aisle',
            },
            groupBy: '',
            sortByName: 'none',
            orientation: 'horizontal',
          },
        },
      },
      {
        h: 4,
        w: 6,
        x: 6,
        y: 3,
        content: {
          id: 'b4809517-4276-4aa9-bcb3-a1449ba56de3',
          type: 'bar-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Level',
            sortBy: 'ascending',
            colorId: 'cyan',
            filters: {
              groupBy: 'level',
            },
            groupBy: '',
            barColor: '#33b1ff',
            sortByName: 'none',
            orientation: 'horizontal',
          },
        },
      },
      {
        h: 4,
        w: 6,
        x: 0,
        y: 7,
        content: {
          id: 'b51a1852-75c0-4c19-80b3-506c4a21edbb',
          type: 'bar-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Device Type',
            sortBy: 'ascending',
            colorId: 'teal',
            filters: {
              groupBy: 'device_functional_type',
            },
            groupBy: '',
            barColor: '#007d79',
            sortByName: 'none',
            orientation: 'horizontal',
          },
        },
      },
      {
        h: 4,
        w: 6,
        x: 6,
        y: 7,
        content: {
          id: '1d476fdc-3a09-4de7-998d-eee70ac3ecca',
          type: 'bar-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Device ID',
            sortBy: 'ascending',
            colorId: 'magenta',
            filters: {
              groupBy: 'device_code',
            },
            groupBy: '',
            barColor: '#ff7eb6',
            sortByName: 'none',
            orientation: 'horizontal',
            displayLimit: 30,
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 0,
        y: 11,
        content: {
          id: '810f60c0-1340-4d83-a76e-7f6172735f63',
          type: 'bar-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Status',
            sortBy: 'ascending',
            colorId: 'red',
            filters: {
              groupBy: 'reason_name',
            },
            groupBy: '',
            barColor: '#fa4d56',
            sortByName: 'none',
            orientation: 'horizontal',
          },
        },
      },
      {
        h: 3,
        w: 6,
        x: 6,
        y: 11,
        content: {
          id: '9fb6914e-974c-4170-8a2e-bc9ca4683722',
          type: 'pie-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'faults',
            title: 'By Status',
            filters: {
              groupBy: 'reason_name',
            },
            groupBy: '',
            pieType: 'donut',
            showUnit: true,
            showPercentage: true,
          },
        },
      },
    ],
    showDateRange: true,
  },
};

const inventoryReplenishmentDashboardConfig: DefaultSettingDefinition = {
  name: 'inventory-replenishment-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the Inventory Replenishment page in the Operations Visibility package',
  defaultValueJson: {
    title: 'Replenishment Details',
    content: [
      {
        h: 3,
        w: 4,
        x: 0,
        y: 0,
        content: {
          id: '3f5a0917-6b23-430a-b5bc-9e6907fedb7c',
          type: 'kpi-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'daily-replenishments',
            title: 'Daily Replenishments',
            showUnit: true,
            precision: 0,
            showLegend: false,
            showAverageKpi: true,
          },
        },
      },
      {
        h: 3,
        w: 4,
        x: 4,
        y: 0,
        content: {
          id: '8e961b48-c55c-4733-9a33-a7bba98c12bd',
          type: 'kpi-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'daily-cycle-times',
            title: 'Daily Cycle Time',
            showUnit: true,
            precision: 0,
            showLegend: false,
            showAverageKpi: true,
          },
        },
      },
      {
        h: 3,
        w: 4,
        x: 8,
        y: 0,
        content: {
          id: '96112246-49f9-4c85-b5cb-72f25864e86f',
          type: 'kpi-chart',
          filters: {
            datePeriodRange: 'today',
          },
          options: {
            type: 'daily-pending-orders',
            title: 'Daily Pending Orders',
            showUnit: true,
            precision: 0,
            showLegend: false,
            showAverageKpi: true,
          },
        },
      },
      {
        h: 3,
        w: 12,
        x: 0,
        y: 3,
        content: {
          id: 'cf72feb5-d58a-42d1-b6f5-ca602d8bcbf9',
          type: 'combo-chart',
          filters: {
            datePeriodRange: 'lastWeek',
          },
          options: {
            type: 'daily-replenishments-by-shift',
            title: 'Daily Replenishments by Shift',
            chartStyle: 'area',
          },
        },
      },
      {
        h: 4,
        w: 12,
        x: 0,
        y: 6,
        content: {
          id: 'dc665515-23de-44b4-9503-5aac8b4afc45',
          type: 'combo-chart',
          filters: {
            datePeriodRange: 'lastWeek',
          },
          options: {
            type: 'replenishment-task-type-data',
            title: 'Daily Replenishments by Type',
            chartStyle: 'stacked-column',
          },
        },
      },
    ],
    showDateRange: true,
    defaultDatePeriodRange: 'last7days',
    availableDateRanges: ['last7days', 'last14days', 'last30days'],
  },
};

const fileUploadDashboardConfig: DefaultSettingDefinition = {
  name: 'file-upload-dashboard',
  dataType: 'json',
  description:
    'Defines the UI dashboard configuration of the File Upload page in the Advanced Orchestration package',
  defaultValueJson: {
    sapOtherFiles: false,
    sapOrderDetails: true,
    sapOrderManagement: true,
  },
};

export const DefaultDashboardConfigs: DefaultSettingDefinition[] = [
  inboundOverviewDashboardConfig,
  outboundOverviewDashboardConfig,
  inventoryOverviewDashboardConfig,
  workstationOverviewDashboardConfig,
  pickingBufferAreaDetailsDashboardConfig,
  inventoryReplenishmentDashboardConfig,
  fileUploadDashboardConfig,
].map(dashConfig => {
  return {
    ...dashConfig,
    // jsonSchema: dashboardConfigSchema, // uncomment this once the schema is finalized
    group: 'dashboard-configurations',
  };
});
