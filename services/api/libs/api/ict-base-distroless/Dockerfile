# this Dockerfile does expect to be run in the root folder of the repo as it needs to copy the foundation files that it depends on
FROM ict-base-shared AS builder

FROM gcr.io/distroless/nodejs22-debian12@sha256:41fe203bd1286fffa4b546fdc2fce60f4ce9a3b80f6ad0b458aa68b66ac97fbb
COPY --from=builder /etc/ssl/certs /etc/ssl/certs
COPY --from=builder /usr/app/cloud-sql-proxy /usr/app/cloud-sql-proxy
COPY --from=builder /usr/app/node_modules /usr/app/node_modules/
COPY --from=builder /usr/app/apps/api/ict-instrumentation/build/instrumentation.cjs /usr/app/apps/api/ict-instrumentation/build/instrumentation.cjs

WORKDIR /usr/app

COPY ./libs/ /usr/app/libs/
COPY ./apps/api/register-tsnode.mjs ./apps/api/register-tsnode.mjs
COPY ./apps/api/starter.ts ./apps/api/starter.ts

ENV NODE_PATH=/usr/app/node_modules
ENV PATH="$PATH:/nodejs/bin"
