import { z } from 'zod';
import { Environments } from '../../types/environment';

export const TestEnvironmentSchema = z.object({
  ICT_USER_EMAIL: z.string(),
  ICT_USER_PASSWORD: z.string(),
  ICT_ENV: z.enum(Environments),
  ICT_BASE_URL: z.string().optional(),
  ICT_API_URL: z.string().optional(),
  ICT_ORGANIZATION: z.string().optional(),
});

export type TestEnvironmentConfiguration = z.infer<typeof TestEnvironmentSchema>;
