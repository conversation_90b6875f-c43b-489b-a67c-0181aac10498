import Container from 'typedi';
import { TestOptions as BaseTestOptions, test as base } from './smoke-fixture';
import { TestConfigurationService } from '@ui-adk/services/configuration';
import { ControlTower } from '@ui-adk/app';
import {
  Dashboard,
  CuratedData,
  DataExplorer,
  InventoryList,
  ContainerList,
  FacilityProcessFlow,
  InventoryForecast,
} from '@ui-adk/views';
import { ConfigurableDashboardUrlMap, DashboardId } from '@ui-adk/views/dashboard/dashboard-id';
import { DashboardConfigMap } from '@ui-adk/views/dashboard/configurations';

export type TestOptions = BaseTestOptions & {
  /**
   * controls the dashboard that will be used for for dashboard and dashboardDefinition fixtures.
   */
  dashboardId: DashboardId;

  /**
   * Represents the dashboard view, to choose which dashboard set the dashboardId.
   */
  dashboard: Dashboard;

  /**
   * Represents the curated-data view.
   */
  curatedData: CuratedData;

  /**
   * Represents the inventory-list view.
   */
  inventoryList: InventoryList;

  /**
   * Represents the container-list view.
   */
  containerList: ContainerList;

  /**
   * Represents the facility-process-flow view.
   */
  facilityProcessFlow: FacilityProcessFlow;

  /**
   * Represents the data-explorer view.
   */
  dataExplorer: DataExplorer;

  /**
   * Represents the inventory-forecast view.
   */
  inventoryForecast: InventoryForecast;
};

export const test = base.extend<TestOptions>({
  dashboardId: ['outbound-overview', { option: true }],

  controlTower: async ({ page }, use) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await controlTower.goto();
    await use(controlTower);
  },

  dashboard: async ({ dashboardId, debug }, use) => {
    const view = new Dashboard(
      debug.page,
      dashboardId,
      Container.get(TestConfigurationService),
      ConfigurableDashboardUrlMap.get(dashboardId),
      DashboardConfigMap.get(dashboardId).apiEndpoints,
    );
    await view.goto();
    await use(view);
  },

  curatedData: async ({ debug }, use) => {
    const view = new CuratedData(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },

  facilityProcessFlow: async ({ debug }, use) => {
    const view = new FacilityProcessFlow(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },

  inventoryList: async ({ debug }, use) => {
    const view = new InventoryList(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },

  containerList: async ({ debug }, use) => {
    const view = new ContainerList(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },

  dataExplorer: async ({ debug }, use) => {
    const view = new DataExplorer(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },

  inventoryForecast: async ({ debug }, use) => {
    const view = new InventoryForecast(debug.page, Container.get(TestConfigurationService));
    await view.goto();
    await use(view);
  },
});
