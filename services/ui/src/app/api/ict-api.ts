import { QueryClient } from "@tanstack/react-query";
import createFetch<PERSON>lient from "openapi-fetch";
import createClient, { type OpenapiQueryClient } from "openapi-react-query";
import { authMiddleware } from "./middleware/auth-middleware";
import { mockMiddleware } from "./middleware/mock-middleware";
import { tableauMiddleware } from "./middleware/tableau-middleware";
import { facilityMiddleware } from "./middleware/facility-middleware";
import type { paths } from "@ict/sdk/openapi-react-query";
import { tableNamesMiddleware } from "./middleware/table-names-middleware";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export class IctApi {
  public client: OpenapiQueryClient<paths>;
  public queryClient: QueryClient;
  public fetchClient;

  constructor(private readonly baseUrl: string) {
    const fetchClient = createFetchClient<paths>({
      baseUrl: this.baseUrl,
    });

    fetchClient.use(mockMiddleware);
    fetchClient.use(authMiddleware);
    fetchClient.use(facilityMiddleware);
    fetchClient.use(tableauMiddleware);
    fetchClient.use(tableNamesMiddleware);

    this.fetchClient = fetchClient;
    this.client = createClient(fetchClient);
    this.queryClient = new QueryClient();
  }
}

// Create a singleton instance
export const ictApi = new IctApi(API_BASE_URL);
