import type { MetricKpiMapDefinition } from "./kpi-resolver-types";
import type { MetricKpiInfo } from "./types";
import { TFunction } from "i18next";

// Create  typescript type for every "id" field string in the metricInfo array

export const metricInfo: (t: TFunction) => MetricKpiInfo[] = (t: TFunction) => [
  {
    id: "orders-shipped",
    label: t("Customer Orders Shipped"),
    description: "Orders shipped to customers",
  },
  {
    id: "facility-orders-shipped",
    label: t("Facility Orders Shipped"),
    description: "Orders shipped to customers",
  },
  {
    id: "orders-progress",
    label: t("Customer Order Progress", {
      defaultValue: "Customer Order Progress",
    }),
    description: "Order progress percentage",
  },
  {
    id: "facility-orders-progress",
    label: t("Facility Order Progress", {
      defaultValue: "Facility Order Progress",
    }),
    description: "Order progress percentage",
  },
  {
    id: "orders-throughput-rate",
    label: t("Throughput Rate", { defaultValue: "Throughput Rate" }),
    description: "Throughput rate of orders per hour",
    filters: ["area"],
  },
  {
    id: "facility-orders-throughput-rate",
    label: t("Facility Throughput Rate", {
      defaultValue: "Facility Throughput Rate",
    }),
    description: "Throughput rate of facility orders per hour",
    filters: ["area"],
  },
  {
    id: "orders-customer-line-throughput-rate",
    label: t("Customer Line Throughput Rate", {
      defaultValue: "Customer Line Throughput Rate",
    }),
    description: "Throughput rate of orders per hour",
    filters: ["area"],
  },
  {
    id: "orders-customer-cycle-time",
    label: t("Customer Cycle Time", { defaultValue: "Customer Cycle Time" }),
    description: "Cycle time of orders",
    filters: ["area"],
  },
  {
    id: "facility-orders-cycle-time",
    label: t("Facility Order Cycle Time", {
      defaultValue: "Facility Order Cycle Time",
    }),
    description: "Average facility order cycle time",
  },
  {
    id: "orders-customer-line-progress",
    label: t("Customer Line Progress", {
      defaultValue: "Customer Line Progress",
    }),
    description: "Progress of orders",
    filters: ["area"],
  },
  {
    id: "orders-cycle-time",
    label: t("Order Cycle Time", { defaultValue: "Order Cycle Time" }),
    description: "Average order cycle time",
    filters: ["area"],
  },
  {
    id: "operators-active",
    label: t("Active Operators", { defaultValue: "Active Operators" }),
    description: "Number of active operators",
  },
  {
    id: "orders-lines-progress",
    label: t("Order Lines Progress", { defaultValue: "Order Lines Progress" }),
    description: "Order lines progress percentage",
  },
  {
    id: "facility-orders-lines-progress",
    label: t("Facility Order Lines Progress", {
      defaultValue: "Facility Order Lines Progress",
    }),
    description: "Order lines progress percentage",
  },
  {
    id: "orders-fulfillment-outstanding",
    label: t("Orders Outstanding", {
      defaultValue: "Fulfillment Orders Outstanding",
    }),
    description: "Number of fulfillment orders outstanding",
  },
  {
    id: "orders-facility-outstanding",
    label: t("Facility Orders Outstanding", {
      defaultValue: "Facility Orders Outstanding",
    }),
    description: "Number of facility orders outstanding",
  },
  {
    id: "orders-projected-fulfillment",
    label: t("Projected Order Fulfillment", {
      defaultValue: "Projected Order Fulfillment",
    }),
    description: "Projected order fulfillment percentage",
  },
  {
    id: "orders-pick-cycle-counts",
    label: t("Cycle Counts", { defaultValue: "Cycle Counts" }),
    description: "Number of cycle counts",
  },
  {
    id: "inventory-storage-utilization",
    label: t("Storage Utilization", { defaultValue: "Storage Utilization" }),
    description: "Storage utilization percentage",
  },
  // inventory..
  {
    id: "inventory-accuracy",
    label: t("Inventory Accuracy", { defaultValue: "Inventory Accuracy" }),
    description: "Current inventory accuracy percentage",
  },
  {
    id: "inventory-advices-cycle-time",
    label: t("Advice Cycle Time", { defaultValue: "Advice Cycle Time" }),
    description: "Average cycle time for advices",
  },
  {
    id: "inventory-advices-finished",
    label: t("Finished Advices", { defaultValue: "Finished Advices" }),
    description: "Number of finished advices",
  },

  {
    id: "inventory-advices-outstanding",
    label: t("Outstanding Advices", { defaultValue: "Outstanding Advices" }),
    description: "Number of outstanding advices",
  },
  {
    id: "inventory-advices-in-progress",
    label: t("In Progress Advices", { defaultValue: "In Progress Advices" }),
    description: "Number of advices in progress",
  },
  // equipment..
  {
    id: "equipment-overall-outbound-rate",
    label: t("Overall Outbound Rate", {
      defaultValue: "Overall Outbound Rate",
    }),
    description: "Outbound diverts per hour",
  },
  {
    id: "orders-units-remaining",
    label: t("Units Remaining", { defaultValue: "Units Remaining" }),
    description: "Number of units remaining to be processed",
  },
  {
    id: "orders-estimated-completion",
    label: t("Estimated Completion", { defaultValue: "Estimated Completion" }),
    description: "Estimated time until completion",
  },
  {
    id: "orders-facility-estimated-completion",
    label: t("Facility Estimated Completion", {
      defaultValue: "Facility Estimated Completion",
    }),
    description: "Estimated time until completion",
  },
];

export const metricDefinitions: MetricKpiMapDefinition[] = [
  {
    id: "orders-shipped",
    endpoint: "/orders/customer/shipped",
    valueKeys: { current: "shipped", total: "total" },
    unit: "orders",
    renderType: "progress",
  },
  {
    id: "facility-orders-shipped",
    endpoint: "/orders/facility/shipped",
    valueKeys: { current: "shipped", total: "total" },
    unit: "orders",
    renderType: "progress",
  },
  {
    id: "orders-progress",
    endpoint: "/orders/customer/progress",
    valueKey: "orderProgressPercentage",
    unit: "percent",
  },
  {
    id: "facility-orders-progress",
    endpoint: "/orders/facility/progress",
    valueKey: "orderProgressPercentage",
    unit: "percent",
  },
  {
    id: "orders-throughput-rate",
    endpoint: "/orders/customer/throughput",
    valueKey: "throughputRateOrdersPerHour",
    unit: "orders/hr",
  },
  {
    id: "facility-orders-throughput-rate",
    endpoint: "/orders/facility/throughput",
    valueKey: "throughputRateOrdersPerHour",
    unit: "orders/hr",
  },
  {
    id: "orders-cycle-time",
    endpoint: "/orders/customer/cycletime",
    valueKey: "orderCycleTimeMinutes",
    unit: "minutes",
  },
  {
    id: "operators-active",
    endpoint: "/operators/active",
    valueKey: "operatorCount",
    unit: "number",
  },
  {
    id: "orders-lines-progress",
    endpoint: "/orders/lineprogress",
    valueKey: "lineProgressPercent",
    unit: "percent",
  },
  {
    id: "facility-orders-lines-progress",
    endpoint: "/orders/facility/line/progress",
    valueKey: "lineProgressPercent",
    unit: "percent",
  },
  {
    id: "orders-fulfillment-outstanding",
    endpoint: "/orders/fulfillment-outstanding",
    valueKey: "incompletedTotal",
    unit: "number",
  },
  {
    id: "orders-facility-outstanding",
    endpoint: "/orders/facility/outstanding",
    valueKey: "incompletedTotal",
    unit: "number",
  },
  {
    id: "orders-projected-fulfillment",
    endpoint: "/orders/fulfillment",
    valueKey: "projectedOrderFulfillmentPercentage",
    unit: "percent",
  },
  {
    id: "orders-pick-cycle-counts",
    endpoint: "/orders/pick/cycle-count",
    valueKey: "cycleCount",
    unit: "number",
  },
  {
    id: "inventory-storage-utilization",
    endpoint: "/inventory/storage/utilization",
    valueKey: "utilizationPercentage",
    unit: "percent",
  },
  {
    id: "inventory-accuracy",
    endpoint: "/inventory/accuracy",
    valueKey: "accuracy",
    unit: "percent",
  },
  {
    id: "orders-performance-fulfillment-rate",
    endpoint: "/orders/performance/fulfillment",
    valueKey: "orderFulfillment",
    unit: "percent",
  },
  {
    id: "inventory-advices-finished",
    endpoint: "/inventory/advices/finished",
    valueKey: "finishedAdvices",
    unit: "number",
  },
  {
    id: "inventory-advices-outstanding",
    endpoint: "/inventory/advices/outstanding",
    valueKey: "outstandingAdvices",
    unit: "number",
  },
  {
    id: "inventory-advices-in-progress",
    endpoint: "/inventory/advices/in-progress",
    valueKey: "inProgressAdvices",
    unit: "number",
  },
  {
    id: "inventory-advices-cycle-time",
    endpoint: "/inventory/advices/cycle-time",
    valueKey: "cycleTime",
    unit: "number",
  },

  {
    id: "equipment-overall-outbound-rate",
    endpoint: "/equipment/outbound-rate",
    valueKey: "outboundDivertsHour",
    unit: "number",
  },
  {
    id: "orders-units-remaining",
    endpoint: "/orders/remaining",
    valueKey: "unitsRemaining",
    unit: "number",
  },
  {
    id: "orders-estimated-completion",
    endpoint: "/orders/customer/completion",
    valueKey: "estimatedCompletionMinutes",
    unit: "minutes",
  },
  {
    id: "orders-facility-estimated-completion",
    endpoint: "/orders/facility/completion",
    valueKey: "estimatedCompletionMinutes",
    unit: "minutes",
  },
  {
    id: "orders-customer-line-throughput-rate",
    endpoint: "/orders/customer/line/throughput",
    valueKey: "throughputRateOrderLinesPerHour",
    unit: "lines/hr",
  },
  {
    id: "orders-customer-cycle-time",
    endpoint: "/orders/customer/cycletime",
    valueKey: "orderCycleTimeMinutes",
    unit: "minutes",
  },
  {
    id: "facility-orders-cycle-time",
    endpoint: "/orders/facility/cycletime",
    valueKey: "orderCycleTimeMinutes",
    unit: "minutes",
  },
  {
    id: "orders-customer-line-progress",
    endpoint: "/orders/customer/line/progress",
    valueKey: "lineProgressPercent",
    unit: "percent",
  },
];
