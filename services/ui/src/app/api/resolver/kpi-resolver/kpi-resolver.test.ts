import { vi } from "vitest";
import i18n from "../../../../test-utils/i18n-testing";
import { ictApi } from "../../ict-api";
import { MetricKpiResolver } from "./kpi-resolver";
import { metricDefinitions, metricInfo } from "./kpi-resolver-data";
// import i18n from "src/test-utils/i18n-testing";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../../config/i18n/i18n", () => ({
  __esModule: true,
  default: { t: (key: string, options?: any) => options?.defaultValue || key },
  i18nInitPromise: Promise.resolve(),
}));

// Mock the ictApi
vi.mock("../../ict-api", () => ({
  ictApi: {
    client: {
      queryOptions: vi.fn().mockReturnValue({
        queryKey: ["test-key"],
      }),
    },
    queryClient: {
      fetchQuery: vi.fn(),
    },
  },
}));

// Mock the Logger and date utilities
vi.mock("../../../utils", () => ({
  Logger: class {
    info = vi.fn();
    warn = vi.fn();
  },
}));

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

describe("MetricKpiResolver", () => {
  let metricResolver: MetricKpiResolver;

  beforeEach(() => {
    vi.clearAllMocks();
    metricResolver = new MetricKpiResolver();
  });

  describe("getMetric", () => {
    it("should fetch standard metric data successfully", async () => {
      // Find a standard metric (non-progress type)
      const standardMetric = metricDefinitions.find(
        (def) => !("valueKeys" in def) && "valueKey" in def,
      );

      if (!standardMetric || !("valueKey" in standardMetric)) {
        throw new Error("No standard metric found in definitions for testing");
      }

      // Setup mock response with the correct value key
      const mockResponse = {
        [standardMetric.valueKey]: 42,
      };

      (ictApi.queryClient.fetchQuery as any).mockResolvedValue(mockResponse);

      const filters = {
        datePeriodRange: "last30days",
      };

      // Execute
      const result = await metricResolver.getMetric(
        standardMetric.id as any,
        filters as any,
        startDate,
        endDate,
      );

      // Verify
      expect(result.result).toBe("200");
      expect(result.id).toBe(standardMetric.id);

      // Type guard to check if we have a data response
      if (result.result === "200") {
        expect(result.value).toBe(42);
        expect(result.unit).toBe(standardMetric.unit);
      } else {
        expect.fail("Expected a data response but got a no-data response");
      }

      expect(ictApi.client.queryOptions).toHaveBeenCalled();
      expect(ictApi.queryClient.fetchQuery).toHaveBeenCalled();
    });

    it("should fetch progress metric data successfully", async () => {
      // Find a progress metric type
      const progressMetric = metricDefinitions.find(
        (def) => "valueKeys" in def,
      );

      if (!progressMetric || !("valueKeys" in progressMetric)) {
        throw new Error("No progress metric found in definitions for testing");
      }

      // Setup mock response with current and total values
      const mockResponse = {
        [progressMetric.valueKeys.current]: 75,
        [progressMetric.valueKeys.total]: 100,
      };

      (ictApi.queryClient.fetchQuery as any).mockResolvedValue(mockResponse);

      const filters = {
        datePeriodRange: {
          startDate: new Date("2023-01-01"),
          endDate: new Date("2023-01-31"),
        },
      };

      // Execute
      const result = await metricResolver.getMetric(
        progressMetric.id as any,
        filters as any,
        startDate,
        endDate,
      );

      // Verify
      expect(result.result).toBe("200");
      expect(result.id).toBe(progressMetric.id);

      // Type guard to check if we have a data response
      if (result.result === "200") {
        expect(result.value).toEqual({
          current: 75,
          total: 100,
        });
        expect(result.unit).toBe(progressMetric.unit);
      } else {
        expect.fail("Expected a data response but got a no-data response");
      }
    });

    it("should handle empty response", async () => {
      // Setup
      (ictApi.queryClient.fetchQuery as any).mockResolvedValue(null);

      const filters = {
        datePeriodRange: "last30days",
      };

      // Execute
      const result = await metricResolver.getMetric(
        metricDefinitions[0].id as any,
        filters as any,
        startDate,
        endDate,
      );

      // Verify
      expect(result.result).toBe("204");
      expect(result.id).toBe(metricDefinitions[0].id);

      // For a 204 response, value should not exist
      if (result.result === "204") {
        expect(result).not.toHaveProperty("value");
      } else {
        expect.fail("Expected a no-data response but got a data response");
      }
    });
  });

  describe("getMetricInfo", () => {
    it("should return metric info", async () => {
      const t = i18n.t;
      const result = await metricResolver.getMetricInfo();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toEqual(metricInfo(t));
    });
  });
});
