import {
  BatteryCharging,
  ChartBar,
  CloudMonitoring,
  ColumnDependency,
  Dashboard,
  DashboardReference,
  Enterprise,
  Flow,
  Information,
  Keyboard,
  Notification,
  Search,
  Settings,
  User,
  Password,
  Rule,
  SearchLocate,
  Star,
  Template,
  Analytics,
  TableOfContents,
  IbmToolchain,
  IbmWatsonDiscovery,
  SecurityServices,
} from "@carbon/icons-react";
import { CarbonIconType } from "@carbon/icons-react";

export type AvailableIcon = {
  icon: CarbonIconType;
  name: string;
};

export const AvailableIcons: AvailableIcon[] = [
  {
    icon: IbmToolchain,
    name: "IbmToolchain",
  },
  {
    icon: TableOfContents,
    name: "TableOfContents",
  },
  {
    icon: ChartBar,
    name: "ChartBar",
  },
  {
    icon: CloudMonitoring,
    name: "CloudMonitoring",
  },
  {
    icon: Dashboard,
    name: "Dashboard",
  },
  {
    icon: Enterprise,
    name: "Enterprise",
  },
  {
    icon: Flow,
    name: "Flow",
  },
  {
    icon: IbmWatsonDiscovery,
    name: "IbmWatsonDiscovery",
  },
  {
    icon: Information,
    name: "Information",
  },
  {
    icon: Notification,
    name: "Notification",
  },
  {
    icon: Search,
    name: "Search",
  },
  {
    icon: Settings,
    name: "Settings",
  },
  {
    icon: User,
    name: "User",
  },
  {
    icon: BatteryCharging,
    name: "BatteryCharging",
  },
  {
    icon: ColumnDependency,
    name: "ColumnDependency",
  },
  {
    icon: Analytics,
    name: "Analytics",
  },
  {
    icon: Template,
    name: "Template",
  },
  {
    icon: Keyboard,
    name: "Keyboard",
  },
  {
    icon: SearchLocate,
    name: "SearchLocate",
  },
  {
    icon: Rule,
    name: "Rule",
  },
  {
    icon: Password,
    name: "Password",
  },
  {
    icon: DashboardReference,
    name: "DashboardReference",
  },
  {
    icon: Star,
    name: "Star",
  },
  {
    icon: SecurityServices,
    name: "SecurityServices",
  },
] as const;

const CARBON_ICONS = {
  Enterprise,
  Dashboard,
  DashboardReference,
  ChartBar,
  Analytics,
  Flow,
  IbmWatsonDiscovery,
  IbmToolchain,
  Information,
  Notification,
  ColumnDependency,
  CloudMonitoring,
  BatteryCharging,
  Template,
  TableOfContents,
  Keyboard,
  SearchLocate,
  Rule,
  Password,
  star: Star,
  SecurityServices,
} as const;

export function iconLookup(iconName: string) {
  const IconComponent = CARBON_ICONS[iconName as keyof typeof CARBON_ICONS];
  return IconComponent ? <IconComponent size={16} /> : null;
}
