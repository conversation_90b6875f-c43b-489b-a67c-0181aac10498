.viewAside {
  width: 320px;
  height: 100%;
  background-color: var(--cds-layer);
  border-left: 1px solid var(--cds-border-subtle);
  overflow-y: auto;
  z-index: 500;
  height: calc(100vh - var(--header-height, 48px));
}

.animateWidthTransition {
  transition: width 0.3s ease;
}

.visible {
  transform: translateX(0);
}

.hidden {
  transform: translateX(100%);
}

.content {
  height: 100%;
}

.resizeHandle {
  position: absolute;
  left: 0;
  top: 0;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.resizeHandle:hover {
  background-color: var(--cds-border-interactive, #0f62fe);
}

.resizeHandle:active {
  background-color: var(--cds-border-interactive, #0f62fe);
}
