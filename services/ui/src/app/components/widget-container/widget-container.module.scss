.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border: 1px solid var(--cds-border-subtle);
  overflow: hidden;
}

.title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1.25rem;
}

.widget {
  height: 100%;
}

.center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.notification {
  width: 100%;
  min-width: 0;
  max-width: 100%;
}

.loader {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  :global(.cds--loading--small) {
    width: 2.4rem !important;
    height: 2.4rem !important;
  }
}
