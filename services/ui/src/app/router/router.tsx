import { Suspense } from "react";
import { Route, Routes } from "react-router";
import App from "../app";
import { AuthGuard } from "../auth/auth-guard";
import { LoadingView } from "../components/loading-view/loading-view";
import { mfeConfigs } from "../mfe/mfe-config";
import { MfeLoader } from "../mfe/mfe-loader";
import { isAppMfeConfig } from "../mfe/types";
import { NotFound } from "../views/not-found/not-found";
import Tableau from "../views/tableau/tableau";
import ContainerDetailView from "../views/container-details/container-details-view";
import { View } from "../views/view";
import { DynamicRouteView } from "./components/dynamic-route-view";
import { TABLEAU_MENU_MAIN_PATH_SEGMENT } from "../config/menu/transformers/tableau-transformer";

/**
 * Renders routes for all micro-frontend applications
 * @returns React elements for MFE routes
 */
function renderMfeRoutes() {
  const apps = mfeConfigs.filter(isAppMfeConfig);
  return (
    <>
      {apps.map((app) => (
        <Route
          key={app.name}
          path={`${app.path?.replace("/", "")}/*`}
          element={<MfeLoader path={app.path} />}
        />
      ))}
    </>
  );
}

/**
 * Renders dynamic routes that are resolved at runtime
 * @returns React elements for dynamic routes
 */
function renderDynamicRoutes() {
  return (
    <>
      <Route element={<View />}>
        <Route
          path=":path/*"
          element={
            <Suspense fallback={<LoadingView />}>
              <DynamicRouteView />
            </Suspense>
          }
        />
      </Route>
    </>
  );
}

/**
 * Main routing component for the application
 * Handles authentication, default routes, micro-frontend routes, and dynamic routes
 */
export const RootRoutes = function RootRoutes() {
  return (
    <Routes>
      <Route element={<AuthGuard />}>
        <Route element={<App />}>
          <Route path="/" element={<LoadingView />} />
          {renderMfeRoutes()}
          {renderDynamicRoutes()}
          <Route path="*" element={<NotFound />} />

          {/* 
           Tableau is a special case - we don't have this specific route in our menu.json because
           Tableau views are dynamically added to the route tree based on the user's permissions.
           */}
          <Route path={TABLEAU_MENU_MAIN_PATH_SEGMENT} element={<Tableau />} />

          {/* 
           Container List is a special case, we need to route to a view for container details
           */}
          <Route
            path="ict-container-list/container/*"
            element={<ContainerDetailView id="container-detail" options={{}} />}
          />
        </Route>
      </Route>
    </Routes>
  );
};
