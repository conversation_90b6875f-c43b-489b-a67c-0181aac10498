import { ViewBar } from "../../components/view-bar/view-bar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tab, <PERSON>bPane<PERSON>, TabPanel, Button } from "@carbon/react";
import { useLocation, useNavigate } from "react-router";
import { useMemo, useState } from "react";
import { Datagrid } from "../../components/datagrid/datagrid";
import styles from "./fault-tracking.module.css";

function ManualEntryForm({ onCancel }: { onCancel: () => void }) {
  return (
    <div>
      <p>Manual Entry Form</p>
      <br />
      <Button onClick={onCancel}>Cancel</Button>
    </div>
  );
}

export default function FaultTrackingView() {
  const location = useLocation();
  const navigate = useNavigate();

  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // Added tableKey state to force rerender
  const [tableKey, setTableKey] = useState(0);

  const [isAddingManualEntry, setIsAddingManualEntry] = useState(false);

  const tabs = [
    { route: "ict-system-availability", label: "System Availability" },
    { route: "ict-equipment-hierarchy", label: "Equipment Hierarchy" },
    { route: "ict-fault-tracking", label: "Fault Tracking" },
  ];

  const selectedIndex = useMemo(() => {
    return tabs.findIndex((tab) => location.pathname.endsWith(tab.route));
  }, [location.pathname]);

  function handleTabChange(state: { selectedIndex: number }) {
    navigate(`/${tabs[state.selectedIndex].route}`);
  }

  // Table columns
  const columns = [
    { accessorKey: "section", header: "Section" },
    { accessorKey: "unit", header: "Unit" },
    { accessorKey: "controllerId", header: "Controller ID" },
    { accessorKey: "faultId", header: "Fault ID" },
    { accessorKey: "faultDescription", header: "Fault Description" },
    { accessorKey: "faultDuration", header: "Fault Duration" },
    { accessorKey: "weightedDowntime", header: "Weighted Downtime" },
    { accessorKey: "kiFactor", header: "Ki Factor" },
    { accessorKey: "calculationStatus", header: "Calculation Status" },
    { accessorKey: "removalReason", header: "Removal Reason" },
    { accessorKey: "subsystem", header: "Subsystem" },
    { accessorKey: "faultCode", header: "Fault Code" },
  ];

  const mockedData = [
    {
      section: "A",
      unit: "Unit 1",
      controllerId: "C-001",
      faultId: "F-1001",
      faultDescription: "Overheat detected",
      faultDuration: "5m",
      weightedDowntime: "4.5m",
      kiFactor: "1.2",
      calculationStatus: "Complete",
      removalReason: "Manual reset",
      subsystem: "Cooling",
      faultCode: "E01",
    },
    {
      section: "B",
      unit: "Unit 2",
      controllerId: "C-002",
      faultId: "F-1002",
      faultDescription: "Sensor failure",
      faultDuration: "10m",
      weightedDowntime: "9.8m",
      kiFactor: "1.1",
      calculationStatus: "Pending",
      removalReason: "Replaced sensor",
      subsystem: "Sensors",
      faultCode: "E02",
    },
  ];

  function handleRefresh() {
    setTableKey((prev) => prev + 1);
  }

  function handleAddManualEntry() {
    setIsAddingManualEntry(true);
  }

  function handleCancelManualEntry() {
    setIsAddingManualEntry(false);
  }

  function handleUpdateCalculation() {
    alert("Update Calculation clicked");
  }

  function renderTabs() {
    return tabs.map((tab, idx) => <Tab key={tabs[idx].route}>{tab.label}</Tab>);
  }

  return (
    <>
      <Tabs selectedIndex={selectedIndex} onChange={handleTabChange}>
        <div className={styles.tabsContainer}>
          <ViewBar title="Availability" isExtended>
            <TabList aria-label="Fault Tracking Tabs">{renderTabs()}</TabList>
          </ViewBar>
        </div>
        <div className={styles.tabsContent}>
          <TabPanels>
            <TabPanel>System Availability content goes here.</TabPanel>
            <TabPanel>Equipment Hierarchy content goes here.</TabPanel>
            <TabPanel>
              {isAddingManualEntry ? (
                <ManualEntryForm onCancel={handleCancelManualEntry} />
              ) : (
                <Datagrid
                  key={tableKey}
                  columns={columns}
                  data={mockedData}
                  enableSelection={true}
                  rowSelection={rowSelection}
                  onRowSelectionChange={setRowSelection}
                  showRefreshButton={true}
                  onRefreshClick={handleRefresh}
                  onAddManualEntry={handleAddManualEntry}
                  onUpdateCalculation={handleUpdateCalculation}
                />
              )}
            </TabPanel>
          </TabPanels>
        </div>
      </Tabs>
    </>
  );
}
