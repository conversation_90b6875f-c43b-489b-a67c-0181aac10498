import { Link } from "@carbon/react";
import type {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import type { InventoryItem } from "../../types";
import { useTranslation } from "react-i18next";
import { useConfigSetting } from "../../../../config/hooks/use-config";

interface InventoryListTableProps {
  data: InventoryItem[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: unknown;
  rowCount: number;
  onExport: () => void;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
}

export function InventoryListTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  //columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onExport,
  onRefresh,
  setGlobalFilter,
}: InventoryListTableProps) {
  const navigate = useNavigate();
  const { setting: columnOrderSetting } = useConfigSetting(
    "inventory-sku-list-column-order",
  );
  const [columns, setColumns] = useState<ColumnDef<InventoryItem>[]>([]);

  // Handle SKU click to navigate to container list with SKU filter
  const handleSkuClick = useCallback(
    (sku: string) => {
      navigate(`/ict-container-list?sku=${encodeURIComponent(sku)}`);
    },
    [navigate],
  );

  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<InventoryItem>();
  const { t } = useTranslation();

  const columnDefinitions = [
    columnHelper.accessor("sku", {
      header: t("inventoryListTable.sku", "SKU"),
      size: 150,
      cell: (info) => (
        <Link
          onClick={(e) => {
            e.stopPropagation();
            handleSkuClick(info.getValue());
          }}
          style={{ cursor: "pointer" }}
        >
          {info.getValue()}
        </Link>
      ),
    }),
    columnHelper.accessor("quantityAvailable", {
      header: t("inventoryListTable.qtyAvailable", "Qty Available"),
      size: 120,
    }),
    columnHelper.accessor("quantityAllocated", {
      header: t("inventoryListTable.qtyAllocated", "Qty Allocated"),
      size: 120,
    }),
    columnHelper.accessor("maxContainers", {
      header: t("inventoryListTable.maxContainers", "Max Containers"),
      size: 120,
    }),
    columnHelper.accessor("skuPositions", {
      header: t("inventoryListTable.skuPositions", "SKU Positions"),
      size: 120,
    }),
    columnHelper.accessor("contOverage", {
      header: t("inventoryListTable.contOverage", "Overage"),
      size: 100,
    }),
    columnHelper.accessor("daysOnHand", {
      header: t("inventoryListTable.daysOnHand", "Days On Hand"),
      size: 120,
    }),
    columnHelper.accessor("averageDailyQuantity", {
      header: t("inventoryListTable.avgDailyQty", "Avg Daily Qty"),
      size: 120,
    }),
    columnHelper.accessor("averageDailyOrders", {
      header: t("inventoryListTable.avgDailyOrders", "Avg Daily Orders"),
      size: 140,
    }),
    columnHelper.accessor("latestActivityDateTimestamp", {
      header: t("inventoryListTable.latestActivity", "Latest Activity"),
      size: 180,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "";
      },
    }),
    columnHelper.accessor("latestCycleCountTimestamp", {
      header: t("inventoryListTable.latestCycleCount", "Latest Cycle Count"),
      size: 180,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "";
      },
    }),
    columnHelper.accessor("description", {
      header: t("inventoryListTable.description", "Description"),
      size: 200,
    }),
    columnHelper.accessor("targetMultiplicity", {
      header: t("inventoryListTable.targetMultiplicity", "Target Multiplicity"),
      size: 150,
    }),
    columnHelper.accessor("velocityClassification", {
      header: t(
        "inventoryListTable.velocityClassification",
        "Velocity Classification",
      ),
      size: 180,
    }),
  ];

  useEffect(() => {
    if (!columnOrderSetting || !columnOrderSetting.value) {
      setColumns(columnDefinitions as ColumnDef<InventoryItem>[]);
    } else {
      const orderedColumns: ColumnDef<InventoryItem>[] = [];

      (columnOrderSetting.value as string[]).forEach((c) => {
        // Find the column definition for the column key
        const columnDefinition = columnDefinitions.find(
          (col) => col.accessorKey === c,
        );

        if (columnDefinition)
          orderedColumns.push(columnDefinition as ColumnDef<InventoryItem>);
      });
      setColumns(orderedColumns);
    }
  }, [columnOrderSetting]);

  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));

      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      onExport={onExport}
      onRefreshClick={onRefresh}
      showExportButton={true}
      showRefreshButton={!!onRefresh}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
    />
  );
}
