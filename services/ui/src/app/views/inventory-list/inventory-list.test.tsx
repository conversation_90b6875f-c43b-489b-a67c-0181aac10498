import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { ictApi } from "../../api/ict-api";
import { InventoryList } from "./inventory-list";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the useConfig hook
vi.mock("../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({ setting: { value: "America/New_York" } })),
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      <div data-testid="view-bar-children">{children}</div>
    </div>
  ),
}));

// Mock the InventoryListTable component
vi.mock("./components/inventory-list-table/inventory-list-table", () => ({
  InventoryListTable: ({
    data,
    pagination,
    setPagination,
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    isLoading,
    isFetching,
    error,
    rowCount,
    onExport,
  }: any) => (
    <div data-testid="inventory-list-table">
      <div data-testid="table-data">{JSON.stringify(data)}</div>
      <div data-testid="table-pagination">{JSON.stringify(pagination)}</div>
      <div data-testid="table-sorting">{JSON.stringify(sorting)}</div>
      <div data-testid="table-filters">{JSON.stringify(columnFilters)}</div>
      <div data-testid="table-loading">{String(isLoading)}</div>
      <div data-testid="table-fetching">{String(isFetching)}</div>
      <div data-testid="table-error">{error ? String(error) : "null"}</div>
      <div data-testid="table-row-count">{rowCount}</div>
      <button data-testid="export-button" type="button" onClick={onExport}>
        Export
      </button>
      <button
        data-testid="change-pagination"
        type="button"
        onClick={() => setPagination({ pageIndex: 1, pageSize: 25 })}
      >
        Change Page
      </button>
      <button
        data-testid="change-sorting"
        type="button"
        onClick={() => setSorting([{ id: "sku", desc: true }])}
      >
        Change Sorting
      </button>
      <button
        data-testid="change-filters"
        type="button"
        onClick={() => setColumnFilters([{ id: "status", value: "Active" }])}
      >
        Change Filters
      </button>
    </div>
  ),
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    Modal: ({
      children,
      open,
      modalHeading,
      primaryButtonText,
      secondaryButtonText,
      onRequestSubmit,
      onRequestClose,
      primaryButtonDisabled,
      ..._props
    }: any) =>
      open ? (
        <div data-testid="modal" {..._props}>
          <h2>{modalHeading}</h2>
          {children}
          <div className="modal-buttons">
            <button
              onClick={onRequestSubmit}
              disabled={primaryButtonDisabled}
              data-testid="modal-primary-button"
            >
              {primaryButtonText}
            </button>
            <button
              onClick={onRequestClose}
              data-testid="modal-secondary-button"
            >
              {secondaryButtonText}
            </button>
          </div>
        </div>
      ) : null,
  };
});

describe("InventoryList", () => {
  const mockUseQuery = ictApi.client.useQuery as any;

  const mockApiResponse = {
    data: [
      {
        sku: "ABC123",
        description: "Test Item 1",
        daysOnHand: 10,
        averageDailyQuantity: 5,
        averageDailyOrders: 2,
        totalQuantity: 100,
        quantityAvailable: 80,
        locations: 3,
        status: "Active",
        latestInventorySnapshotTimestamp: "2023-05-01T12:00:00Z",
        latestActivityDateTimestamp: "2023-05-02T14:30:00Z",
      },
    ],
    metadata: {
      totalResults: 100,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementation
    mockUseQuery.mockReturnValue({
      data: mockApiResponse,
      dataUpdatedAt: new Date().getTime(),
      error: null,
      isLoading: false,
      isFetching: false,
    });
  });

  it("should render the view bar with correct title", () => {
    render(<InventoryList />);

    const viewBar = screen.getByTestId("view-bar");
    expect(viewBar).toBeInTheDocument();
    expect(screen.getByText("Inventory List")).toBeInTheDocument();
  });

  it("should render the inventory list table with correct props", () => {
    render(<InventoryList />);

    const table = screen.getByTestId("inventory-list-table");
    expect(table).toBeInTheDocument();

    // Check data is passed correctly
    const tableData = screen.getByTestId("table-data");
    const parsedData = JSON.parse(tableData.textContent || "[]");
    expect(parsedData).toEqual(mockApiResponse.data);

    // Check pagination is initialized correctly
    const tablePagination = screen.getByTestId("table-pagination");
    const parsedPagination = JSON.parse(tablePagination.textContent || "{}");
    expect(parsedPagination).toEqual({ pageIndex: 0, pageSize: 50 });

    // Check sorting is initialized correctly
    const tableSorting = screen.getByTestId("table-sorting");
    const parsedSorting = JSON.parse(tableSorting.textContent || "[]");
    expect(parsedSorting).toEqual([{ id: "daysOnHand", desc: false }]);

    // Check row count is passed correctly
    const tableRowCount = screen.getByTestId("table-row-count");
    expect(tableRowCount.textContent).toBe("100");
  });

  it("should display loading state correctly", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      error: null,
      isLoading: true,
      isFetching: false,
    });

    render(<InventoryList />);

    const tableLoading = screen.getByTestId("table-loading");
    expect(tableLoading.textContent).toBe("true");

    // Last updated should show loading
    expect(
      screen.getByText("Inventory Data Updated: Loading..."),
    ).toBeInTheDocument();
  });

  it("should display error state correctly", () => {
    const testError = new Error("Test error");
    mockUseQuery.mockReturnValue({
      data: null,
      error: testError,
      isLoading: false,
      isFetching: false,
    });

    render(<InventoryList />);

    const tableError = screen.getByTestId("table-error");
    expect(tableError.textContent).toBe("Error fetching data");
  });

  it("should update pagination when table triggers change", async () => {
    render(<InventoryList />);

    // Trigger pagination change from the table
    fireEvent.click(screen.getByTestId("change-pagination"));

    // Check that the API was called with updated pagination
    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/inventory/skus/list",
        expect.objectContaining({
          body: expect.objectContaining({
            page: 1,
            limit: 25,
          }),
        }),
        expect.anything(),
      );
    });
  });

  it("should update sorting when table triggers change", async () => {
    render(<InventoryList />);

    // Trigger sorting change from the table
    fireEvent.click(screen.getByTestId("change-sorting"));

    // Check that the API was called with updated sorting
    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/inventory/skus/list",
        expect.objectContaining({
          body: expect.objectContaining({
            sortFields: [{ columnName: "sku", isDescending: true }],
          }),
        }),
        expect.anything(),
      );
    });
  });

  it("should update filters when table triggers change", async () => {
    render(<InventoryList />);

    // Trigger filter change from the table
    fireEvent.click(screen.getByTestId("change-filters"));

    // Check that the API was called with updated filters
    // Note: The exact structure depends on transformFilters implementation
    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/inventory/skus/list",
        expect.objectContaining({
          body: expect.objectContaining({
            filters: expect.anything(),
          }),
        }),
        expect.anything(),
      );
    });
  });

  it("should format the last updated timestamp correctly", () => {
    mockUseQuery.mockReturnValue({
      data: mockApiResponse,
      error: null,
      isLoading: false,
      isFetching: false,
    });

    render(<InventoryList />);

    const expectedFormattedDate = "2023-05-01 8:00:00 AM";
    expect(
      screen.getByText(`Inventory Data Updated: ${expectedFormattedDate}`),
    ).toBeInTheDocument();
  });

  it("should handle export button click", () => {
    render(<InventoryList />);

    fireEvent.click(screen.getByTestId("export-button"));

    // Currently the export function is a TODO, so we're just testing that it doesn't crash
    // When implemented, we could test the actual export functionality
  });
});
