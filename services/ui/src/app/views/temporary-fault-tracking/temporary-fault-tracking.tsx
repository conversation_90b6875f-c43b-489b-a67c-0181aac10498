import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ViewBar } from "../../components/view-bar/view-bar";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import type { FaultAlarm, SortField } from "../fault-tracking/types";
import { TemporaryFaultTrackingTable } from "./components/temporary-fault-tracking-table/temporary-fault-tracking-table";

export default function TemporaryFaultTrackingView() {
  const { t } = useTranslation();

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    { id: "timing.startTime", desc: true },
  ]);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");

  const [dateRange] = useState<{ start_date: Date; end_date: Date }>(() => ({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    end_date: new Date(),
  }));

  const sortFields: SortField[] = useMemo(() => {
    return sorting.map((sort) => {
      let columnName = sort.id;
      if (sort.id === "timing.startTime" || sort.id === "timing_startTime") {
        columnName = "startTime";
      } else if (sort.id === "timing.endTime" || sort.id === "timing_endTime") {
        columnName = "endTime";
      } else if (sort.id === "location.area" || sort.id === "location_area") {
        columnName = "area";
      } else if (
        sort.id === "location.section" ||
        sort.id === "location_section"
      ) {
        columnName = "section";
      } else if (
        sort.id === "location.equipment" ||
        sort.id === "location_equipment"
      ) {
        columnName = "equipment";
      } else if (
        sort.id === "timing.duration" ||
        sort.id === "timing_duration"
      ) {
        // API exposes duration as a flat field name
        columnName = "duration";
      } else if (sort.id === "id") {
        // Backend expects alarm id as faultId
        columnName = "faultId";
      }
      return {
        columnName,
        isDescending: sort.desc,
      };
    });
  }, [sorting]);

  // Map UI column filter ids to API field names
  const mappedColumnFilters = useMemo(() => {
    return columnFilters.map((filter) => {
      let id = filter.id;
      if (id === "timing.startTime" || id === "timing_startTime") {
        id = "startTime";
      } else if (id === "timing.endTime" || id === "timing_endTime") {
        id = "endTime";
      } else if (id === "timing.duration" || id === "timing_duration") {
        id = "duration";
      } else if (id === "location.area" || id === "location_area") {
        id = "area";
      } else if (id === "location.section" || id === "location_section") {
        id = "section";
      } else if (id === "location.equipment" || id === "location_equipment") {
        id = "equipment";
      } else if (id === "id") {
        id = "faultId";
      }
      return { ...filter, id };
    });
  }, [columnFilters]);

  const apiFilters = useMemo(
    () => transformFilters(mappedColumnFilters),
    [mappedColumnFilters],
  );

  const requestBody = useMemo(
    () => ({
      start_date: dateRange.start_date.toISOString(),
      end_date: dateRange.end_date.toISOString(),
      limit: pagination.pageSize,
      offset: pagination.pageIndex * pagination.pageSize,
      filters: apiFilters,
      sortFields,
      ...(globalFilter !== "" && { searchString: globalFilter }),
    }),
    [
      dateRange.start_date.getTime(),
      dateRange.end_date.getTime(),
      pagination.pageIndex,
      pagination.pageSize,
      apiFilters,
      sortFields,
      globalFilter,
    ],
  );

  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/availability/alarms/list",
      { body: requestBody },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const alarmData = useMemo(() => {
    return (data?.data ?? []) as unknown as FaultAlarm[];
  }, [data]);

  return (
    <div
      data-testid="temporary-fault-tracking"
      style={{ flex: 1, width: "100%" }}
    >
      <ViewBar title={t("faultTracking.title", "Fault Tracking")} />
      <FullPageContainer>
        <TemporaryFaultTrackingTable
          data={alarmData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={error}
          rowCount={data?.metadata.totalResults ?? 0}
          onRefresh={refetch}
          setGlobalFilter={setGlobalFilter}
        />
      </FullPageContainer>
    </div>
  );
}
