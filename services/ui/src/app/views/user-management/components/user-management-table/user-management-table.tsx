import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import type { UserInfo } from "../../types";

interface UserManagementTableProps {
  data: UserInfo[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: unknown;
  rowCount: number;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
}

export function UserManagementTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onRefresh,
  setGlobalFilter,
}: UserManagementTableProps) {
  const { t } = useTranslation();

  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<UserInfo>();

  const columns = [
    columnHelper.accessor("email", {
      header: t("userManagementTable.email", "Email"),
      size: 250,
      cell: (info) => {
        const email = info.getValue();
        const user = info.row.original;
        const userId = user.id;

        return (
          <Link
            to={`/ict-user-management/${userId}`}
            style={{
              color: "var(--cds-link-primary)",
              textDecoration: "underline",
            }}
          >
            {email}
          </Link>
        );
      },
    }),
    columnHelper.accessor("name", {
      header: t("userManagementTable.name", "Name"),
      size: 200,
      cell: (info) => info.getValue() || "-",
    }),
    columnHelper.accessor("emailVerified", {
      header: t("userManagementTable.emailVerified", "Email Verified"),
      size: 140,
      cell: (info) => (info.getValue() ? "Yes" : "No"),
    }),
    columnHelper.accessor("createdAt", {
      header: t("userManagementTable.createdAt", "Created At"),
      size: 180,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "";
      },
    }),
    columnHelper.accessor("lastLogin", {
      header: t("userManagementTable.lastLogin", "Last Login"),
      size: 180,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "Never";
      },
    }),
    columnHelper.accessor("loginCount", {
      header: t("userManagementTable.loginCount", "Login Count"),
      size: 120,
    }),
    columnHelper.accessor("isSocialAuth", {
      header: t("userManagementTable.isSocialAuth", "Social Auth"),
      size: 120,
      cell: (info) => (info.getValue() ? "Yes" : "No"),
    }),
  ];

  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Only search if our filter is > 3 characters because Auth0 user
      // serach does not support less than 3 characters
      if (newFilters.globalFilter.length < 3) {
        return;
      }

      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));

      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      onRefreshClick={onRefresh}
      showExportButton={false}
      showRefreshButton={!!onRefresh}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
    />
  );
}
