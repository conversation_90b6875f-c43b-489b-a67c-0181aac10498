import { lazy } from "react";

export const viewsConfig = [
  {
    id: "dashboard",
    component: lazy(() => import("./dashboard/dashboard")),
  },
  {
    id: "curated-data",
    component: lazy(() => import("./curated-data/curated-data")),
  },
  {
    id: "workstation-overview",
    component: lazy(
      () => import("./workstation-order-status/workstation-view"),
    ),
  },
  {
    id: "workstation-view",
    component: lazy(
      () => import("./workstation-order-status/workstation-view"),
    ),
  },
  {
    id: "daily-performance-report",
    component: lazy(
      () => import("./daily-performance-report/daily-performance-report"),
    ),
  },
  {
    id: "data-explorer",
    component: lazy(() => import("./data-explorer/data-explorer")),
  },
  {
    id: "dematic-chat",
    component: lazy(() => import("./dematic-chat/dematic-chat")),
  },
  {
    id: "debug-info",
    component: lazy(() => import("./debug-info/debug-info")),
  },
  {
    id: "feature-flags",
    component: lazy(() => import("./feature-flags/feature-flags")),
  },
  {
    id: "app-config-settings",
    component: lazy(() => import("./app-config-settings/app-config-settings")),
  },
  {
    id: "menu-manager",
    component: lazy(() => import("./menu-manager/menu-manager")),
  },
  {
    id: "mfe-manager",
    component: lazy(() => import("./mfe-manager/mfe-manager")),
  },
  {
    id: "file-upload",
    component: lazy(() => import("./file-upload/file-upload")),
  },
  {
    id: "inventory-list",
    component: lazy(() => import("./inventory-list/inventory-list")),
  },
  {
    id: "inventory-forecast",
    component: lazy(() => import("./inventory-forecast/inventory-forecast")),
  },
  {
    id: "simulation",
    component: lazy(() => import("./simulation/simulation")),
  },
  {
    id: "not-found",
    component: lazy(() => import("./not-found/not-found")),
  },
  {
    id: "playground",
    component: lazy(() => import("./playground/playground")),
  },
  {
    id: "tableau",
    component: lazy(() => import("./tableau/tableau")),
  },
  {
    id: "user-management",
    component: lazy(() => import("./user-management/user-management")),
  },
  {
    id: "options-view",
    component: lazy(() => import("./examples/options-view/options-view")),
  },
  {
    id: "facility-process-flow",
    component: lazy(
      () => import("./facility-process-flow/facility-process-flow"),
    ),
  },
  {
    id: "routable-view",
    component: lazy(() => import("./examples/routable-view/routable-view")),
  },
  {
    id: "static-view",
    component: lazy(() => import("./examples/static-view/static-view")),
  },
  {
    id: "fault-tracking",
    component: lazy(() => import("./fault-tracking/fault-tracking")),
  },
  {
    id: "iframe",
    component: lazy(() => import("./iframe")),
  },
  {
    id: "equipment-hierarchy",
    component: lazy(
      () => import("./ict-equipment-hierarchy/ict-equipment-hierarchy"),
    ),
  },
  {
    id: "system-availability",
    component: lazy(
      () => import("./ict-system-availability/ict-system-availability"),
    ),
  },
  {
    id: "configured-alerts",
    component: lazy(() => import("./configured-alerts/configured-alerts")),
  },
];
