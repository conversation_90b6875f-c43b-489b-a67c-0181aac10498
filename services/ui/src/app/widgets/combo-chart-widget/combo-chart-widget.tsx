import { useTheme } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type {
  TimeChartData,
  TimeChartSeriesData,
  TimeChartSeriesDataPoints,
} from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { ComboChartComponent } from "../../components/combo-chart/combo-chart-component";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { useConfigSetting } from "../../config/hooks/use-config";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./combo-chart-widget.module.css";
import type { ComboChartWidgetOptions } from "./types";
import { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { useDateRangeFormatter, useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";

interface ComboChartWidgetProps
  extends BaseWidgetProps<ComboChartWidgetOptions> {
  options: ComboChartWidgetOptions;
}

export const ComboChartWidget = ({
  options,
  filters,
}: ComboChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const defaultDateFormat = useDateRangeFormatter(filters.datePeriodRange);
  const dateFormat = options.dateFormat ?? defaultDateFormat;
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["chart", options.type, options.filters, filters.datePeriodRange],
    queryFn: () =>
      chartResolver.getChart(
        options.type as TimeChartType,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (!options.type) {
    return (
      <WidgetContainer title={options.title || "Combo Chart"} initializing />
    );
  }

  if (error) {
    return (
      <WidgetContainer title={options.title || "Combo Chart"} error={error} />
    );
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title || "Combo Chart"} loading />;
  }

  const chartTitle = options.title || "Combo Chart";

  if (
    !response?.success ||
    !response.data ||
    !("series" in response.data) ||
    !response.data.series ||
    response.data.series.length === 0 ||
    response.data.series.some(
      (series: TimeChartSeriesData) =>
        !series.data ||
        series.data.length === 0 ||
        series.data.some((point: TimeChartSeriesDataPoints) =>
          isNaN(point.value),
        ),
    )
  ) {
    return <WidgetContainer title={chartTitle} noData />;
  }

  const chartData = response.data as TimeChartData;

  // Determine the color to use
  let chartColor: string | undefined;

  if (options.colorId) {
    // If colorId is set, use it to get the color
    chartColor = getColorById(options.colorId, isDark);
  } else if (options.chartColor) {
    // For backward compatibility, use chartColor if provided
    chartColor = options.chartColor;
  } else {
    // If no color is selected (Auto mode), use the default color strategy
    chartColor = undefined;
  }

  return (
    <WidgetContainer title={chartTitle}>
      <div
        className={styles.container}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <ComboChartComponent
          chartData={chartData}
          chartStyle={options.chartStyle}
          color={chartColor}
          showAverageLine={options.showAverageLine}
          showTargetLine={options.showTargetLine}
          showLegend={options.showLegend}
          targetValue={options.targetValue}
          timezone={timezoneConfig?.value as string}
          dateFormat={dateFormat}
        />
      </div>
    </WidgetContainer>
  );
};

export default ComboChartWidget;
