import { Heading, Stack, useTheme } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import type { ApiFilters } from "../../api/api.types";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type {
  TimeChartData,
  TimeChartSeriesDataPoints,
} from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ComboChartComponent } from "../../components/combo-chart/combo-chart-component";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { useConfigSetting } from "../../config/hooks/use-config";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./kpi-chart-widget.module.css";
import type { KpiChartWidgetOptions } from "./types";
import { useDateRangeFormatter, useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";

interface KpiChartWidgetProps extends BaseWidgetProps<KpiChartWidgetOptions> {
  options: KpiChartWidgetOptions;
}

export const KpiChartWidget = ({ options, filters }: KpiChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const defaultDateFormat = useDateRangeFormatter(filters.datePeriodRange);
  const dateFormat = options.dateFormat ?? defaultDateFormat;
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "kpi-chart",
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      chartResolver.getChart(
        options.type as TimeChartType,
        mergedFilters as ApiFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  if (error) {
    return <WidgetContainer title={options.title} error={error} />;
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title} loading />;
  }

  const chartTitle = options.title;

  /*
   * Check the response is not sucessful
   * There is no data
   * The series is empty
   * Any data point contain NaN values
   */
  if (
    !response?.success ||
    !response.data ||
    !("series" in response.data) ||
    !response.data.series ||
    response.data.series.length === 0 ||
    !response.data.series[0]?.data ||
    response.data.series[0].data.length === 0 ||
    response.data.series[0].data.some((point: TimeChartSeriesDataPoints) =>
      isNaN(point.value),
    )
  ) {
    return <WidgetContainer title={chartTitle} noData />;
  }

  const chartData = response.data as TimeChartData;
  const chartSeriesData = chartData.series[0].data;
  const unitString = chartData.series[0].unit || "";
  const precision = options.precision ?? 2;
  const showUnit = options.showUnit || false;

  // Format unit for display
  const formatUnit = (value: number): string => {
    const formattedValue = value.toFixed(precision);
    if (!showUnit) return formattedValue;

    if (unitString.toLowerCase() === "percent") {
      return `${formattedValue}%`;
    }
    return `${formattedValue} ${unitString}`;
  };

  // Calculate KPI values
  const values = chartSeriesData.map((d) => d.value);
  const currentValue = values[values.length - 1];
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const averageValue = values.reduce((a, b) => a + b, 0) / values.length;
  const totalValue = values.reduce((a, b) => a + b, 0);

  // Create KPI display component
  const KpiDisplay = ({ value, label }: { value: number; label: string }) => (
    <div className={styles.kpiValueContainer}>
      <Heading className={styles.kpiValue}>{formatUnit(value)}</Heading>
      <p className={styles.kpiLabel}>{label}</p>
    </div>
  );

  // Determine the color to use
  let lineColor: string | undefined;

  if (options.colorId) {
    // If colorId is set, use it to get the color
    lineColor = getColorById(options.colorId, isDark);
  } else if (options.lineColor) {
    // For backward compatibility, use lineColor if provided
    lineColor = options.lineColor;
  } else {
    // If no color is selected (Auto mode), use the default color strategy
    lineColor = undefined;
  }

  return (
    <WidgetContainer title={chartTitle}>
      <div className={styles.container}>
        <Stack orientation="horizontal" gap={5}>
          {options.showCurrentKpi && (
            <KpiDisplay
              value={currentValue}
              label={options.valueLabel || "Current"}
            />
          )}
          {options.showMinKpi && <KpiDisplay value={minValue} label="Min" />}
          {options.showMaxKpi && <KpiDisplay value={maxValue} label="Max" />}
          {options.showAverageKpi && (
            <KpiDisplay value={averageValue} label="Average" />
          )}
          {options.showTotalKpi && (
            <KpiDisplay value={totalValue} label="Total" />
          )}
        </Stack>
        <ComboChartComponent
          showLegend={options.showLegend !== false}
          chartData={chartData}
          color={lineColor}
          chartStyle={options.chartStyle}
          showAverageLine={options.showAverageLine}
          showTargetLine={options.showTargetLine}
          targetValue={options.targetValue}
          timezone={timezoneConfig?.value as string}
          dateFormat={dateFormat}
        />
      </div>
    </WidgetContainer>
  );
};

export default KpiChartWidget;
