import { ArrowDown, <PERSON>U<PERSON> } from "@carbon/icons-react";
import { Heading, Stack } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { metricKpiResolver } from "../../api/resolver/kpi-resolver/kpi-resolver";
import type { MetricKpiType } from "../../api/resolver/kpi-resolver/kpi-types";
import { formatProgress, type progress } from "../../api/resolver/types";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { formatTimeValue } from "../../utils/date-util";
import type { BaseWidgetProps, WidgetFilters } from "../widget.types";
import styles from "./kpi-widget.module.css";
import { useTranslation } from "react-i18next";
import { useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";

export interface KpiWidgetOptions {
  type: MetricKpiType;
  title: string;
  precision: number;
  unit: boolean;
  filters?: WidgetFilters;
  showTarget?: boolean;
  targetValue?: number;
  targetDirection?: "above" | "below";
  targetDisplayStyle?: "arrow" | "color";
  acceptableThreshold?: number;
  warningThreshold?: number;
  showDelta?: boolean;
  [key: string]: unknown;
}

// Simple interface extension now works with proper generic types
interface KpiWidgetProps extends BaseWidgetProps<KpiWidgetOptions> {
  options: KpiWidgetOptions;
}

// Type for target comparison result
interface TargetComparison {
  color: string;
  status: "acceptable" | "warning" | "critical";
  isGood: boolean;
  difference: number;
  percentDifference: number;
  formattedDelta: string;
  ArrowIcon: typeof ArrowUp | typeof ArrowDown;
}

export const KpiWidget = ({ options, filters }: KpiWidgetProps) => {
  const mergedFilters = { ...options?.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);

  const { t } = useTranslation();

  const {
    data: metric,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "metric",
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      metricKpiResolver.getMetric(
        options.type as MetricKpiType,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  if (error) {
    return (
      <WidgetContainer
        title={options.title}
        error={error}
        errorMessage="Failed to load metric data. Please try again later."
      />
    );
  }

  if (isLoading || !metric) {
    return <WidgetContainer title={options.title} loading />;
  }

  const renderValue = (): string => {
    if (metric?.result === "200") {
      // if metric value is object
      if (typeof metric.value === "object") {
        return formatProgress(metric.value as progress, t);
      }

      // if is number
      if (Number.isFinite(metric.value)) {
        const numberValue = metric?.value as number;

        // Format time units nicely, but only if options.unit is true
        if (
          options.unit &&
          ["seconds", "minutes", "hours"].includes(metric.unit)
        ) {
          return formatTimeValue(numberValue, metric.unit);
        }

        // Handle percent as before
        if (metric.unit === "percent") {
          return `${numberValue.toFixed(options.precision)}${
            options.unit ? "%" : ""
          }`;
        }

        // Handle other numeric values
        // return `${numberValue.toFixed(options.precision)}${options.unit ? ` ${metric.unit}` : ""
        return `${numberValue.toFixed(options.precision)} ${options.unit ? t(metric.unit) : ""}`;
      }
      return `${metric.value} ${options.unit ? t(metric.unit) : ""}`;
      // return `${metric.value}${options.unit ? ` ${metric.unit}` : ""}`;
    }

    return "no data available";
  };

  const getTargetComparison = (): TargetComparison | null => {
    if (
      !options.showTarget ||
      !options.targetValue ||
      metric?.result !== "200" ||
      typeof metric.value !== "number"
    ) {
      return null;
    }

    const currentValue = metric.value as number;
    const targetValue = options.targetValue;
    const direction = options.targetDirection || "above";
    const acceptableThreshold = options.acceptableThreshold ?? 5;
    const warningThreshold = options.warningThreshold ?? 10;

    // Calculate absolute difference from target
    const difference = currentValue - targetValue;
    const absDifference = Math.abs(difference);
    // Keep percentDifference for the return value, but don't use it for threshold comparison
    const percentDifference = (absDifference / targetValue) * 100;

    // Determine if we're meeting the target based on direction
    let isGood = false;
    if (direction === "above") {
      isGood = currentValue >= targetValue;
    } else {
      isGood = currentValue <= targetValue;
    }

    // Determine status based on performance using absolute numerical difference
    let status: "acceptable" | "warning" | "critical" = "acceptable";
    let color = "gray"; // Default for acceptable range

    if (!isGood) {
      // We're not meeting the target
      if (absDifference <= acceptableThreshold) {
        status = "acceptable";
        color = "gray";
      } else if (absDifference <= warningThreshold) {
        status = "warning";
        color = "yellow";
      } else {
        status = "critical";
        color = "red";
      }
    } else {
      // We're meeting or exceeding the target
      status = "acceptable";
      color = "green";
    }

    // Format delta for display
    let formattedDelta = "";

    if (options.unit && ["seconds", "minutes", "hours"].includes(metric.unit)) {
      // For time units, format the difference using our time formatter, but only if options.unit is true
      formattedDelta = difference > 0 ? "+" : "";
      formattedDelta += formatTimeValue(Math.abs(difference), metric.unit);
    } else {
      // For other units or when options.unit is false, use the existing formatting
      formattedDelta = `${difference > 0 ? "+" : ""}${difference.toFixed(
        options.precision,
      )} ${options.unit && metric.unit ? ` ${metric.unit}` : ""}`;
    }

    // Determine which arrow to show
    const ArrowIcon = difference > 0 ? ArrowUp : ArrowDown;

    return {
      color,
      status,
      isGood,
      difference,
      percentDifference,
      formattedDelta,
      ArrowIcon,
    };
  };

  // Helper component for rendering arrow indicator
  const ArrowIndicator = ({ comparison }: { comparison: TargetComparison }) => (
    <>
      {comparison.isGood ? (
        <ArrowUp size={36} className={styles.greenIcon} />
      ) : (
        <comparison.ArrowIcon
          size={36}
          className={styles[`${comparison.color}Icon`]}
        />
      )}
    </>
  );

  const targetComparison = getTargetComparison();
  const displayStyle = options.targetDisplayStyle || "arrow";
  const showDelta = options.showDelta === true;

  if (metric?.result === "204") {
    return <WidgetContainer title={options.title} noData />;
  }
  return (
    <WidgetContainer title={options.title}>
      <div className={styles.valueContainer}>
        <Stack gap={4}>
          <div className={styles.valueGroup}>
            {/* Render value with color if color display style is selected */}
            {targetComparison && displayStyle === "color" ? (
              <Heading
                data-testid="kpi-value"
                className={`${
                  targetComparison.status !== "acceptable" ||
                  targetComparison.isGood
                    ? styles[`${targetComparison.color}Text`]
                    : ""
                }`}
              >
                {renderValue()}
              </Heading>
            ) : (
              <Heading data-testid="kpi-value">{renderValue()}</Heading>
            )}

            {/* Show arrow indicator if arrow display style is selected */}
            {targetComparison &&
              displayStyle === "arrow" &&
              (targetComparison.status !== "acceptable" ||
                targetComparison.isGood) && (
                <ArrowIndicator comparison={targetComparison} />
              )}
          </div>

          {/* Show delta value below the main value and arrow */}
          {targetComparison && showDelta && (
            <p
              className={`${styles.deltaValue} ${styles[`${targetComparison.color}Text`]}`}
            >
              {targetComparison.formattedDelta}
            </p>
          )}
        </Stack>
      </div>
    </WidgetContainer>
  );
};

export default KpiWidget;
