.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tabs {
  margin: 0;
  padding: 0;
}

.widgetGrid {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 20px;
  margin-top: 1rem;
}

.kpiRow {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.chartRow {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  flex-grow: 1;
}

// Options component styles
.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  width: 100%;
  max-width: 100%;
}

.areasList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.areaItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--cds-layer-selected);
  border-color: var(--cds-border-subtle);
  border-radius: 4px;
}

.addArea {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;

  :global(.cds--text-input-wrapper) {
    width: 100%;
  }

  button {
    align-self: flex-start;
  }
}
