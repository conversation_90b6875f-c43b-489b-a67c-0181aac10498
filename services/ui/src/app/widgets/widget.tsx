import { OverflowMenuVertical, Warning } from "@carbon/icons-react";
import {
  Button,
  Loading,
  OverflowMenu,
  OverflowMenuItem,
  Tooltip,
} from "@carbon/react";
import { Suspense, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRoles } from "../auth/hooks/use-roles";
import { OptionsContainer } from "../components/options/options-container/options-container";
import { WidgetContainer } from "../components/widget-container/widget-container";
import { DatePeriod } from "../types";
import { Logger } from "../utils";
import { getWidget } from "./widget-registry";
import styles from "./widget.module.css";
import type { WidgetFilters } from "./widget.types";
import { metricAndChartDefinitions } from "../api/resolver";
import DebugInfoTooltip from "../components/debug-info/debug-info-tooltip";

const logger = new Logger("Widget");

export interface WidgetProps {
  /**
   * Unique ID for this widget
   */
  id: string;
  /**
   * The type of widget
   */
  type: string;
  filters?: WidgetFilters;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: Record<string, any>;
  onShowAside?: (content: React.ReactNode) => void;
  onUpdateOptions?: (id: string, newOptions: Record<string, unknown>) => void;
  onDeleteWidget?: (id: string) => void;
  readonly?: boolean;
}

export const Widget = ({
  id,
  type,
  onShowAside,
  onUpdateOptions,
  onDeleteWidget,
  readonly,
  filters,
  options,
}: WidgetProps) => {
  const { hasConfiguratorAccess, isInternalUser } = useRoles();
  if (!options) {
    return (
      <div className={styles.widgetWrapper}>
        <div className={styles.widgetContent}>
          <div />
        </div>
      </div>
    );
  }

  const { t } = useTranslation();
  if (options.title && options.title !== "") {
    options.title = t(options.title);
  }

  const widgetDef = getWidget(type);
  if (!widgetDef) {
    return (
      <WidgetContainer
        error={new Error("Unknown widget type")}
        title="Unknown widget type"
        errorMessage="The widget type is not valid. Please contact support."
      >
        <Button
          onMouseDown={(e) => e.stopPropagation()}
          onTouchStart={(e) => e.stopPropagation()}
          kind="primary"
          onClick={() => onDeleteWidget?.(id)}
          size="sm"
        >
          Delete widget
        </Button>
      </WidgetContainer>
    );
  }

  const { component: WidgetComponent, optionsComponent: OptionsComponent } =
    widgetDef;

  const handleOptionsChange = (newOptions: Record<string, unknown>) => {
    onUpdateOptions?.(id, newOptions);
  };

  const OptionsForm = () => {
    const [localOptions, setLocalOptions] = useState(options);

    const handleLocalOptionsChange = (newOptions: unknown) => {
      const newOptionsTyped = newOptions as Record<string, unknown>;
      setLocalOptions(newOptionsTyped);
      handleOptionsChange(newOptionsTyped);
    };

    const handleSave = () => {
      handleOptionsChange(localOptions);
      onShowAside?.(null);
    };

    const handleCancel = () => {
      handleOptionsChange(options);
      onShowAside?.(null);
    };

    return (
      <OptionsContainer onClose={handleCancel} onSave={handleSave}>
        <Suspense
          fallback={<div>{t("widget.loadingOptions", "Loading options")}</div>}
        >
          {OptionsComponent && (
            <OptionsComponent
              options={localOptions}
              onChange={handleLocalOptionsChange}
            />
          )}
        </Suspense>
      </OptionsContainer>
    );
  };

  const handleOnDeleteWidget = (id: string) => {
    onDeleteWidget?.(id);
  };

  const uniqueId = options.type ? `${type}-${options.type}` : `${type}`;
  logger.info(`widget::uniqueId`, uniqueId);

  // set widget level date range override if it exists
  let updatedFilters = filters;
  if (options.hasDateRangeOverride && options.datePeriod) {
    updatedFilters = {
      ...filters,
      datePeriodRange: options.datePeriod.id,
    };
  }

  return (
    <div data-testid={uniqueId} className={styles.widgetWrapper}>
      <div className={styles.widgetHeader}>
        {options.hasDateRangeOverride && isInternalUser && (
          <Tooltip
            label={"This widget has a date range override set."}
            align="top-right"
          >
            <Warning />
          </Tooltip>
        )}
        {hasConfiguratorAccess && !readonly && (
          <button
            type="button"
            style={{ background: "none", border: "none", padding: 0 }}
            onMouseDown={(e: React.MouseEvent<HTMLButtonElement>) =>
              e.stopPropagation()
            }
            onTouchStart={(e: React.TouchEvent<HTMLButtonElement>) =>
              e.stopPropagation()
            }
          >
            <DebugInfoTooltip
              endpointKey={
                metricAndChartDefinitions.find((m) => m.id === options.type)
                  ?.endpoint ?? ""
              }
            />
            <OverflowMenu
              renderIcon={OverflowMenuVertical}
              iconDescription="Widget options"
              flipped
              align="left"
            >
              <OverflowMenuItem
                itemText={t("widget.settings", "Settings")}
                disabled={!OptionsComponent}
                onClick={(e) => {
                  e.stopPropagation();
                  onShowAside?.(<OptionsForm />);
                }}
              />
              <OverflowMenuItem
                itemText={t("widget.delete", "Delete")}
                onClick={(e) => {
                  e.stopPropagation();
                  handleOnDeleteWidget(id);
                }}
                isDelete
              />
            </OverflowMenu>
          </button>
        )}
      </div>
      <div className={styles.widgetContent}>
        <Suspense
          fallback={
            <Loading
              withOverlay
              description={t("widget.loadingWidget", "Loading widget")}
            />
          }
        >
          <WidgetComponent
            id={id}
            type={type}
            options={options}
            filters={updatedFilters ?? { datePeriodRange: DatePeriod.today }}
          />
        </Suspense>
      </div>
    </div>
  );
};
